//
// Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/18.
// 多盒子接力测试 - 分时序列处理方案（简化版本）
//
#include <iostream>
#include <string>
#include <vector>
#include <cstdio>
#include <fstream>
#include <cmath>
#include <algorithm>
#include <chrono>
#include <queue>
#include "zlogger.h"
#include "algorithm_header.h"
#include "utils_debug.h"

ZLOGGER_HEADER_DEFINE("ALG_MULTI_BOX_RELAY_TEST_V2")

#define MAX_FRAME 9999
/*
 * @Name: RelayFrameData
 * @Description: 接力帧数据结构，用于在两个处理阶段间传递数据
 *
 * @Edit History
 * Date: 2024/12/18
 * Time: 14:00
 * Author: JiaTao
 * Content: 创建接力帧数据结构
 */
struct RelayFrameData {
    long long timestamp;                    // 时间戳
    MultiBoxTargets original_data;          // 原始输入数据
    OutputTargets box1_output;              // 第一个盒子的输出结果
    bool has_box1_output;                   // 是否有第一个盒子的输出

    RelayFrameData() : timestamp(0), has_box1_output(false) {
        memset(&original_data, 0, sizeof(MultiBoxTargets));
        memset(&box1_output, 0, sizeof(OutputTargets));
    }
};

/*
 * @Name: CompactOutputTargets
 * @Description: 紧凑的输出目标结构，只存储实际需要的目标数据，减少内存占用
 *
 * @Edit History
 * Date: 2024/12/18
 * Time: 16:00
 * Author: JiaTao
 * Content: 创建紧凑的输出目标结构
 */
struct CompactOutputTargets {
    unsigned int target_cnt;                        // 输出目标数量
    std::vector<OutputTarget> fusion_targets;       // 动态数组，只存储实际的目标数据

    CompactOutputTargets() : target_cnt(0) {}

    // 从OutputTargets转换
    void fromOutputTargets(const OutputTargets* src) {
        if (!src) {
            target_cnt = 0;
            fusion_targets.clear();
            return;
        }

        target_cnt = src->target_cnt;
        fusion_targets.resize(target_cnt);

        // 只复制实际需要的目标数据
        for (unsigned int i = 0; i < target_cnt; i++) {
            fusion_targets[i] = src->fusion_targets[i];
        }
    }

    // 转换为OutputTargets
    void toOutputTargets(OutputTargets* dst) const {
        if (!dst) return;

        memset(dst, 0, sizeof(OutputTargets));
        dst->target_cnt = target_cnt;

        // 复制目标数据，确保不超出数组边界
        unsigned int max_targets = sizeof(dst->fusion_targets) / sizeof(OutputTarget);
        unsigned int copy_count = std::min(target_cnt, max_targets);

        for (unsigned int i = 0; i < copy_count; i++) {
            dst->fusion_targets[i] = fusion_targets[i];
        }
    }
};

// 全局变量
std::vector<CompactOutputTargets> g_box1_outputs;   // 第一个盒子的输出结果（按帧索引，紧凑存储）
std::vector<bool> g_box1_has_output;                // 每帧是否有输出结果
size_t g_current_frame_index = 0;                   // 当前处理的帧索引

/*
 * @Name: relay_output_callback
 * @Description: 接力输出回调函数
 *
 * @Input
 * callbackType: 回调类型
 * channel_id: 通道ID
 * output_targets: 输出目标
 * output_targets_next: 传递给下一个盒子的目标
 * user_ptr: 用户指针
 *
 * @Output
 * 0: 成功
 *
 * @Edit History
 * Date: 2024/12/18
 * Time: 14:00
 * Author: JiaTao
 * Content: 接力输出回调函数
 */
int relay_output_callback(AlgCallbackType callbackType, int channel_id,
                         OutputTargets *output_targets, OutputTargets *output_targets_next,
                         void *user_ptr) {
    if (callbackType == AlgCallbackType::CALLBACK_TYPE_TARGETS) {
        ZINFO("接力回调 - 通道ID:%d, 输出目标数量:%u, 传递目标数量:%u \n",
              channel_id,
              output_targets ? output_targets->target_cnt : 0,
              output_targets_next ? output_targets_next->target_cnt : 0);
        if (output_targets_next && output_targets_next->target_cnt > 0) {
            // 使用紧凑数据结构，只保存实际需要的目标数据，大幅减少内存占用
            CompactOutputTargets compact_output;
            compact_output.fromOutputTargets(output_targets_next);

            // 确保容器大小足够
            if (g_current_frame_index >= g_box1_outputs.size()) {
                g_box1_outputs.resize(g_current_frame_index + 1);
                g_box1_has_output.resize(g_current_frame_index + 1, false);
            }

            // 保存紧凑的输出数据
            g_box1_outputs[g_current_frame_index] = std::move(compact_output);
            g_box1_has_output[g_current_frame_index] = true;

            ZINFO("保存帧[%zu]的 %u 个目标用于传递给下一个盒子（紧凑存储，内存占用减少约 %.1f%%）",
                  g_current_frame_index,
                  g_box1_outputs[g_current_frame_index].target_cnt,
                  (1.0 - (double)g_box1_outputs[g_current_frame_index].target_cnt / (512.0 * 4.0)) * 100.0);
        }

        // 打印部分目标信息用于调试
        if (output_targets && output_targets->target_cnt > 0) {
            unsigned int print_count = std::min(output_targets->target_cnt, 3u);
            for (unsigned int i = 0; i < print_count; i++) {
                const OutputTarget& ot = output_targets->fusion_targets[i];
                const TecuTarget& target = ot.splice_target;
                ZINFO("目标[%d]: 拼接ID=%u, 设备ID=%u, 目标ID=%u, 类型=%d, 坐标=(%.2f,%.2f), 速度=%.2f \n ",
                      i, ot.splice_id, ot.grp_id, target.id,
                      (int)target.target_type, target.x, target.y, target.speed);
            }
        }
    }
    return 0;
}

/* 事件类型 */
typedef struct
{
    unsigned int trigger_count;         //当前目标触发的事件计数
    bool b_illegal_change;              //非法变道
    bool b_over_speed;                  //超速
    bool b_below_speed;                 //低速
    bool b_retrograde;                  //逆行
    bool b_illegal_parking;             //非法停车
    bool b_intrusion;                   //入侵
    bool b_accident;                    //事故
    bool b_drag_racing;                 //飙车
    bool b_dist_no_maintain;            //未保持车距
    bool b_abnormal_parking;            //异常停车
    bool b_occ_emer;                    //占用应急车道
    bool b_snake_change;                //蛇形变道
} EventState;

typedef struct
{
    char device_ip[16];                          //设备IP
    unsigned int id;  		                     //目标id     (1 ~ MAX_OBJECT_NUM)
    unsigned int source_type;                    //目标的融合类型
    TargetType target_type;		                 //目标类型
    float      tt_score;                         //目标分数

    float x; 			                         //x轴坐标
    float y; 			                         //y轴坐标
    float angle;                                 //目标图标角度 0-360°
    float speed;			                     //速度 km/h
    double longitude;                            //目标经度 小数点精确到8位
    double latitude;                             //目标维度 小数点精确到8位

    char number_plate[64];                       //车牌文字
    float np_score;                              //车牌文字分数
    PlateColor plate_color;                      //车牌颜色
    float      pc_score;                         //车牌颜色分数
    VehicleType vehicle_type;                    //车辆类型
    float       vt_score;                        //车辆类型分数
    VehicleColor vehicle_color;                  //车身颜色
    float       vc_score;                        //车身颜色分数
    EventState event_state;                      //目标的事件触发状态

    double direction;                            //方向角 0-360° pengge edit
    double distance;                             //距离  pengge edit

    float radar_coord_dist;                      //目标到雷达坐标系原点的距离
    float radar_coord_angle;                     //目标和雷达坐标系原点的连线与雷达法线的夹角
    long long timestamp_ms;                      //目标的时间戳
}   Target;

/*
 * @Name: change_target2tecu_target
 * @Description: 旧版本Target转换为TecuTarget
 *
 * @Input
 * target_tmp: 旧版本Target数组
 * tecu_target: TecuTarget数组
 * target_cnt: 目标数量
 * grp_id: 设备组ID
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/12/18
 * Time: 14:00
 * Author: JiaTao
 * Content: 数据结构转换函数
 */
void change_target2tecu_target(Target *target_tmp, TecuTarget *tecu_target, int target_cnt, unsigned int grp_id) {
    for(int i = 0; i < target_cnt; i++) {
        tecu_target[i].grp_id = grp_id;
        tecu_target[i].stream_id = 0;

        tecu_target[i].id = target_tmp[i].id;
        tecu_target[i].source_type = target_tmp[i].source_type;
        tecu_target[i].target_type = target_tmp[i].target_type;
        tecu_target[i].tt_score = target_tmp[i].tt_score;

        tecu_target[i].x = target_tmp[i].x;
        tecu_target[i].y = target_tmp[i].y;
        tecu_target[i].angle = target_tmp[i].angle;
        tecu_target[i].speed = target_tmp[i].speed;
        tecu_target[i].longitude = target_tmp[i].longitude;
        tecu_target[i].latitude = target_tmp[i].latitude;

        strcpy(tecu_target[i].number_plate, target_tmp[i].number_plate);
        tecu_target[i].np_score = target_tmp[i].np_score;
        tecu_target[i].plate_color = target_tmp[i].plate_color;
        tecu_target[i].pc_score = target_tmp[i].pc_score;
        tecu_target[i].vehicle_type = target_tmp[i].vehicle_type;
        tecu_target[i].vt_score = target_tmp[i].vt_score;
        tecu_target[i].vehicle_color = target_tmp[i].vehicle_color;
        tecu_target[i].vc_score = target_tmp[i].vc_score;

        tecu_target[i].direction = target_tmp[i].direction;
        tecu_target[i].distance = target_tmp[i].distance;

        tecu_target[i].radar_coord_dist = target_tmp[i].radar_coord_dist;
        tecu_target[i].radar_coord_angle = target_tmp[i].radar_coord_angle;
        tecu_target[i].timestamp_ms = target_tmp[i].timestamp_ms;
    }
}

/*
 * @Name: filter_devices_for_box
 * @Description: 为指定盒子过滤设备数据
 *
 * @Input
 * original_data: 原始数据
 * box_id: 盒子ID（1或2）
 * frame_index: 帧索引（用于获取对应的box1输出）
 * filtered_data: 过滤后的数据
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/12/18
 * Time: 14:00
 * Author: JiaTao
 * Content: 为指定盒子过滤设备数据，支持按帧索引获取box1输出
 */
void filter_devices_for_box(const MultiBoxTargets& original_data,
                           int box_id,
                           size_t frame_index,
                           MultiBoxTargets& filtered_data) {
    memset(&filtered_data, 0, sizeof(MultiBoxTargets));

    // 定义每个盒子处理的设备ID
    std::vector<unsigned int> target_devices;
    if (box_id == 1) {
        target_devices = {1, 2};  // 第一个盒子处理设备1、2
    } else if (box_id == 2) {
        target_devices = {3, 4};  // 第二个盒子处理设备3、4
    }

    // 过滤多设备目标数据
    unsigned int filtered_device_cnt = 0;
    for (unsigned int i = 0; i < original_data.multi_device_targets.device_cnt; i++) {
        const InputTargets& device = original_data.multi_device_targets.device_input[i];

        // 检查是否是目标设备
        bool should_process = false;
        for (unsigned int target_id : target_devices) {
            if (device.grp_id == target_id) {
                should_process = true;
                break;
            }
        }

        if (should_process) {
            memcpy(&filtered_data.multi_device_targets.device_input[filtered_device_cnt],
                   &device, sizeof(InputTargets));
            filtered_device_cnt++;
            ZINFO("Box[%d] 处理设备ID=%u的数据，目标数量=%u",
                  box_id, device.grp_id, device.target_cnt);
        }
    }
    filtered_data.multi_device_targets.device_cnt = filtered_device_cnt;

    // 如果是第二个盒子，需要添加第一个盒子的输出作为last_output_target
    if (box_id == 2 && frame_index < g_box1_has_output.size() && g_box1_has_output[frame_index]) {
        // 从紧凑数据结构转换为OutputTargets
        g_box1_outputs[frame_index].toOutputTargets(&filtered_data.last_output_target);
        ZINFO("Box[%d] 接收到来自Box[1]帧[%zu]的 %u 个接力目标（从紧凑存储恢复）",
              box_id, frame_index, g_box1_outputs[frame_index].target_cnt);
    } else if (box_id == 2) {
        ZINFO("Box[%d] 帧[%zu]没有来自Box[1]的接力目标", box_id, frame_index);
    }
}

/*
 * @Name: initialize_algorithm
 * @Description: 初始化算法实例
 *
 * @Input
 * config_path: 配置文件路径
 * device_list: 设备列表
 * road_config_vec: 道路配置
 *
 * @Output
 * channel_id: 成功返回通道ID
 * -1: 失败
 *
 * @Edit History
 * Date: 2024/12/18
 * Time: 14:00
 * Author: JiaTao
 * Content: 初始化算法实例
 */
int initialize_algorithm(const std::string& config_path,
                        DeviceList& device_list,
                        std::vector<RoadConfigAlg>& road_config_vec,
                        int edgebox_id) {
    char version[128];
    int ret = tecu_algorithm_init_with_config(version, config_path.c_str());
    if (ret != 0) {
        ZERROR("算法初始化失败! config_path:%s", config_path.c_str());
        return -1;
    }

    int channel_id = tecu_algorithm_open_channel(relay_output_callback, nullptr);
    if (channel_id < 0) {
        ZERROR("创建通道失败!");
        tecu_algorithm_deinit();
        return -1;
    }

    // 设置道路配置
    ZINFO("开始设置道路配置，配置数量: %zu", road_config_vec.size());
    if (road_config_vec.empty()) {
        ZERROR("道路配置为空!");
        tecu_algorithm_close_channel(channel_id);
        tecu_algorithm_deinit();
        return -1;
    }

    // 检查第一个道路配置的有效性
    const RoadConfigAlg& first_config = road_config_vec[0];

    ret = tecu_algorithm_set_param(channel_id,
                                  TecuAlgorithmSetType::TECU_ALG_TYPE_ROAD_CONFIG,
                                  &road_config_vec,
                                  road_config_vec.size());
    if (ret != 0) {
        ZERROR("设置道路配置失败! 错误码: %d", ret);
        tecu_algorithm_close_channel(channel_id);
        tecu_algorithm_deinit();
        return -1;
    }
    ZINFO("道路配置设置成功");

    // 设置基准设备组
    ret = tecu_algorithm_set_param(channel_id,
                                  TecuAlgorithmSetType::TECU_ALG_TYPE_BASE_GRP,
                                  &(device_list.device_list),
                                  device_list.device_cnt);
    if (ret != 0) {
        ZERROR("设置基准设备组失败!");
        tecu_algorithm_close_channel(channel_id);
        tecu_algorithm_deinit();
        return -1;
    }

    // 设置标定HDM
    ret = tecu_algorithm_set_param(channel_id,
                                  TecuAlgorithmSetType::TECU_ALG_TYPE_CALIB_HDM,
                                  &(device_list.device_list),
                                  device_list.device_cnt);
    if (ret != 0) {
        ZERROR("设置标定HDM失败!");
        tecu_algorithm_close_channel(channel_id);
        tecu_algorithm_deinit();
        return -1;
    }
    ret = tecu_algorithm_set_param(channel_id,
                              TecuAlgorithmSetType::TECU_ALG_TYPE_EDGEBOX_DEVICE,
                              &edgebox_id,
                              0);
    if(ret != 0){
        ZERROR("tecu_algorithm_set_param failure! \n");
    }else{
        ZINFO("tecu_algorithm_set_param success! \n");
    }
    // 启动算法
    ret = tecu_algorithm_start(channel_id);
    if (ret != 0) {
        ZERROR("启动算法失败!");
        tecu_algorithm_close_channel(channel_id);
        tecu_algorithm_deinit();
        return -1;
    }

    ZINFO("算法初始化成功! channel_id:%d config_path:%s version:%s",
          channel_id, config_path.c_str(), version);
    return channel_id;
}

/*
 * @Name: cleanup_algorithm
 * @Description: 清理算法实例
 *
 * @Input
 * channel_id: 通道ID
 *
 * @Output
 * 0: 成功
 *
 * @Edit History
 * Date: 2024/12/18
 * Time: 14:00
 * Author: JiaTao
 * Content: 清理算法实例
 */
int cleanup_algorithm(int channel_id) {
    if (channel_id >= 0) {
        tecu_algorithm_close_channel(channel_id);
    }
    tecu_algorithm_deinit();
    ZINFO("算法实例清理完成");
    return 0;
}

// 函数声明
int process_box1_data(const std::string& data_file_path,
                     const std::string& time_file_path,
                     int channel_id);

int process_box2_data(const std::string& data_file_path,
                     const std::string& time_file_path,
                     int channel_id);

/*
 * @Name: main
 * @Description: 多盒子接力测试主函数（分时序列处理方案）
 *
 * @Input
 * argc: 命令行参数个数
 * argv: 命令行参数数组
 *
 * @Output
 * 0: 成功
 * -1: 失败
 *
 * @Edit History
 * Date: 2024/12/18
 * Time: 14:00
 * Author: JiaTao
 * Content: 多盒子接力测试主函数
 */
int main(int argc, char *argv[])
{
    novasky_zlog_init("");
    ZINFO("多盒子接力测试开始（分时序列处理方案）");
    ZINFO("<<<<<<<<<<<<<<<<<<<<<-------------------------MULTI BOX RELAY TEST V2----------------------->>>>>>>>>>>>>>>>>>>>> \n");

    int ret = 0;

    // 检查命令行参数
    if (argc != 3) {
        ZERROR("参数错误! argc:%d \n", argc);
        ZERROR("用法: %s data_dir_path input_data_timems \n", argv[0]);
        ZERROR("示例: %s E:/data/channel_id_0 2024_05_23_15_32_49 \n", argv[0]);
        return -1;
    }

    std::string data_dir_path = argv[1];
    std::string input_data_timems = argv[2];

    // 构建文件路径
    std::string input_data_file_path = data_dir_path + "/" + input_data_timems + ".dat";
    std::string input_data_timems_path = data_dir_path + "/" + input_data_timems + ".txt";
    std::string devicelist_file_path = data_dir_path + "/" + "devicelist.dat";
    std::string lane_config_path = "tecu_r1000_config.json";

    ZINFO("数据文件路径: %s", input_data_file_path.c_str());
    ZINFO("时间戳文件路径: %s", input_data_timems_path.c_str());
    ZINFO("设备列表文件路径: %s", devicelist_file_path.c_str());
    ZINFO("车道配置文件路径: %s", lane_config_path.c_str());

    // 第一阶段：处理第一个盒子（设备1、2）
    ZINFO("========== 第一阶段：处理第一个盒子（设备1、2）==========");
    // 读取配置文件
    std::vector<RoadConfigAlg> road_config_vec;
    ZINFO("开始读取道路配置文件: %s", lane_config_path.c_str());
    ret = read_road_lanes_config(lane_config_path, road_config_vec);
    if (ret != 0) {
        ZERROR("读取道路配置失败!");
        return -1;
    }
    ZINFO("道路配置读取成功，配置数量: %zu", road_config_vec.size());

    DeviceList device_list;
    ZINFO("开始读取设备列表文件: %s", devicelist_file_path.c_str());
    ret = read_device_to_list(devicelist_file_path, device_list);
    if (ret != 0) {
        ZERROR("读取设备列表失败!");
        return -1;
    }
    ZINFO("设备列表读取成功，设备数量: %u", device_list.device_cnt);

    ZINFO("配置文件读取完成，开始分时序列处理...");
    // 仅保留id为1和2的device
    DeviceList device_list_box1;
    device_list_box1.device_cnt = 0;
    for (int i = 0; i < device_list.device_cnt; i++) {
        if (device_list.device_list[i].grp_id == 1 || device_list.device_list[i].grp_id == 2) {
            device_list_box1.device_list[device_list_box1.device_cnt++] = device_list.device_list[i];
        }
    }
    ret = read_devices_config(device_list_box1, road_config_vec);
    if (ret != 0) {
        ZERROR("读取设备配置失败!");
        return -1;
    }
    ZINFO("设备配置读取成功");

    int box1_channel_id = initialize_algorithm("algorithm/config_algorithm_box1.txt",
                                              device_list_box1, road_config_vec, 1);
    if (box1_channel_id < 0) {
        ZERROR("第一个盒子初始化失败!");
        return -1;
    }

    // 读取并处理数据 - 第一阶段
    ret = process_box1_data(input_data_file_path, input_data_timems_path, box1_channel_id);
    if (ret != 0) {
        ZERROR("第一个盒子数据处理失败!");
        cleanup_algorithm(box1_channel_id);
        return -1;
    }

    // 清理第一个盒子
    cleanup_algorithm(box1_channel_id);

    // 统计保存的接力目标总数和内存优化效果
    size_t total_relay_targets = 0;
    size_t frames_with_output = 0;
    size_t total_memory_saved = 0;
    for (size_t i = 0; i < g_box1_has_output.size(); i++) {
        if (g_box1_has_output[i]) {
            total_relay_targets += g_box1_outputs[i].target_cnt;
            frames_with_output++;

            // 计算内存节省：原始大小 - 实际使用大小
            size_t original_size = sizeof(OutputTargets);
            size_t compact_size = sizeof(unsigned int) + g_box1_outputs[i].target_cnt * sizeof(OutputTarget);
            total_memory_saved += (original_size - compact_size);
        }
    }
    ZINFO("第一个盒子处理完成，共 %zu 帧有输出，总共保存了 %zu 个接力目标",
          frames_with_output, total_relay_targets);
    ZINFO("内存优化效果：节省约 %zu KB 内存（紧凑存储 vs 静态数组）",
          total_memory_saved / 1024);

    // 第二阶段：处理第二个盒子（设备3、4 + 第一个盒子的输出）
    ZINFO("========== 第二阶段：处理第二个盒子（设备3、4 + 接力目标）==========");
  // 读取配置文件
    ZINFO("开始读取道路配置文件: %s", lane_config_path.c_str());
    ret = read_road_lanes_config(lane_config_path, road_config_vec);
    if (ret != 0) {
        ZERROR("读取道路配置失败!");
        return -1;
    }
    ZINFO("道路配置读取成功，配置数量: %zu", road_config_vec.size());

    ZINFO("开始读取设备列表文件: %s", devicelist_file_path.c_str());
    ret = read_device_to_list(devicelist_file_path, device_list);
    if (ret != 0) {
        ZERROR("读取设备列表失败!");
        return -1;
    }
    ZINFO("设备列表读取成功，设备数量: %u", device_list.device_cnt);

    ZINFO("配置文件读取完成，开始分时序列处理...");
    // 仅保留id为1和2的device
    DeviceList device_list_box2;
    device_list_box2.device_cnt = 0;
    for (int i = 0; i < device_list.device_cnt; i++) {
        if (device_list.device_list[i].grp_id == 3 || device_list.device_list[i].grp_id == 4) {
            device_list_box2.device_list[device_list_box2.device_cnt++] = device_list.device_list[i];
        }
    }
    ret = read_devices_config(device_list_box2, road_config_vec);
    if (ret != 0) {
        ZERROR("读取设备配置失败!");
        return -1;
    }
    ZINFO("设备配置读取成功");
    int box2_channel_id = initialize_algorithm("algorithm/config_algorithm_box2.txt",
                                              device_list_box2, road_config_vec, 2);
    if (box2_channel_id < 0) {
        ZERROR("第二个盒子初始化失败!");
        return -1;
    }

    // 读取并处理数据 - 第二阶段
    ret = process_box2_data(input_data_file_path, input_data_timems_path, box2_channel_id);
    if (ret != 0) {
        ZERROR("第二个盒子数据处理失败!");
        cleanup_algorithm(box2_channel_id);
        return -1;
    }

    // 清理第二个盒子
    cleanup_algorithm(box2_channel_id);

    ZINFO("多盒子接力测试完成!");
    ZINFO("验证要点:");
    ZINFO("1. 第一个盒子处理了设备1、2的数据");
    ZINFO("2. 第二个盒子接收了第一个盒子的输出作为接力目标");
    ZINFO("3. 第二个盒子处理了设备3、4的数据并进行了融合");
    ZINFO("4. 实现了目标ID的连续性和轨迹拼接");

    return 0;
}

/*
 * @Name: process_box1_data
 * @Description: 处理第一个盒子的数据（设备1、2）
 *
 * @Input
 * data_file_path: 数据文件路径
 * time_file_path: 时间戳文件路径
 * channel_id: 通道ID
 *
 * @Output
 * 0: 成功
 * -1: 失败
 *
 * @Edit History
 * Date: 2024/12/18
 * Time: 14:00
 * Author: JiaTao
 * Content: 处理第一个盒子的数据
 */
int process_box1_data(const std::string& data_file_path,
                     const std::string& time_file_path,
                     int channel_id) {
    FILE* data_file = fopen(data_file_path.c_str(), "rb");
    if (nullptr == data_file) {
        ZERROR("无法打开数据文件: %s", data_file_path.c_str());
        return -1;
    }

    FILE* time_file = fopen(time_file_path.c_str(), "r");
    if (nullptr == time_file) {
        ZERROR("无法打开时间戳文件: %s", time_file_path.c_str());
        fclose(data_file);
        return -1;
    }

    char time_buffer[128];
    size_t frame_count = 0;
    long long current_timestamp = 0;
    MultiBoxTargets box_targets;

    // 重置全局输出状态
    g_box1_outputs.clear();
    g_box1_has_output.clear();
    g_current_frame_index = 0;

    ZINFO("开始处理第一个盒子的数据...");

    // 逐帧读取和处理
    while (true) {
        // 读取时间戳
        if (fgets(time_buffer, sizeof(time_buffer), time_file) == nullptr) {
            break;  // 时间戳文件读取完毕
        }
        current_timestamp = atoll(time_buffer);

        // 清零当前帧数据
        memset(&box_targets, 0, sizeof(MultiBoxTargets));

        // 读取设备数量
        if (fread(&box_targets.multi_device_targets.device_cnt, sizeof(unsigned int), 1, data_file) != 1) {
            break;  // 数据文件读取完毕
        }

        // 验证设备数量
        if (box_targets.multi_device_targets.device_cnt > (DEV_GRP_NUM_MAX * 2)) {
            ZERROR("非法的设备数量: %u，帧数: %zu", box_targets.multi_device_targets.device_cnt, frame_count);
            break;
        }

        // 读取每个设备的数据
        bool read_error = false;
        for (unsigned int i = 0; i < box_targets.multi_device_targets.device_cnt; i++) {
            InputTargets &device = box_targets.multi_device_targets.device_input[i];

            // 读取设备信息
            if (fread(&device.grp_id, sizeof(unsigned int), 1, data_file) != 1 ||
                fread(&device.grp_type, sizeof(unsigned int), 1, data_file) != 1 ||
                fread(&device.target_cnt, sizeof(unsigned int), 1, data_file) != 1 ||
                fread(&device.timestamp_ms, sizeof(long long), 1, data_file) != 1) {
                read_error = true;
                break;
            }

            // 验证目标数量
            if (device.target_cnt > MAX_OBJECT_NUM) {
                ZERROR("非法的目标数量: %u，设备ID: %u，帧数: %zu",
                       device.target_cnt, device.grp_id, frame_count);
                read_error = true;
                break;
            }

            // 读取目标数据
            Target target_tmp[MAX_OBJECT_NUM];
            if (device.target_cnt > 0) {
                if (fread(target_tmp, sizeof(Target), device.target_cnt, data_file) != device.target_cnt) {
                    read_error = true;
                    break;
                }
            }

            // 转换目标数据格式
            change_target2tecu_target(target_tmp, device.targets.target_devices, device.target_cnt, device.grp_id);
        }

        // 跳过融合目标数据（原始文件中的数据，我们不需要）
        OutputTargets output_targets_;
        if (fread(&output_targets_.target_cnt, sizeof(unsigned int), 1, data_file) != 1) {
            read_error = true;
            break;
        }

        // 跳过融合目标数据
        for (unsigned int i = 0; i < output_targets_.target_cnt; i++) {
            Target target_tmp2;
            if (fread(&output_targets_.fusion_targets[i].splice_id, sizeof(unsigned int), 1, data_file) != 1 ||
                fread(&output_targets_.fusion_targets[i].grp_id, sizeof(unsigned int), 1, data_file) != 1 ||
                fread(&output_targets_.fusion_targets[i].timestamp_ms, sizeof(long long), 1, data_file) != 1 ||
                fread(&output_targets_.fusion_targets[i].splicing_x, sizeof(float), 1, data_file) != 1 ||
                fread(&output_targets_.fusion_targets[i].splicing_y, sizeof(float), 1, data_file) != 1 ||
                fread(&target_tmp2, sizeof(Target), 1, data_file) != 1) {
                read_error = true;
                break;
            }
        }

        if (read_error) {
            ZERROR("读取第 %zu 帧数据失败", frame_count);
            break;
        }

        // 过滤第一个盒子的数据（设备1、2）
        MultiBoxTargets box1_targets;
        filter_devices_for_box(box_targets, 1, frame_count, box1_targets);

        if (box1_targets.multi_device_targets.device_cnt > 0) {
            // 更新当前帧索引，用于回调函数保存输出
            g_current_frame_index = frame_count;

            int ret = tecu_algorithm_target_input_multi_box(channel_id, &box1_targets, current_timestamp);
            if (ret != 0) {
                ZERROR("Box1 处理第 %zu 帧失败，错误码: %d", frame_count, ret);
            } else {
                ZINFO("Box1 处理帧 %zu: 设备数量=%u, 时间戳=%lld",
                      frame_count, box1_targets.multi_device_targets.device_cnt, current_timestamp);
            }
        }

        frame_count++;

        // 限制处理帧数用于测试
        if (frame_count > MAX_FRAME) {
            ZINFO("达到第一阶段测试帧数限制，停止处理");
            break;
        }
    }

    fclose(data_file);
    fclose(time_file);

    ZINFO("第一个盒子总共处理 %zu 帧数据", frame_count);
    return 0;
}

/*
 * @Name: process_box2_data
 * @Description: 处理第二个盒子的数据（设备3、4 + 第一个盒子的输出）
 *
 * @Input
 * data_file_path: 数据文件路径
 * time_file_path: 时间戳文件路径
 * channel_id: 通道ID
 *
 * @Output
 * 0: 成功
 * -1: 失败
 *
 * @Edit History
 * Date: 2024/12/18
 * Time: 14:00
 * Author: JiaTao
 * Content: 处理第二个盒子的数据
 */
int process_box2_data(const std::string& data_file_path,
                     const std::string& time_file_path,
                     int channel_id) {
    FILE* data_file = fopen(data_file_path.c_str(), "rb");
    if (nullptr == data_file) {
        ZERROR("无法打开数据文件: %s", data_file_path.c_str());
        return -1;
    }

    FILE* time_file = fopen(time_file_path.c_str(), "r");
    if (nullptr == time_file) {
        ZERROR("无法打开时间戳文件: %s", time_file_path.c_str());
        fclose(data_file);
        return -1;
    }

    char time_buffer[128];
    size_t frame_count = 0;
    long long current_timestamp = 0;
    MultiBoxTargets box_targets;

    ZINFO("开始处理第二个盒子的数据...");
    ZINFO("第二个盒子将接收来自第一个盒子的接力目标，总共有 %zu 帧数据", g_box1_outputs.size());

    // 逐帧读取和处理
    while (true) {
        // 读取时间戳
        if (fgets(time_buffer, sizeof(time_buffer), time_file) == nullptr) {
            break;  // 时间戳文件读取完毕
        }
        current_timestamp = atoll(time_buffer);

        // 清零当前帧数据
        memset(&box_targets, 0, sizeof(MultiBoxTargets));

        // 读取设备数量
        if (fread(&box_targets.multi_device_targets.device_cnt, sizeof(unsigned int), 1, data_file) != 1) {
            break;  // 数据文件读取完毕
        }

        // 验证设备数量
        if (box_targets.multi_device_targets.device_cnt > (DEV_GRP_NUM_MAX * 2)) {
            ZERROR("非法的设备数量: %u，帧数: %zu", box_targets.multi_device_targets.device_cnt, frame_count);
            break;
        }

        // 读取每个设备的数据
        bool read_error = false;
        for (unsigned int i = 0; i < box_targets.multi_device_targets.device_cnt; i++) {
            InputTargets &device = box_targets.multi_device_targets.device_input[i];

            // 读取设备信息
            if (fread(&device.grp_id, sizeof(unsigned int), 1, data_file) != 1 ||
                fread(&device.grp_type, sizeof(unsigned int), 1, data_file) != 1 ||
                fread(&device.target_cnt, sizeof(unsigned int), 1, data_file) != 1 ||
                fread(&device.timestamp_ms, sizeof(long long), 1, data_file) != 1) {
                read_error = true;
                break;
            }

            // 验证目标数量
            if (device.target_cnt > MAX_OBJECT_NUM) {
                ZERROR("非法的目标数量: %u，设备ID: %u，帧数: %zu",
                       device.target_cnt, device.grp_id, frame_count);
                read_error = true;
                break;
            }

            // 读取目标数据
            Target target_tmp[MAX_OBJECT_NUM];
            if (device.target_cnt > 0) {
                if (fread(target_tmp, sizeof(Target), device.target_cnt, data_file) != device.target_cnt) {
                    read_error = true;
                    break;
                }
            }

            // 转换目标数据格式
            change_target2tecu_target(target_tmp, device.targets.target_devices, device.target_cnt, device.grp_id);
        }

        // 跳过融合目标数据（原始文件中的数据，我们不需要）
        OutputTargets output_targets_;
        if (fread(&output_targets_.target_cnt, sizeof(unsigned int), 1, data_file) != 1) {
            read_error = true;
            break;
        }

        // 跳过融合目标数据
        for (unsigned int i = 0; i < output_targets_.target_cnt; i++) {
            Target target_tmp2;
            if (fread(&output_targets_.fusion_targets[i].splice_id, sizeof(unsigned int), 1, data_file) != 1 ||
                fread(&output_targets_.fusion_targets[i].grp_id, sizeof(unsigned int), 1, data_file) != 1 ||
                fread(&output_targets_.fusion_targets[i].timestamp_ms, sizeof(long long), 1, data_file) != 1 ||
                fread(&output_targets_.fusion_targets[i].splicing_x, sizeof(float), 1, data_file) != 1 ||
                fread(&output_targets_.fusion_targets[i].splicing_y, sizeof(float), 1, data_file) != 1 ||
                fread(&target_tmp2, sizeof(Target), 1, data_file) != 1) {
                read_error = true;
                break;
            }
        }

        if (read_error) {
            ZERROR("读取第 %zu 帧数据失败", frame_count);
            break;
        }

        // 过滤第二个盒子的数据（设备3、4 + 第一个盒子的输出）
        MultiBoxTargets box2_targets;
        filter_devices_for_box(box_targets, 2, frame_count, box2_targets);

        if (box2_targets.multi_device_targets.device_cnt > 0 || box2_targets.last_output_target.target_cnt > 0) {
            int ret = tecu_algorithm_target_input_multi_box(channel_id, &box2_targets, current_timestamp);
            if (ret != 0) {
                ZERROR("Box2 处理第 %zu 帧失败，错误码: %d", frame_count, ret);
            } else {
                ZINFO("Box2 处理帧 %zu: 设备数量=%u, 接力目标数量=%u, 时间戳=%lld",
                      frame_count, box2_targets.multi_device_targets.device_cnt,
                      box2_targets.last_output_target.target_cnt, current_timestamp);
            }
        }

        frame_count++;

        // 限制处理帧数用于测试
        if (frame_count > MAX_FRAME) {
            ZINFO("达到第二阶段测试帧数限制，停止处理");
            break;
        }
    }

    fclose(data_file);
    fclose(time_file);

    ZINFO("第二个盒子总共处理 %zu 帧数据", frame_count);
    return 0;
}