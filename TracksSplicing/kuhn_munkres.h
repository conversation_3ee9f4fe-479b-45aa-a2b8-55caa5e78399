#pragma once
#include <vector>
#include <cstddef>
#include <cmath>



class KuhnMunkres
{
public:
	KuhnMunkres();
	~KuhnMunkres();
	void init(int size, std::vector<std::vector<double>> diff);
	bool dfs(int a);
	void KM_Calc();
	int* getMatch();
	double getdiffSum();

private:
	std::vector<std::vector<double>> diff;
	double* ex_a;  
	double* ex_b; 
	bool* vis_a;  
	bool* vis_b;  
	int* match; 
	double* slack; 
	int size;      
	double diffSum;
};













 
