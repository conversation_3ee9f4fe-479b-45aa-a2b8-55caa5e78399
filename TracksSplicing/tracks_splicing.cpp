#include "tracks_splicing.h"
#include "tecu1000_algorithm_header.h"
#include "pipeline_track_fusion.h"
#include "Dense"
#include "zlogger.h"
#include "utils.h"

ZLOGGER_HEADER_DEFINE("ALG_TRACK_SPLICING")

using namespace tecu_r1000_0_algorithm;
using namespace tecu1000_alg;
extern trackFusionManger *tFusionManger;

TrackSplicing::TrackSplicing(int channel_id, DeviceList device_list, DevicesMap devices_map, Config track_splicing)
{
    splicing_channel_id = channel_id;
    splicing_devices_list = device_list;
    splicing_devices_map = devices_map;
    splicing_timestamp_ms = 0;
    splicing_fps = track_splicing.callback_fps;
    splicing_fps_time_ms = 0;
    splicing_fps_count = 0;
    // 拼接算法计算调用帧率的时长
    splicing_fps_duration = 5000;
    ZINFO("create TrackSplicing channel_id:%d splicing_devices_list cnt:%d splicing_devices_map:%d splicing_timestamp_ms:%lld ms splicing_fps:%d splicing_fps_duration:%lld splicing_fps_time_ms:%lld splicing_fps_count:%d \n",
          splicing_channel_id, splicing_devices_list.device_cnt, splicing_devices_map.device_map_mats.size(), splicing_timestamp_ms,
          splicing_fps, splicing_fps_duration, splicing_fps_time_ms, splicing_fps_count);

    target_direction_update_time_ms = 1000;
    ZINFO("create TrackSplicing splicing_output_direction_update channel_id:%d target_direction_update_time_ms:%lld! \n",
          splicing_channel_id, target_direction_update_time_ms);

    // 卡口相机与雷达设备目标的融合
    splicing_rvcalib = nullptr;
    BcrFusion = new RadarVisionFusion(channel_id, track_splicing);
    ZINFO("create TrackSplicing channel_id:%d BcrFusion! \n");

    splicing_input_target_path_time_threshold = 3000;
    splicing_input_target_save_time_threshold = 501; // 同时收到多个

    splicing_device_stably_detect_distance_threshold = 100;
    splicing_device_stably_track_distance_threshold = 375;
    splicing_device_max_distance_threshold = track_splicing.max_detect_distance;
    splicing_device_min_distance_threshold = track_splicing.min_detect_distance;
    ZINFO("create TrackSplicing splicing_input_target_vec channel_id:%d splicing_input_target_path_time_threshold:%lld ms splicing_input_target_save_time_threshold:%lld ms splicing_device_max_distance_threshold:%f splicing_device_min_distance_threshold:%f splicing_device_stably_distance_threshold:%f splicing_device_stably_track_distance_threshold:%.2f\n",
          splicing_channel_id, splicing_input_target_path_time_threshold, splicing_input_target_save_time_threshold,
          splicing_device_max_distance_threshold, splicing_device_min_distance_threshold,
          splicing_device_stably_detect_distance_threshold, splicing_device_stably_track_distance_threshold);

    kf_state_size = 4;    // 状态矩阵的维度
    kf_meas_size = 2;     // 测量向量的维度
    kf_control_size = 0;  // 控制矩阵的维度
    kf_t = 0.1f;          // 预测的时间间隔
    kf_rs_rate = 1000.0f; // 平滑时的R矩阵扩大系数
    kf_qs_rate = 1.0f;    // 平滑时的Q矩阵扩大系数
    kf_x_error = 3.33f;   // 平滑时的x方向误差
    kf_y_error = 2.33f;   // 平滑时的y方向误差
    kf_stime = 5000;      // 平滑时长
    ZINFO("create TrackSplicing splicing_targets_track_smooth 1 channel_id:%d kf_state_size:%d kf_meas_size:%d kf_control_size:%d kf_t:%.2f kf_rs_rate:%.2f kf_qs_rate:%.2f \n",
          splicing_channel_id, kf_state_size, kf_meas_size, kf_control_size, kf_t, kf_rs_rate, kf_qs_rate);

    ZINFO("create TrackSplicing splicing_targets_track_smooth 2 channel_id:%d kf_x_error:%.2f kf_y_error:%.2f kf_stime:%lld \n",
          splicing_channel_id, kf_x_error, kf_y_error, kf_stime);

    predict_time_interval = int(1000 / track_splicing.callback_fps);

    dead_predict_distance = 60.0f;
    max_predict_distance = 400.0f;
    ZINFO("create TrackSplicing splicing_target_predict channel_id:%d predict_time_interval:%lld ms dead_predict_distance:%f max_predict_distance:%f\n",
          splicing_channel_id, predict_time_interval, dead_predict_distance, max_predict_distance);

    lose_x_threshold = track_splicing.lose_xcoor_distance_threshold;
    lose_y_threshold = track_splicing.lose_ycoor_distance_threshold;

    second_lose_x_threshold = track_splicing.second_lose_xcoor_distance_threshold;
    second_lose_y_threshold = track_splicing.second_lose_ycoor_distance_threshold;

    ZINFO("create TrackSplicing lose error  lose_x_threshold:%.2f lose_y_threshold：%.2f second_lose_x_threshold:%.2f second_lose_y_threshold:%.2f\n",
          lose_x_threshold, lose_y_threshold, second_lose_x_threshold, second_lose_y_threshold);
    lose_speed_rate = 0.2f;
    lose_match_weigth_threshold = 0.01f;
    lose_speed_threshold = 3.6f; // km/h
    lose_plate_top1_score = 0.9f;
    lose_plate_top2_score = 0.8f;
    lose_number_plate_distance = 40.0f;
    ZINFO("create TrackSplicing splcing_target_lose_match channel_id:%d lose_x_threshold:%f lose_y_threshold:%f lose_speed_rate:%f lose_match_weigth_threshold:%f lose_speed_threshold:%f lose_plate_top1_score:%f lose_plate_top2_score:%f lose_number_plate_distance:%.2f \n",
          splicing_channel_id, lose_x_threshold, lose_y_threshold, lose_speed_rate, lose_match_weigth_threshold, lose_speed_threshold, lose_plate_top1_score, lose_plate_top2_score, lose_number_plate_distance);

    splicing_fusion_id_init = 0;
    splicing_fusion_id_count = 0;
    splicing_fusion_id_min = 1;
    splicing_fusion_id_max = MAX_OBJECT_NUM;
    splicing_batch_frame_count = 2;
    splicing_fusion_lost_time_threshold = 20000; // ms
    splicing_fusion_path_time_threshold = 2000;  // m
    ZINFO("create TrackSplicing splicing_output_targets_vec channel_id:%d splicing_fusion_id_init:%d splicing_fusion_id_count:%d splicing_fusion_id_min:%d splicing_fusion_id_max:%d splicing_batch_frame_count:%d splicing_fusion_lost_time_threshold:%lld ms splicing_fusion_path_time_threshold:%lld \n",
          splicing_channel_id, splicing_fusion_id_init, splicing_fusion_id_count, splicing_fusion_id_min, splicing_fusion_id_max, splicing_batch_frame_count, splicing_fusion_lost_time_threshold, splicing_fusion_path_time_threshold);

    spilcing_default_match_weight = 100;
    splicing_match_speed_threshold = 3.6f; // km/h
    splicing_match_time_threshold = 301;
    splicing_km = new KuhnMunkres;
    splicing_match_score_threshold = 0.1f;
    splicing_speed_rate = 0.2f;
    splicing_plate_match_top1_score = 0.99f;
    splicing_plate_match_top2_score = 0.98f;
    splicing_speed_time_threshold = 1000;
    ZINFO("create TrackSplicing spilcing_match_ids_vec channel_id:%d spilcing_default_match_weight:%f splicing_match_speed_threshold:%f km/h splicing_match_time_threshold:%lld ms splicing_match_score_threshold:%f splicing_speed_rate:%f splicing_plate_match_score:%f splicing_plate_match_first_score:%f splicing_speed_time_threshold:%lld\n",
          splicing_channel_id, spilcing_default_match_weight,
          splicing_match_speed_threshold,
          splicing_match_time_threshold, splicing_match_score_threshold,
          splicing_speed_rate,
          splicing_plate_match_top1_score, splicing_plate_match_top2_score,
          splicing_speed_time_threshold);

    splicing_match_xcoor_distance_theshold = track_splicing.match_xcoor_distance_theshold;
    splicing_match_ycoor_distance_theshold = track_splicing.match_ycoor_distance_theshold;

    splicing_batch_xcoor_distance_theshold = track_splicing.batch_xcoor_distance_theshold;
    splicing_batch_ycoor_distance_theshold = track_splicing.batch_ycoor_distance_theshold;

    splicing_match_direction_threshold = 3.0f;
    ZINFO("create TrackSplicing spilcing_match_ids_vec channel_id:%d splicing_match_xcoor_distance_theshold:%f splicing_match_ycoor_distance_theshold:%f splicing_batch_xcoor_distance_theshold:%f splicing_batch_ycoor_distance_theshold:%f splicing_match_direction_threshold:%.2f\n",
          splicing_channel_id, splicing_match_xcoor_distance_theshold, splicing_match_ycoor_distance_theshold,
          splicing_batch_xcoor_distance_theshold, splicing_batch_ycoor_distance_theshold, splicing_match_direction_threshold);

    splicing_batch_min_time = 200;
    splicing_batch_max_time = 1000;

    splicing_fusion_near_time_threshold = 210;
    splicing_batch_score_threshold = 0.01f;
    splicing_batch_count_rate_threshold = 0.6f;
    splicing_batch_lose_time_threshold = 301;
    ZINFO("create TrackSplicing spilcing_match_ids_vec channel_id:%d splicing_batch_min_time:%lld splicing_batch_max_time:%lld splicing_fusion_near_time_threshold:%lld, splicing_batch_score_threshold:%f splicing_batch_count_rate_threshold:%f splicing_batch_lose_time_threshold:%lld\n",
          splicing_channel_id, splicing_batch_min_time, splicing_batch_max_time, splicing_fusion_near_time_threshold, splicing_batch_score_threshold, splicing_batch_count_rate_threshold, splicing_batch_lose_time_threshold);

    splicing_output_targets.target_cnt = 0;
    ZINFO("create TrackSplicing splicing_output_targets  target_cnt:%d \n",
          splicing_output_targets.target_cnt);

    splicing_output_targets_next.target_cnt = 0;
    ZINFO("create TrackSplicing splicing_output_targets_next  target_cnt:%d \n",
          splicing_output_targets_next.target_cnt);

    plate_color_update_threshold = 0.2;
    plate_number_update_threshold = 10.0f;
    vehicle_color_update_threshold = 0.2;
    vehicle_type_update_threshold = 0.2;
    ZINFO("create TrackSplicing splicing_target_info_update plate_color_update_threshold:%.2f plate_number_update_threshold:%.2f vehicle_color_update_threshold:%.2f vehicle_type_update_threshold:%.2f \n",
          plate_color_update_threshold, plate_number_update_threshold, vehicle_color_update_threshold, vehicle_type_update_threshold);

    std::vector<std::string> plate_str = {"#", "京", "沪", "津", "渝", "冀", "晋", "蒙", "辽", "吉", "黑", "苏", "浙", "皖",
                                          "闽", "赣", "鲁", "豫", "鄂", "湘", "粤", "桂", "琼", "川", "贵", "云", "藏", "陕", "甘", "青", "宁", "新", "学", "警", "港", "澳", "挂", "使", "领", "民", "航", "深",
                                          "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "J", "K", "L", "M", "N", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "险", "品", "应", "急", "试"};

    ZINFO("plate_str size:%d \n", plate_str.size());
    print_char_byte_sizes(plate_str);

    roadDirectionCacl = new RoadDirectionCacl(channel_id, track_splicing.files_dir_path);
    ZINFO("create  roadDirectionCacl class \n");

    laneSmooth = new VehicleLaneSmoothing();
    ZINFO("create laneSmooth class! \n");

    collisionDetection = new CollisionDetection();
    ZINFO("create collisionDetection class! \n");

    is_platform_relay_mode = track_splicing.is_platform_relay_mode;
    ZINFO("create TrackSplicing is_platform_relay_mode:%d \n", is_platform_relay_mode);

    // 初始化目标
    use_input_noise_filter = track_splicing.use_input_noise_filter;
    use_output_result_smooth = track_splicing.use_output_result_smooth;
    ZINFO("create TrackSplicing use_input_noise_filter:%d use_output_result_smooth:%d \n", use_input_noise_filter, use_output_result_smooth);

    log_info_level = track_splicing.log_info_level;
}

TrackSplicing::~TrackSplicing()
{
    splicing_input_target_vec_clean();
    ZINFO("release TrackSplicing splicing_input_target_vec! \n");

    if (BcrFusion != nullptr)
    {
        delete BcrFusion;
        BcrFusion = nullptr;
    }
    ZINFO("release TrackSplicing BcrFusion! \n");

    splicing_output_targets_vec_clean();
    ZINFO("release TrackSplicing splicing_output_targets_vec! \n");

    spilcing_match_ids_vec_clean();
    ZINFO("release TrackSplicing spilcing_match_ids_vec! \n");

    std::map<unsigned int, DevicesCalibMat>().swap(splicing_devices_map.device_map_mats);
    ZINFO("release TrackSplicing splicing_devices_map! \n");

    if (roadDirectionCacl != nullptr)
    {
        delete roadDirectionCacl;
        roadDirectionCacl = nullptr;
    }
    ZINFO("release roadDirectionCacl! \n");

    if (laneSmooth != nullptr)
    {
        delete laneSmooth;
        laneSmooth = nullptr;
    }
    ZINFO("release laneSmooth! \n");

    if (collisionDetection != nullptr)
    {
        delete collisionDetection;
        collisionDetection = nullptr;
    }
    ZINFO("release collisionDetection! \n");
}

void TrackSplicing::tracks_splicing_main(MultiDeviceTargets multi_device_targets, long long timestamp_ms)
{
    // 打印系统时间 ms
    struct timespec sys_run_time = {0, 0};
    clock_gettime(CLOCK_MONOTONIC, &sys_run_time);
    long long callback_time_ms_start = sys_run_time.tv_sec * 1000 + sys_run_time.tv_nsec / 1000000;
#ifdef DEBUG1
    ZINFO("tracks_splicing_main 系统时间:%lld 算法输入时间:%lld \n", callback_time_ms_start, timestamp_ms);
    ZINFO("start_time: %lld \n", timestamp_ms);
#endif
    // 校验每个设备的时间戳，如果每个设备的时间戳不存在，则设置为发送数据的时间戳
    for (unsigned int mdt = 0; mdt < multi_device_targets.device_cnt; mdt++)
    {
#ifdef DEBUG1
        ZINFO("device_id:%d timestamp_ms:%lld cnt:%d \n",
        multi_device_targets.device_input[mdt].device_id,
        multi_device_targets.device_input[mdt].timestamp_ms,
        multi_device_targets.device_input[mdt].target_cnt);
#endif
        multi_device_targets.device_input[mdt].timestamp_ms = timestamp_ms;
    }

    // 前后两帧的时间戳校验
    // 时间戳校验
    dt = timestamp_ms - splicing_timestamp_ms;
    if (dt > splicing_input_target_save_time_threshold)
    {
        ZERROR("current frame time_ms:%lld and previous frame time_ms:%lld > splicing_input_target_save_time_threshold, time_diff:%lld \n",
               timestamp_ms, splicing_timestamp_ms, dt);
    }
    auto tracks_splicing_main_start = std::chrono::system_clock::now();

    // 当前的平均时间
    splicing_timestamp_ms = timestamp_ms;

    // 得到算法调用的帧率
    get_splicing_fps(timestamp_ms);

    // 得到算法中设备的平均速度 一次处理多个设备
    devices_speed_map_update(multi_device_targets, this->devices_speed_map);

    // 输入目标信息加入缓存 一次处理多个设备
    splicing_input_target_vec_update(multi_device_targets);

    // 对输入目标数据插值
    // splicing_input_target_interploation(this->splicing_input_target_vec,
    //                                     splicing_timestamp_ms);

    // 每台目标的方位角
    get_target_direction(this->splicing_input_target_vec, this->splicing_devices_list, splicing_timestamp_ms);

    // 统计道路信息 使用 车道线信息
    roadDirectionCacl->get_device_direction_map(
        this->splicing_input_target_vec,
        splicing_device_min_distance_threshold,
        splicing_device_max_distance_threshold,
        splicing_devices_list,
        laneSmooth->get_lane_infos());

    // 当未统计完成时不进行建模
    if (roadDirectionCacl->road_direction_state != RoadDirectionState::DIRECTION_END)
    {
        return;
    }

    // 输入的缓存信息
#ifdef DEBUG_DATA_SPLICING_INPUT
    ZINFO("splicing_input_target_vec splicing_input_target_vec.size:%d fps:%d \n", splicing_input_target_vec.size(), splicing_fps);
    for (unsigned int sot = 0; sot < splicing_input_target_vec.size(); sot++)
    {
        ZINFO("splicing_input_target_vec id:%d device_id:%d bx:%.2f by:%.2f target_direction:%.2f distance:%.2f x:%.2f y:%.2f s:%.2f target_area_status:%d,n_plate:%s np_score:%.2f p_color:%d pc_score:%.2f v_color:%d vc_score:%.2f v_type:%d vt_socre:%.2f tt_type:%d, tt_score:%.2f time:%lld\n",
              splicing_input_target_vec[sot].id, splicing_input_target_vec[sot].device_id,
              splicing_input_target_vec[sot].target_input.target_coor_in_base.x, splicing_input_target_vec[sot].target_input.target_coor_in_base.y,
              splicing_input_target_vec[sot].target_input.target.direction, splicing_input_target_vec[sot].target_input.target.distance,
              splicing_input_target_vec[sot].target_input.target.x, splicing_input_target_vec[sot].target_input.target.y,
              splicing_input_target_vec[sot].target_input.target.speed,
              splicing_input_target_vec[sot].target_area_status,
              splicing_input_target_vec[sot].target_input.target.number_plate, splicing_input_target_vec[sot].target_input.target.np_score,
              splicing_input_target_vec[sot].target_input.target.plate_color, splicing_input_target_vec[sot].target_input.target.pc_score,
              splicing_input_target_vec[sot].target_input.target.vehicle_color, splicing_input_target_vec[sot].target_input.target.vc_score,
              splicing_input_target_vec[sot].target_input.target.vehicle_type, splicing_input_target_vec[sot].target_input.target.vt_score,
              splicing_input_target_vec[sot].target_input.target.target_type, splicing_input_target_vec[sot].target_input.target.tt_score,
              splicing_input_target_vec[sot].target_input.time_ms);
    }
    ZINFO("\n");
#endif

    // 重叠区域的关联
    spilcing_match_ids_vec_update();

    // 拼接目标的跟踪
    splicing_targets_track();

    // 拼接目标的车道级修正
    laneSmooth->lane_amend_targets(this->splicing_output_targets_vec);

#ifdef DEBUG_DATA_SPLICING_OUTPUT
    ZINFO("splicing_output_targets_vec Print, splicing_output_targets_vec.size:%d \n", splicing_output_targets_vec.size());
    for (unsigned int sot = 0; sot < splicing_output_targets_vec.size(); sot++)
    {
        // 计算d_kx d_dy
        ZINFO("splicing_output_targets_vec fusion_id:%d k_x:%.2f k_y:%.2f k_vx:%.2f k_vy:%.2f b_x:%.2f b_y:%.2f status:%d a_status:%d id:%d device_id:%d direction:%.2f distance:%.2f long:%.7f lat:%.7f x:%.2f y:%.2f s:%.2f  px:%.2f py:%.2f smmoth_speed:%.2f is_predict_state:%d is_speed:%d is_retreat:%d n_plate:%s p_color:%d time_ms:%lld start_timestamp_ms:%lld target_time_ms:%lld p:%d\n",
              splicing_output_targets_vec[sot].target_output.fusion_id,
              splicing_output_targets_vec[sot].target_kalman->kf_update_res[0], splicing_output_targets_vec[sot].target_kalman->kf_update_res[1],
              splicing_output_targets_vec[sot].target_kalman->kf_update_res[2], splicing_output_targets_vec[sot].target_kalman->kf_update_res[3],
              splicing_output_targets_vec[sot].target_output.target_coor_in_base.x, splicing_output_targets_vec[sot].target_output.target_coor_in_base.y,
              splicing_output_targets_vec[sot].target_output.splicing_state,
              splicing_output_targets_vec[sot].target_output.target_area_status,
              splicing_output_targets_vec[sot].target_output.id,
              splicing_output_targets_vec[sot].target_output.device_id,
              splicing_output_targets_vec[sot].target_output.target.direction,
              splicing_output_targets_vec[sot].target_output.target.distance,
              splicing_output_targets_vec[sot].target_output.target.longitude, splicing_output_targets_vec[sot].target_output.target.latitude,
              splicing_output_targets_vec[sot].target_output.target.x, splicing_output_targets_vec[sot].target_output.target.y,
              splicing_output_targets_vec[sot].target_output.target.speed,
              splicing_output_targets_vec[sot].target_predict_xcoor, splicing_output_targets_vec[sot].target_predict_ycoor,
              splicing_output_targets_vec[sot].target_kalman->speed_smooth_threshold * 3.60f * splicing_fps,
              splicing_output_targets_vec[sot].target_kalman->is_predict_state,
              splicing_output_targets_vec[sot].target_kalman->is_speed_state,
              splicing_output_targets_vec[sot].target_kalman->is_retreat_state,
              splicing_output_targets_vec[sot].target_output.target.number_plate, splicing_output_targets_vec[sot].target_output.target.plate_color,
              splicing_timestamp_ms, splicing_output_targets_vec[sot].target_output.target_start_timestamp_ms, splicing_output_targets_vec[sot].target_output.target_timestamp_ms,
              splicing_output_targets_vec[sot].target_output_path.size());
    }
    // 输出拼接信息
    for (unsigned int sot = 0; sot < spilcing_match_ids_vec.size(); sot++)
    {
        ZINFO("spilcing_match_ids_vec id1:%d id2:%d device1_id:%d device2_id:%d speed:%.2f batch_x_error:%.2f batch_y_error:%.2f splicing_update_time:%lld splicing_start_time:%lld splicing_batch_count:%d splicing_match_score_vec.size:%d splicing_batch_flag:%d splicing_coor:%.2f %.2f \n",
              spilcing_match_ids_vec[sot].id1, spilcing_match_ids_vec[sot].id2, spilcing_match_ids_vec[sot].device1_id, spilcing_match_ids_vec[sot].device2_id,
              spilcing_match_ids_vec[sot].speed, spilcing_match_ids_vec[sot].batch_x_error, spilcing_match_ids_vec[sot].batch_y_error,
              spilcing_match_ids_vec[sot].splicing_update_time, spilcing_match_ids_vec[sot].splicing_start_time, spilcing_match_ids_vec[sot].splicing_batch_count,
              spilcing_match_ids_vec[sot].spilcing_match_score_vec.size(), spilcing_match_ids_vec[sot].spilcing_batch_flag,
              spilcing_match_ids_vec[sot].splicing_coor.x, spilcing_match_ids_vec[sot].splicing_coor.y);
    }
#endif

    // 输出目标的信息
    splicing_output_target_get_direction();
    // 将最后一个设备的目标从splicing_output_targets中移动到splicing_output_targets_next
    split_splicing_output_targets();

    // 目标平滑
    if(use_output_result_smooth) {
        smooth_target_output(splicing_output_targets);
    }


#ifdef DEBUG_DATA_THREAD_OUTPUT
        for (size_t sot = 0; sot < splicing_output_targets.target_cnt; sot++)
        {
            ZINFO("thread_output_targets_: splice_id:%d device_id:%d id:%d splicing_x:%f splicing_y:%f long:%.7f lat:%.7f timestamp_ms:%lld \n",
                splicing_output_targets.fusion_targets[sot].splice_id,
                splicing_output_targets.fusion_targets[sot].device_id,
                splicing_output_targets.fusion_targets[sot].splice_target.id,
                splicing_output_targets.fusion_targets[sot].splicing_x,
                splicing_output_targets.fusion_targets[sot].splicing_y,
                splicing_output_targets.fusion_targets[sot].splice_target.longitude,
                splicing_output_targets.fusion_targets[sot].splice_target.latitude,
                timestamp_ms);
        }
    // ZINFO("\n output end \n");
#endif

    #ifdef DEBUG_DATA_MULTI_BOX_OUTPUT
        ZINFO("multi_boxes_output: splicing_output_targets target_cnt:%d \n", splicing_output_targets.target_cnt);
        for (size_t sot = 0; sot < splicing_output_targets.target_cnt; sot++)
        {
            ZINFO("multi_boxes_output: splice_id:%d device_id:%d id:%d splicing_x:%f splicing_y:%f timestamp_ms:%lld \n",
                splicing_output_targets.fusion_targets[sot].splice_id,
                splicing_output_targets.fusion_targets[sot].device_id,
                splicing_output_targets.fusion_targets[sot].splice_target.id,
                splicing_output_targets.fusion_targets[sot].splicing_x,
                splicing_output_targets.fusion_targets[sot].splicing_y,
                timestamp_ms);
        }

        ZINFO("multi_boxes_output: splicing_output_targets_next target_cnt:%d \n", splicing_output_targets_next.target_cnt);
        for (size_t sot = 0; sot < splicing_output_targets_next.target_cnt; sot++)
        {
            ZINFO("multi_boxes_output: splice_id:%d device_id:%d id:%d splicing_x:%f splicing_y:%f timestamp_ms:%lld \n",
                splicing_output_targets_next.fusion_targets[sot].splice_id,
                splicing_output_targets_next.fusion_targets[sot].device_id,
                splicing_output_targets_next.fusion_targets[sot].splice_target.id,
                splicing_output_targets_next.fusion_targets[sot].splicing_x,
                splicing_output_targets_next.fusion_targets[sot].splicing_y,
                timestamp_ms);
        }
    ZINFO("\n output end \n");
#endif
// 生成平滑的模拟数据
#ifdef DEBUG_MAKE_SMOOTH_DATA
    make_smooth_data();
#endif

    // 打印时间
    clock_gettime(CLOCK_MONOTONIC, &sys_run_time);
    long long callback_time_ms_end = sys_run_time.tv_sec * 1000 + sys_run_time.tv_nsec / 1000000;
    int tracks_splicing_main_duration = callback_time_ms_end - callback_time_ms_start;
#ifdef DEBUG1
    ZINFO("tracks_splicing_main callback_time_ms_start:%lld timestamp_ms:%lld tracks_splicing_main_duration:%d\n", callback_time_ms_start, timestamp_ms, tracks_splicing_main_duration);
    ZINFO("\n output end \n");
#endif
    if(tracks_splicing_main_duration > 100)
    {
        ZERROR("tracks_splicing_main took:%d > 100, the is except!\n", tracks_splicing_main_duration);
    }

}


#ifdef DEBUG_MAKE_SMOOTH_DATA
    void TrackSplicing::make_smooth_data()
    {
        double longitude = splicing_devices_list.device_list[0].device_longitude;
        double latitude = splicing_devices_list.device_list[0].device_latitude;

        // 初始化
        if(splicing_output_targets_smooth.target_cnt == 0) {
            splicing_output_targets_smooth.target_cnt = 3;
            // 初始化坐标为第一个设备的坐标
            for(unsigned int sots = 0; sots < splicing_output_targets_smooth.target_cnt; sots++) {
                // 基本信息
                splicing_output_targets_smooth.fusion_targets[sots].device_id = 1;
                splicing_output_targets_smooth.fusion_targets[sots].splice_id = 10000 + sots;

                // 位置和运动信息
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.speed = 10;
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.angle = 270;
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.direction = 270;
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.distance = 0;
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.radar_coord_dist = 0;
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.radar_coord_angle = 0;

                // 初始经纬度位置
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.longitude = longitude;
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.latitude = (latitude - (0.00007 * sots));

                // 车辆信息
                strncpy(splicing_output_targets_smooth.fusion_targets[sots].splice_target.number_plate,
                       ("TEST" + std::to_string(sots)).c_str(), MAX_PLATE_TEXT);
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.np_score = 0.95;
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.plate_color = PlateColor::BLUE_COLOR;
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.pc_score = 0.9;
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.vehicle_type = VehicleType::CAR;
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.vt_score = 0.85;
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.vehicle_color = VehicleColor::VEHICLE_WHITE_COLOR;
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.vc_score = 0.8;

                // 事件状态
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.event_state.b_snake_change = true;

                // 转换经纬度到xy坐标
                double x = 0, y = 0;
                Utils::lonlatToMercator(
                    splicing_output_targets_smooth.fusion_targets[sots].splice_target.longitude,
                    splicing_output_targets_smooth.fusion_targets[sots].splice_target.latitude,
                    x, y);
                splicing_output_targets_smooth.fusion_targets[sots].splicing_x = x;
                splicing_output_targets_smooth.fusion_targets[sots].splicing_y = y;
            }
        }

        // 每次更新目标位置
        for(unsigned int sots = 0; sots < splicing_output_targets_smooth.target_cnt; sots++) {
            // 检查目标是否需要重置到初始位置
            if(fabs(splicing_output_targets_smooth.fusion_targets[sots].splice_target.longitude - longitude) > 0.01) {
                // 重置到初始位置
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.longitude = longitude;
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.latitude = (latitude - (0.00007 * sots));

                // 转换xy坐标
                double x = 0, y = 0;
                Utils::lonlatToMercator(
                    splicing_output_targets_smooth.fusion_targets[sots].splice_target.longitude,
                    splicing_output_targets_smooth.fusion_targets[sots].splice_target.latitude,
                    x, y);
                splicing_output_targets_smooth.fusion_targets[sots].splicing_x = x;
                splicing_output_targets_smooth.fusion_targets[sots].splicing_y = y;
            }

            // 每次往西移动约2米
            // 经度每减少0.000018度约等于2米
            splicing_output_targets_smooth.fusion_targets[sots].splice_target.longitude -= 0.000018;

            // 更新xy坐标
            double x = 0, y = 0;
            Utils::lonlatToMercator(
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.longitude,
                splicing_output_targets_smooth.fusion_targets[sots].splice_target.latitude,
                x, y);
            splicing_output_targets_smooth.fusion_targets[sots].splicing_x = x;
            splicing_output_targets_smooth.fusion_targets[sots].splicing_y = y;
        }

        for (size_t sot = 0; sot < splicing_output_targets_smooth.target_cnt; sot++)
        {
            ZINFO("thread_output_targets_: splice_id:%d device_id:%d id:%d splicing_x:%f splicing_y:%f long:%.7f lat:%.7f \n",
                splicing_output_targets_smooth.fusion_targets[sot].splice_id,
                splicing_output_targets_smooth.fusion_targets[sot].device_id,
                splicing_output_targets_smooth.fusion_targets[sot].splice_target.id,
                splicing_output_targets_smooth.fusion_targets[sot].splicing_x,
                splicing_output_targets_smooth.fusion_targets[sot].splicing_y,
                splicing_output_targets_smooth.fusion_targets[sot].splice_target.longitude,
                splicing_output_targets_smooth.fusion_targets[sot].splice_target.latitude);
        }
        // splicing_output_targets.target_cnt = splicing_output_targets_smooth.target_cnt;
        // for (size_t sot = 0; sot < splicing_output_targets_smooth.target_cnt; sot++)
        // {
        //     splicing_output_targets.fusion_targets[sot] = splicing_output_targets_smooth.fusion_targets[sot];
        // }
        splicing_output_targets = splicing_output_targets_smooth;
    }
#endif

void TrackSplicing::tracks_splicing_main(tecu1000_alg::MultiBoxTargets& multi_box_targets, long long timestamp_ms)
{

    if (log_info_level == 9) return;
    if(this->log_info_level > 0)
    {
        ZINFO("target_input_multi_box multi_device_targets.device_cnt: %d timestamp_ms: %lld \n", multi_box_targets.multi_device_targets.device_cnt, timestamp_ms);
        // 同步每个设备的时间
        for (unsigned int mdt = 0; mdt < multi_box_targets.multi_device_targets.device_cnt; mdt++)
        {
            ZINFO("device_id:%d timestamp_ms:%lld cnt:%d \n", 
            multi_box_targets.multi_device_targets.device_input[mdt].device_id, 
            multi_box_targets.multi_device_targets.device_input[mdt].timestamp_ms, 
            multi_box_targets.multi_device_targets.device_input[mdt].target_cnt);
        }
    }
    // 数据校验
    check_update_multi_box_targets(multi_box_targets, timestamp_ms);
    struct timespec sys_run_time = {0, 0};
    clock_gettime(CLOCK_MONOTONIC, &sys_run_time);
    long long callback_time_ms_start = sys_run_time.tv_sec * 1000 + sys_run_time.tv_nsec / 1000000;
    // 当前盒子所接管的设备输入
    MultiDeviceTargets& multi_device_targets = multi_box_targets.multi_device_targets;
    // 来自上一个盒子的最后一个设备接管的设备输出 带有拼接信息
    OutputTargets& last_output_targets = multi_box_targets.last_output_target;

    // 前后两帧的时间戳校验
    // 时间戳校验
    dt = timestamp_ms - splicing_timestamp_ms;
    if (dt > splicing_input_target_save_time_threshold)
    {
        ZINFO("current frame time_ms:%lld and previous frame time_ms:%lld > splicing_input_target_save_time_threshold, time_diff:%lld \n",
               timestamp_ms, splicing_timestamp_ms, dt);
    }
    auto tracks_splicing_main_start = std::chrono::system_clock::now();

    // 当前的平均时间
    splicing_timestamp_ms = timestamp_ms;

    // 得到算法调用的帧率
    get_splicing_fps(timestamp_ms);
    // 得到算法中设备的平均速度 一次处理多个设备
    devices_speed_map_update(multi_device_targets, this->devices_speed_map);
    // 将上一个设备的输出加入缓存
    splicing_input_target_vec_update(last_output_targets);

    // 输入目标信息加入缓存 一次处理多个设备
    splicing_input_target_vec_update(multi_device_targets);

    // 每台目标的方位角
    get_target_direction(this->splicing_input_target_vec, this->splicing_devices_list, splicing_timestamp_ms);

    // 统计道路信息 使用 车道线信息
    roadDirectionCacl->get_device_direction_map(
        this->splicing_input_target_vec,
        splicing_device_min_distance_threshold,
        splicing_device_max_distance_threshold,
        splicing_devices_list,
        laneSmooth->get_lane_infos());

    // 当未统计完成时不进行建模
    if (roadDirectionCacl->road_direction_state != RoadDirectionState::DIRECTION_END)
    {
        return;
    }

    // 重叠区域的关联
    spilcing_match_ids_vec_update();

    // 拼接目标的跟踪
    splicing_targets_track();

    // 拼接目标的车道级修正
    laneSmooth->lane_amend_targets(this->splicing_output_targets_vec);

    // // 车辆碰撞检测与纠正
    // if(use_collision_detection_correction) {
    //     collisionDetection->vehicle_collision_detection_and_correction(this->splicing_output_targets_vec, splicing_timestamp_ms);
    // }

    // 输出目标的信息 更新目标经纬度
    splicing_output_target_get_direction();

    // 将最后一个设备的目标从splicing_output_targets中移动到splicing_output_targets_next
    if(is_platform_relay_mode != 3) {
        split_splicing_output_targets();
    }

    // 目标平滑 只对上报平台的数据进行平滑(splicing_output_targets) splicing_output_targets_next不需要平滑 在下一个盒子再进行平滑
    // 目标平滑
    if(use_output_result_smooth) {
        smooth_target_output(splicing_output_targets);
    }

    print_box_targets(timestamp_ms);

    auto tracks_splicing_main_end = std::chrono::system_clock::now();
    int tracks_splicing_main_duration = std::chrono::duration_cast<std::chrono::milliseconds>(tracks_splicing_main_end - tracks_splicing_main_start).count();
    if (tracks_splicing_main_duration > 100) {
        ZINFO("tracks_splicing_main took:%d > 100, the is except!\n", tracks_splicing_main_duration);
    }
}

void TrackSplicing::split_splicing_output_targets() {
    // 将最后一个设备的目标从splicing_output_targets中移动到splicing_output_targets_next
    splicing_output_targets_next.target_cnt = 0;
    // 如果是最后一个设备，则跳过
    if(is_platform_relay_mode == 2) {
        return;
    }

    // 创建临时数组存储需要保留的目标
    OutputTargets temp_targets;
    temp_targets.target_cnt = 0;

    // 遍历所有目标
    for (unsigned int i = 0; i < splicing_output_targets.target_cnt; i++) {
        int last_device_id_index = splicing_devices_list.device_cnt - 1;
        // int last_device_id_index = 1;  // TODO: 测试用
        // 如果是最后一个设备的目标，移动到next
        if (splicing_output_targets.fusion_targets[i].device_id ==
            splicing_devices_list.device_list[last_device_id_index].device_id) {
            // 如果不是真实目标则跳过
            if(check_target_is_real_target(splicing_output_targets.fusion_targets[i]) == false) {
                continue;
            }
            splicing_output_targets_next.fusion_targets[splicing_output_targets_next.target_cnt] =
                splicing_output_targets.fusion_targets[i];
            // 设置最后一台设备的目标的device_id为0，传递给下一个盒子代表上一个盒子的最后一个设备的id
#ifdef DEBUG1
            ZINFO("id:%d device_id:%d 在最后一个设备，移动到next\n", splicing_output_targets_next.fusion_targets[splicing_output_targets_next.target_cnt].splice_id, splicing_output_targets_next.fusion_targets[splicing_output_targets_next.target_cnt].device_id);
#endif
            splicing_output_targets_next.fusion_targets[splicing_output_targets_next.target_cnt].device_id = 0;
            splicing_output_targets_next.target_cnt++;
        } else {
            // 如果不是最后一个设备的目标，保留在临时数组中
            temp_targets.fusion_targets[temp_targets.target_cnt] = splicing_output_targets.fusion_targets[i];
            temp_targets.target_cnt++;
        }
    }
    // 上报给平台的数据
    splicing_output_targets = temp_targets;
}

bool TrackSplicing::check_target_is_real_target(OutputTarget& target) {
    for (size_t sit = 0; sit < splicing_input_target_vec.size(); sit++) {
        if (target.splice_target.id == splicing_input_target_vec[sit].id &&
            target.device_id == splicing_input_target_vec[sit].device_id) {
            return true;
        }
    }
#ifdef DEBUG1
    ZINFO("check_target_is_real_target id:%d device_id:%d 不在输入目标中\n", target.splice_id, target.device_id);
#endif
    return false;
}

void TrackSplicing::smooth_target_output(OutputTargets& targets) {
    try {

        if (targets.target_cnt <= 0) {
            // ZERROR("No targets to smooth");
            return;
        }

        std::unordered_set<int> active_ids;

        // 处理每个目标
        for (size_t i = 0; i < targets.target_cnt; i++) {
            if (i >= 100) {
                ZERROR("Target count exceeds MAX_TARGET_NUM");
                break;
            }

            auto& target = targets.fusion_targets[i];
            int target_id = target.splice_id;

            if (target_id < 0) {
                ZERROR("Invalid target ID: %d", target_id);
                continue;
            }
            active_ids.insert(target_id);

            try {
                // 创建或获取平滑器
                if (_smoothers.find(target_id) == _smoothers.end()) {
                    _smoothers[target_id] = TargetSmoother(target_id, SMOOTH_WINDOW_SIZE, SMOOTH_ALPHA);
                }

                // 更新平滑器
                _smoothers[target_id].update(
                    target.splicing_x,
                    target.splicing_y,
                    // target.timestamp_ms
                    splicing_timestamp_ms
                );

                // 获取平滑后的位置
                auto [smoothed_x, smoothed_y] = _smoothers[target_id].getSmoothedPosition();

                // 更新目标位置
                target.splicing_x = smoothed_x;
                target.splicing_y = smoothed_y;
                double mercator_x = (double)target.splicing_x + (double)splicing_devices_list.device_list[0].x;
                double mercator_y = (double)target.splicing_y + (double)splicing_devices_list.device_list[0].y;
                Utils::mercatorToLonLat(mercator_x,
                                        mercator_y,
                                        target.splice_target.longitude,
                                        target.splice_target.latitude);
                // 更新经纬度


            } catch (const std::exception& e) {
                ZERROR("Exception processing target %d: %s", target_id, e.what());
                continue;
            }
        }

        // 清理不活跃的平滑器
        for (auto it = _smoothers.begin(); it != _smoothers.end();) {
            if (!active_ids.count(it->first)) {
                it = _smoothers.erase(it);
            } else {
                ++it;
            }
        }

    } catch (const std::exception& e) {
        ZERROR("Exception in smooth_target_output: %s", e.what());
    } catch (...) {
        ZERROR("Unknown exception in smooth_target_output");
    }
}



void TrackSplicing::smooth_target_output(std::vector<SplicingTarget>& targets) {
    try {

        if (targets.size() <= 0) {
            // ZERROR("No targets to smooth");
            return;
        }

        std::unordered_set<int> active_ids;

        // 处理每个目标
        for (size_t i = 0; i < targets.size(); i++) {
            if (i >= 100) {
                ZERROR("Target count exceeds MAX_TARGET_NUM");
                break;
            }

            auto& target = targets[i];
            int target_id = target.target_output.fusion_id;

            if (target_id < 0) {
                ZERROR("Invalid target ID: %d", target_id);
                continue;
            }
            active_ids.insert(target_id);

            try {
                // 创建或获取平滑器
                if (_smoothers.find(target_id) == _smoothers.end()) {
                    _smoothers[target_id] = TargetSmoother(target_id, 10);
                }

                // 更新平滑器
                _smoothers[target_id].update(
                    target.target_kalman->kf_update_res[0],
                    target.target_kalman->kf_update_res[1],
                    // target.timestamp_ms
                    splicing_timestamp_ms
                );

                // 获取平滑后的位置
                auto [smoothed_x, smoothed_y] = _smoothers[target_id].getSmoothedPosition();

                // 更新目标位置
                target.target_kalman->kf_update_res[0] = smoothed_x;
                target.target_kalman->kf_update_res[1] = smoothed_y;
                double mercator_x = (double)target.target_kalman->kf_update_res[0] + (double)splicing_devices_list.device_list[0].x;
                double mercator_y = (double)target.target_kalman->kf_update_res[1] + (double)splicing_devices_list.device_list[0].y;
                // Utils::mercatorToLonLat(mercator_x,
                //                         mercator_y,
                //                         target.target_output.target.longitude,
                //                         target.target_output.target.latitude);
                // 更新经纬度


            } catch (const std::exception& e) {
                ZERROR("Exception processing target %d: %s", target_id, e.what());
                continue;
            }
        }

        // 清理不活跃的平滑器
        for (auto it = _smoothers.begin(); it != _smoothers.end();) {
            if (!active_ids.count(it->first)) {
                it = _smoothers.erase(it);
            } else {
                ++it;
            }
        }

    } catch (const std::exception& e) {
        ZERROR("Exception in smooth_target_output: %s", e.what());
    } catch (...) {
        ZERROR("Unknown exception in smooth_target_output");
    }
}



/*
 * @Name: get_target_direction
 * @Description: 目标朝向角计算
 *
 * @Input
 * (splicing_input_target_vec): 输入数据
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/10/22
 * Time: 10:14
 * Author: WangXing
 * Content: Create
 */
void TrackSplicing::get_target_direction(std::vector<SplicingInputTarget> &splicing_input_target_vec,
                                         DeviceList splicing_devices_list,
                                         long long splicing_timestamp_ms)
{
    for (unsigned int sot = 0; sot < splicing_input_target_vec.size(); sot++)
{
        double road_direction = 0.0f;
        // int direction_ret = roadDirectionCacl->get_road_direction(splicing_input_target_vec[sot].device_id,
        //                                                             splicing_input_target_vec[sot].target_input.target_coor_in_base,
        //                                                             road_direction);
        int direction_ret = laneSmooth->get_lane_target_direction(splicing_input_target_vec[sot].target_input.target_coor_in_base,
                            road_direction);
        if (direction_ret == -1)
        {
            road_direction = 0.0f;
            ZERROR("get_target_direction device_id:%d id:%d direction_ret:%d \n",
                   splicing_input_target_vec[sot].device_id, splicing_input_target_vec[sot].id, direction_ret);
        }
        splicing_input_target_vec[sot].target_input.target.direction = road_direction;
        // continue;

        if (abs(splicing_timestamp_ms - splicing_input_target_vec[sot].target_direction_update_time) > target_direction_update_time_ms)
        {
            splicing_input_target_vec[sot].target_direction_update_time = splicing_timestamp_ms;
            double target_angle_in_base = 0; // 默认目标移动的方向与设备方向为同向
            unsigned int target_path_length = splicing_input_target_vec[sot].target_input_path.size();
            if (target_path_length >= 2)
            {
                CalibPoint p1 = {
                    splicing_input_target_vec[sot].target_input_path[0].target_coor_in_base.x,
                    splicing_input_target_vec[sot].target_input_path[0].target_coor_in_base.y};
                CalibPoint p2 = {
                    splicing_input_target_vec[sot].target_input.target_coor_in_base.x,
                    splicing_input_target_vec[sot].target_input.target_coor_in_base.y};
                // 计算轨迹最后一个点和基坐标系下的点之间的角度
                target_angle_in_base = calculateAngle(p1, p2);
                if (std::isnan(target_angle_in_base))
                {
#ifdef DEBUG1
                    ZINFO("direction is nan, device_id:%d id:%d direction:%.2f p1 x:%.2f y:%.2f p2 x:%.2f y:%.2f \n",
                          splicing_input_target_vec[sot].device_id,
                          splicing_input_target_vec[sot].id,
                          target_angle_in_base,
                          p1.x, p1.y,
                          p2.x, p2.y);
#endif
                    target_angle_in_base = road_direction;
                }
            }
            splicing_input_target_vec[sot].target_input.target.direction = target_angle_in_base;
        }
        else
        {
            // 当目标出现的时刻，车牌号码存在，则赋予方位信息
            if ((splicing_input_target_vec[sot].target_input.target.number_plate[0] != '\0' ||
            splicing_input_target_vec[sot].target_input.target.vehicle_type != VehicleType::VEHICLE_TYPE_UNKNOWN) &&
            splicing_input_target_vec[sot].target_input.target.direction == 0.0f)
            {
                float road_direction = 0.0f;
                int direction_ret = roadDirectionCacl->get_road_direction(splicing_input_target_vec[sot].device_id,
                                                                          splicing_input_target_vec[sot].target_input.target_coor_in_base,
                                                                          road_direction);
                if (direction_ret == -1)
                {
                    road_direction = 0.0f;
                }
                splicing_input_target_vec[sot].target_input.target.direction = road_direction;
            }
        }
        splicing_input_target_vec[sot].target_input.target.distance = sqrt(pow(splicing_input_target_vec[sot].target_input.target.x, 2) + pow(splicing_input_target_vec[sot].target_input.target.y, 2));
    }
}

/*
 * @Name: spiling_target_weight_merge
 * @Description: 拼接目标的融合
 *
 * @Input
 * (splicing_input_target_vec): 输入数据
 * (spilcing_match_ids_vec): 融合数据
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/9/4
 * Time: 13:55
 * Author: WangXing
 * Content: Create
 */
void TrackSplicing::spiling_target_weight_merge(std::vector<SplicingInputTarget> &splicing_input_target_vec_)
{
    std::vector<unsigned int> splicing_input_target_indexs;
    for (unsigned int sit = 0; sit < splicing_input_target_vec_.size(); sit++)
    {
        // 重叠区域的起批成功后目标
        splicing_input_target_indexs.push_back(sit);
    }
    for (size_t smi = 0; smi < spilcing_match_ids_vec.size(); smi++)
    {
        if (spilcing_match_ids_vec[smi].spilcing_batch_flag == false)
        {
            continue;
        }
        bool is_exist_device1_target = false;
        unsigned int device1_target_index = 0;
        bool is_exist_device2_target = false;
        unsigned int device2_target_index = 0;
        for (size_t ti = 0; ti < splicing_input_target_indexs.size(); ti++)
        {
            if (spilcing_match_ids_vec[smi].device1_id == splicing_input_target_vec_[splicing_input_target_indexs[ti]].device_id && spilcing_match_ids_vec[smi].id1 == splicing_input_target_vec_[splicing_input_target_indexs[ti]].id)
            {
                is_exist_device1_target = true;
                device1_target_index = splicing_input_target_indexs[ti];
            }
            if (spilcing_match_ids_vec[smi].device2_id == splicing_input_target_vec_[splicing_input_target_indexs[ti]].device_id && spilcing_match_ids_vec[smi].id2 == splicing_input_target_vec_[splicing_input_target_indexs[ti]].id)
            {
                is_exist_device2_target = true;
                device2_target_index = splicing_input_target_indexs[ti];
            }
            if (is_exist_device1_target == true && is_exist_device2_target == true)
            {
                break;
            }
        }
#ifdef DEBUG
        ZDEBUG("device_id1:%d id1:%d device_id2:%d id2:%d sx:%.2f sy:%.2f\n",
               spilcing_match_ids_vec[smi].device1_id, spilcing_match_ids_vec[smi].id1,
               spilcing_match_ids_vec[smi].device2_id, spilcing_match_ids_vec[smi].id2,
               spilcing_match_ids_vec[smi].splicing_coor.x, spilcing_match_ids_vec[smi].splicing_coor.y);
#endif
        if (is_exist_device1_target == true && is_exist_device2_target == true)
        {
            if (splicing_input_target_vec_[device1_target_index].target_input.target.y > splicing_input_target_vec_[device2_target_index].target_input.target.y)
            {
                splicing_input_target_vec_[device2_target_index].target_input.target_coor_in_base = spilcing_match_ids_vec[smi].splicing_coor;
#ifdef DEBUG
                ZDEBUG("splicing after device_id:%d id:%d tx:%.2f ty:%.2f \n",
                       splicing_input_target_vec[device2_target_index].device_id,
                       splicing_input_target_vec[device2_target_index].id,
                       splicing_input_target_vec[device2_target_index].target_input.target_coor_in_base.x,
                       splicing_input_target_vec[device2_target_index].target_input.target_coor_in_base.y);
#endif
            }
            else
            {
                splicing_input_target_vec_[device1_target_index].target_input.target_coor_in_base = spilcing_match_ids_vec[smi].splicing_coor;
#ifdef DEBUG
                ZDEBUG("splicing after device_id:%d id:%d tx:%.2f ty:%.2f \n",
                       splicing_input_target_vec[device1_target_index].device_id,
                       splicing_input_target_vec[device1_target_index].id,
                       splicing_input_target_vec[device1_target_index].target_input.target_coor_in_base.x,
                       splicing_input_target_vec[device1_target_index].target_input.target_coor_in_base.y);
#endif
            }
        }
    }
}

// 简化函数来估算UTF-8编码下字符的字节大小
// 注意：这个函数不完美，但它能处理ASCII字符和大多数常见的UTF-8多字节字符
size_t TrackSplicing::utf8_char_size(const std::string &str)
{
    if (str.empty())
        return 0; // 空字符串
    if ((unsigned char)str[0] <= 0x7F)
        return 1; // ASCII字符
    if ((unsigned char)str[0] >= 0xC2 && (unsigned char)str[0] <= 0xDF)
        return 2; // 两个字节的UTF-8字符的起始字节范围
    if ((unsigned char)str[0] >= 0xE0 && (unsigned char)str[0] <= 0xEF)
        return 3; // 三个字节的UTF-8字符的起始字节范围
    if ((unsigned char)str[0] >= 0xF0 && (unsigned char)str[0] <= 0xF4)
        return 4; // 四个字节的UTF-8字符的起始字节范围
    // 如果第一个字节不在上述范围内，则字符串可能不是有效的UTF-8编码
    return 0; // 未知或无效字符
}

// 遍历vector中的每个string，并打印每个字符的字节大小
void TrackSplicing::print_char_byte_sizes(const std::vector<std::string> &plate_str)
{
    for (const std::string &str : plate_str)
    {
        ZINFO("str:%s size:%d\n", str.c_str(), str.size());
    }
}

void TrackSplicing::get_splicing_fps(long long timestamp_ms)
{
    if (timestamp_ms - splicing_fps_time_ms > splicing_fps_duration)
    {
        int cur_splicing_fps = splicing_fps_count / int(splicing_fps_duration / 1000);
        int device_callback_fps = cur_splicing_fps;
        if (device_callback_fps > 0)
        {
            splicing_fps = device_callback_fps + 1;
        }
#ifdef DEBUG
        ZINFO("get_splicing_fps channel_id:%d splicing_fps_count:%d  cur_splicing_fps:%d device_callback_fps:%d \n",
              splicing_channel_id, splicing_fps_count, cur_splicing_fps, device_callback_fps);
#endif
        splicing_fps_time_ms = timestamp_ms;
        splicing_fps_count = 0;
    }
    else
    {
        splicing_fps_count++;
    }
}

void TrackSplicing::splicing_output_target_get_direction()
{
    splicing_output_targets.target_cnt = 0;
    unsigned int output_target_count = 0;
    for (unsigned int sot = 0; sot < splicing_output_targets_vec.size(); sot++)
    {
        if (splicing_output_targets_vec[sot].target_output.fusion_id == 0)
        {
            continue;
        }
        float output_base_xcoor = float(splicing_output_targets_vec[sot].target_kalman->kf_update_res[0]);
        float output_base_ycoor = float(splicing_output_targets_vec[sot].target_kalman->kf_update_res[1]);
        CalibPoint update_point = { 
            output_base_xcoor,
            output_base_ycoor};
        // float amend_target_direction = roadDirectionCacl->road_direction_amend(splicing_output_targets_vec[sot].target_output.device_id,
        //                                                                        update_point,
        //                                                                        splicing_output_targets_vec[sot].target_output.target.direction);
        double amend_target_direction = 0.0f;
        int direction_ret = laneSmooth->get_lane_target_direction(update_point,
                            amend_target_direction);
        OutputTarget temp_output_target;
        temp_output_target.splice_id = splicing_output_targets_vec[sot].target_output.fusion_id;
        temp_output_target.device_id = splicing_output_targets_vec[sot].target_output.device_id;
        temp_output_target.splice_target.timestamp_ms = splicing_timestamp_ms;
        temp_output_target.timestamp_ms = splicing_timestamp_ms;
        temp_output_target.splice_target = splicing_output_targets_vec[sot].target_output.target;
        temp_output_target.splicing_x = output_base_xcoor;
        temp_output_target.splicing_y = output_base_ycoor;
        temp_output_target.splice_target.angle = 360.0f - (amend_target_direction - 90.0f);
        temp_output_target.splice_target.distance = splicing_output_targets_vec[sot].target_output.target.distance;
        temp_output_target.splice_target.direction = amend_target_direction;

        double precess_longitude = splicing_output_targets_vec[sot].target_output.target.longitude;
        double precess_latitude = splicing_output_targets_vec[sot].target_output.target.latitude;
        double precess_base_x = output_base_xcoor + splicing_devices_list.device_list[0].x;
        double precess_base_y = output_base_ycoor + splicing_devices_list.device_list[0].y;
        Utils::mercatorToLonLat(precess_base_x, precess_base_y, precess_longitude, precess_latitude);

        // 更新后的经纬度
        temp_output_target.splice_target.longitude = precess_longitude;
        temp_output_target.splice_target.latitude = precess_latitude;
        // 更新前的经纬度
        temp_output_target.longitude_input = splicing_output_targets_vec[sot].target_output.target.longitude;
        temp_output_target.latitude_input = splicing_output_targets_vec[sot].target_output.target.latitude;
        splicing_output_targets.fusion_targets[output_target_count] = temp_output_target;
        output_target_count++;
    }
    splicing_output_targets.target_cnt = output_target_count;
}

/*
 * @Name: spilcing_match_ids_vec_match
 * @Description: 目标匹配权重计算函数
 *              使用多维度特征计算目标间的匹配权重，包括位置、速度、车牌等信息
 *              最终使用KM算法进行最优匹配
 *
 * @Input
 * splicing_overlap_target_vec: 待匹配的目标列表
 * splicing_match_weights: 输出的匹配权重矩阵
 *
 * @Output
 * 返回KM算法计算的最优匹配结果
 */
int *TrackSplicing::spilcing_match_ids_vec_match(std::vector<SplicingInputTarget> splicing_overlap_target_vec,
                                                std::vector<std::vector<double>> &splicing_match_weights)
{
    // 初始化匹配权重矩阵大小
    splicing_match_weights.resize(splicing_overlap_target_vec.size());
    for (unsigned int smw = 0; smw < splicing_match_weights.size(); smw++)
    {
        splicing_match_weights[smw].resize(splicing_overlap_target_vec.size());
    }

    // 遍历每个目标，计算与其他目标的匹配权重
    for (unsigned int r = 0; r < splicing_overlap_target_vec.size(); r++)
    {
        // 获取当前目标的基准坐标点
        CalibPoint splicing_point = {
            splicing_overlap_target_vec[r].target_input.target_coor_in_base.x,
            splicing_overlap_target_vec[r].target_input.target_coor_in_base.y};

        // 获取道路方向信息，用于后续匹配区域的计算
        float road_direction = 0.0f;
        int direction_ret = roadDirectionCacl->get_road_direction(splicing_overlap_target_vec[r].device_id,
                                                                  splicing_point,
                                                                  road_direction);
        if (direction_ret == -1)
        {
            road_direction = 0.0f;
        }

        // 计算一级和二级匹配区域的矩形范围
        // 一级匹配区域：较小范围，用于高置信度匹配
        std::vector<CalibPoint> splicing_rect = calculateRectangleCorners(splicing_point,
                                                                          road_direction,
                                                                          splicing_match_xcoor_distance_theshold,
                                                                          splicing_match_ycoor_distance_theshold);
        // 二级匹配区域：较大范围，用于低置信度匹配
        std::vector<CalibPoint> splicing_rect_second = calculateRectangleCorners(splicing_point,
                                                                                 road_direction,
                                                                                 splicing_batch_xcoor_distance_theshold,
                                                                                 splicing_batch_ycoor_distance_theshold);

// #ifdef DEBUG1
//         ZDEBUG("device1_id:%d id1:%d target_coor_in_base x:%.2f y:%2f road_direction:%.2f batch_xcoor_distance_theshold:%.2f batch_ycoor_distance_theshold:%.2f \n",
//                splicing_overlap_target_vec[r].device_id,
//                splicing_overlap_target_vec[r].id,
//                splicing_point.x,
//                splicing_point.y,
//                road_direction,
//                splicing_match_xcoor_distance_theshold,
//                splicing_match_ycoor_distance_theshold);
// #endif

        // 计算坐标误差阈值，用于二级匹配
        float coor_error_threshold = sqrt(pow(splicing_batch_xcoor_distance_theshold, 2) +
                                        pow(splicing_batch_ycoor_distance_theshold, 2));

        // 与其他目标计算匹配权重
        for (unsigned int d = 0; d < splicing_overlap_target_vec.size(); d++)
        {
            // 初始化默认匹配权重
            double match_weight = spilcing_default_match_weight;

            // 跳过自身匹配
            if (splicing_overlap_target_vec[r].id == splicing_overlap_target_vec[d].id &&
                splicing_overlap_target_vec[r].device_id == splicing_overlap_target_vec[d].device_id)
            {
                splicing_match_weights[r][d] = match_weight;
                continue;
            }

            // 检查设备邻近性约束
            // bool is_near_devices = abs((int)splicing_overlap_target_vec[r].device_id -
            //                          (int)splicing_overlap_target_vec[d].device_id) == 1 ? true : false;
            bool is_near_devices = true;
            // 如果当前设备id为0（代表上一个盒子的最后一个设备），并且目标设备id为第一个的设备的id，则认为邻近
            if ((splicing_overlap_target_vec[r].device_id == 0 &&
                splicing_overlap_target_vec[d].device_id == splicing_devices_list.device_list[0].device_id)
                ||
                (splicing_overlap_target_vec[r].device_id == splicing_devices_list.device_list[0].device_id &&
                splicing_overlap_target_vec[d].device_id == 0))
            {
                is_near_devices = true;
            }
            // 如果当前设备的id不是0，并且设备id相差大于1，则认为不邻近
            else if (abs((int)splicing_overlap_target_vec[r].device_id -
                    (int)splicing_overlap_target_vec[d].device_id) > 1)
            {
                is_near_devices = false;
            }
            // 如果是同一个设备，则认为不近邻
            else if (splicing_overlap_target_vec[r].device_id == splicing_overlap_target_vec[d].device_id)
            {
                is_near_devices = false;
            }
            if (is_near_devices == false)
            {
                splicing_match_weights[r][d] = match_weight;
                continue;
            }

            // 检查时间约束
            long long r_time_ms = splicing_overlap_target_vec[r].target_input.time_ms;
            long long d_time_ms = splicing_overlap_target_vec[d].target_input.time_ms;
            if (abs(r_time_ms - d_time_ms) > splicing_match_time_threshold)
            {
                splicing_match_weights[r][d] = match_weight;
                continue;
            }

            // 检查速度约束
            float yspeed_r = splicing_overlap_target_vec[r].target_input.target.speed;
            float yspeed_d = splicing_overlap_target_vec[d].target_input.target.speed;

            // 避免相同同一个设备的不同速度的目标进行匹配 主要针对双向车道的情况
            if (splicing_overlap_target_vec[r].device_id == splicing_overlap_target_vec[d].device_id &&
                splicing_overlap_target_vec[r].target_input.target.speed * splicing_overlap_target_vec[d].target_input.target.speed < 0)
            {
                splicing_match_weights[r][d] = match_weight;
                continue;
            }
#ifdef SHOOTING_SOLUTION
            // 对射方案：速度方向相同时不匹配
            if (yspeed_r * yspeed_d > 0)
            {
                splicing_match_weights[r][d] = match_weight;
                continue;
            }
#elif FRONT_BACK_SOLUTION
            // 前后方案：速度方向不同或差值过大时不匹配
            if (yspeed_r * yspeed_d < 0 || fabsf(yspeed_r - yspeed_d) > splicing_match_speed_threshold)
            {
                splicing_match_weights[r][d] = match_weight;
                continue;
            }
#endif

            // 检查目标方向约束
            if (fabs(splicing_overlap_target_vec[r].target_input.target.direction) < 1e-4 ||
                fabs(splicing_overlap_target_vec[d].target_input.target.direction) < 1e-4)
            {
                splicing_match_weights[r][d] = match_weight;
                continue;
            }

            // 检查方向误差约束
            if (fabs(splicing_overlap_target_vec[r].target_input.target.direction -
                    splicing_overlap_target_vec[d].target_input.target.direction) > splicing_match_direction_threshold)
            {
                splicing_match_weights[r][d] = match_weight;
                continue;
            }

            // 一级匹配区域判断
            bool target_in_rect = judgePointInRect(splicing_rect,
                                                 splicing_overlap_target_vec[d].target_input.target_coor_in_base);
            if (target_in_rect == true)
            {
                // 车牌匹配判断
                float plate_score = 0;
                int ck_ret = checkout_plate_ipentity(
                    splicing_overlap_target_vec[r].target_input.target.number_plate,
                    splicing_overlap_target_vec[d].target_input.target.number_plate,
                    splicing_plate_match_top1_score,
                    splicing_plate_match_top2_score,
                    plate_score);

                // 车牌匹配成功，使用车牌相似度作为权重
                if (ck_ret == 0)
                {
                    splicing_match_weights[r][d] = spilcing_default_match_weight * (1.0f - plate_score);
                    // 保留原有的调试信息
                    ZINFO("splcing_target_match SUCCESS PLATE_MATCH, ...");
                }
                else
                {
                    // 车型和颜色匹配判断
                    float splicing_speed_error = fabs(splicing_overlap_target_vec[r].target_input.target.speed -
                                                    splicing_overlap_target_vec[d].target_input.target.speed);
                    if (splicing_overlap_target_vec[r].target_input.target.vehicle_type ==
                        splicing_overlap_target_vec[d].target_input.target.vehicle_type &&
                        splicing_overlap_target_vec[r].target_input.target.vehicle_color ==
                        splicing_overlap_target_vec[d].target_input.target.vehicle_color &&
                        splicing_overlap_target_vec[r].target_input.target.vehicle_type !=
                        VehicleType::VEHICLE_TYPE_UNKNOWN &&
                        splicing_overlap_target_vec[r].target_input.target.vehicle_color !=
                        VehicleColor::VEHICLE_COLOR_UNKNOWN)
                    {
                        // 车型颜色匹配成功
                        splicing_match_weights[r][d] = spilcing_default_match_weight * (1.0f - lose_plate_top2_score);
                        // 保留原有的调试信息
                        #ifdef DEBUG
                        ZDEBUG("splcing_target_match SUCCESS VEHICLE AND COLOR, ...");
                        #endif
                        continue;
                    }
                    else if (splicing_speed_error < splicing_match_speed_threshold)
                    {
                        // 速度匹配判断
                        double splicing_speed_weight = (splicing_match_speed_threshold - splicing_speed_error) /
                                                     splicing_match_speed_threshold;
                        splicing_match_weights[r][d] = spilcing_default_match_weight * (1.0f - splicing_speed_weight);
                        // 保留原有的调试信息
                        #ifdef DEBUG
                        ZDEBUG("splcing_target_match SUCCESS SPEED_MATCH, ...");
                        #endif
                    }
                    else
                    {
                        // 二级匹配区域判断
                        bool target_in_rect_second = judgePointInRect(splicing_rect_second,
                                                   splicing_overlap_target_vec[d].target_input.target_coor_in_base);
                        if (target_in_rect_second == true)
                        {
                            // 计算坐标误差权重
                            float temp_splicing_x_error = fabs(
                                splicing_overlap_target_vec[r].target_input.target_coor_in_base.x -
                                splicing_overlap_target_vec[d].target_input.target_coor_in_base.x);
                            float temp_splicing_y_error = fabs(
                                splicing_overlap_target_vec[r].target_input.target_coor_in_base.y -
                                splicing_overlap_target_vec[d].target_input.target_coor_in_base.y);
                            float temp_splicing_coor_error = sqrt(pow(temp_splicing_x_error, 2) +
                                                                pow(temp_splicing_y_error, 2));

                            double splicing_coor_weight = (coor_error_threshold - temp_splicing_coor_error) /
                                                        coor_error_threshold;
                            splicing_match_weights[r][d] = spilcing_default_match_weight *
                                                         (1.0f - splicing_coor_weight);

                            #ifdef DEBUG
                            ZINFO("splcing_target_match SUCCESS COOR_MATCH, ...");
                            #endif
                        }
                        else
                        {
                            // 所有匹配条件都不满足，使用默认权重
                            splicing_match_weights[r][d] = spilcing_default_match_weight * (1.0f - 0.0f);
                        }
                    }
                }
            }
            else
            {
                // 目标不在匹配区域内，使用默认权重
                splicing_match_weights[r][d] = match_weight;
            }
        }
    }

    // 使用KM算法计算最优匹配
    splicing_km->init(static_cast<int>(splicing_match_weights.size()), splicing_match_weights);
    splicing_km->KM_Calc();
    double splicing_match_weights_sum = splicing_km->getdiffSum();
    int *splicing_match = splicing_km->getMatch();
    return splicing_match;
}

void TrackSplicing::spilcing_match_ids_vec_clean()
{
    for (unsigned int smi = 0; smi < spilcing_match_ids_vec.size(); smi++)
    {
        std::vector<float>().swap(spilcing_match_ids_vec[smi].spilcing_match_score_vec);
    }
    std::vector<SpilcingMatchIds>().swap(spilcing_match_ids_vec);
}

/*
 * @Name: spilcing_match_ids_vec_update
 * @Description: 更新目标拼接匹配关系的主函数
 *              处理重叠区域的目标匹配、批处理和误差修正
 */
void TrackSplicing::spilcing_match_ids_vec_update()
{
    // 对未完成批处理的匹配项进行计数
    for (unsigned int mt = 0; mt < spilcing_match_ids_vec.size(); mt++)
    {
        if (spilcing_match_ids_vec[mt].spilcing_batch_flag == false)
        {
            spilcing_match_ids_vec[mt].splicing_batch_count++;
        }
    }

    // 创建两个向量分别存储未完成起批和已完成起批的目标
    std::vector<SplicingInputTarget> splicing_overlap_batch_target_vec;  // 未完成起批的目标
    std::vector<SplicingInputTarget> splicing_overlap_match_target_vec;  // 完成起批的目标

    // 遍历所有输入目标，将其分类到对应的向量中
    for (unsigned int sit = 0; sit < splicing_input_target_vec.size(); sit++)
    {
        // 检查目标是否已经完成批处理匹配
        bool batch_success_target = false;
        for (unsigned int mt = 0; mt < spilcing_match_ids_vec.size(); mt++)
        {
            // 跳过未完成批处理的匹配项
            if (spilcing_match_ids_vec[mt].spilcing_batch_flag == false)
            {
                continue;
            }
            // 检查当前目标是否已存在于已批处理的匹配对中
            if ((spilcing_match_ids_vec[mt].device1_id == splicing_input_target_vec[sit].device_id &&
                 spilcing_match_ids_vec[mt].id1 == splicing_input_target_vec[sit].id) ||
                (spilcing_match_ids_vec[mt].device2_id == splicing_input_target_vec[sit].device_id &&
                 spilcing_match_ids_vec[mt].id2 == splicing_input_target_vec[sit].id))
            {
                batch_success_target = true;
                break;
            }
        }
        // 根据目标状态将其添加到对应向量中
        if (batch_success_target == true)
        {
            splicing_overlap_match_target_vec.push_back(splicing_input_target_vec[sit]);
        }
        else
        {
            splicing_overlap_batch_target_vec.push_back(splicing_input_target_vec[sit]);
        }
    }

    // 使用二分图算法对重叠区域的目标进行匹配 包括多级关联门
    std::vector<std::vector<double>> splicing_match_weights;
    int *splicing_match = spilcing_match_ids_vec_match(splicing_overlap_batch_target_vec, splicing_match_weights);

#ifdef DEBUG1
    ZINFO("splicing_overlap_batch_target_vec size:%d splicing_overlap_match_target_vec size:%d\n",
          splicing_overlap_batch_target_vec.size(), splicing_overlap_match_target_vec.size());
    for (unsigned int i = 0; i < splicing_overlap_batch_target_vec.size(); i++)
    {
        ZINFO("splicing_overlap_batch_target_vec[%d].device_id:%d id:%d weight[d]:%f\n",
              i, splicing_overlap_batch_target_vec[i].device_id, splicing_overlap_batch_target_vec[i].id, splicing_match_weights[splicing_match[i]][i]);
    }
    ZINFO("splicing_overlap_match_target_vec size:%d\n", splicing_overlap_match_target_vec.size());
    for (unsigned int i = 0; i < splicing_overlap_match_target_vec.size(); i++)
    {
        ZINFO("splicing_overlap_match_target_vec[%d].device_id:%d id:%d\n",
              i, splicing_overlap_match_target_vec[i].device_id, splicing_overlap_match_target_vec[i].id);
    }
#endif

    // 根据匹配结果更新批处理匹配信息
    spilcing_batch_id_update(splicing_overlap_batch_target_vec, splicing_match_weights, splicing_match);

    // 处理已匹配目标的关联关系
    spilcing_match_id_update(splicing_overlap_match_target_vec);

    // 修正重叠区域内的误匹配情况
    splicing_mismatch_correct(splicing_overlap_match_target_vec);

    // 执行批处理确认流程
    spilcing_match_id_vec_batch();
}
/*
 * @Name: splicing_mismatch_correct
 * @Description: 修正目标拼接过程中可能出现的误匹配情况
 *              处理已完成批处理但长时间未更新的匹配对
 *              通过检查和修正可能的误匹配，提高系统的鲁棒性
 *
 * @Input
 * splicing_overlap_target_vec: 重叠区域的目标向量
 *
 * @Edit History
 * Date: 2025/02/25
 * Time: 14:33
 * Author: JiaTao
 * Content: 添加详细注释
 */
void TrackSplicing::splicing_mismatch_correct(std::vector<SplicingInputTarget> splicing_overlap_target_vec)
{
    // 遍历所有匹配对
    for (unsigned int smi = 0; smi < spilcing_match_ids_vec.size(); smi++)
    {
        // 跳过未完成批处理的匹配对，只处理已完成批处理的匹配对
        if (spilcing_match_ids_vec[smi].spilcing_batch_flag == false)
        {
            continue;
        }

        // 检查匹配对是否长时间未更新（超过阈值），如果是则可能需要修正
        if (abs(splicing_timestamp_ms - spilcing_match_ids_vec[smi].splicing_update_time) > splicing_batch_lose_time_threshold)
        {
            // 1. 判断两个目标是否都存在
            bool exist_device1 = false;                // 第一个设备目标是否存在
            unsigned int device1_target_index = 0;     // 第一个设备目标的索引
            bool exist_device2 = false;                // 第二个设备目标是否存在
            unsigned int device2_target_index = 0;     // 第二个设备目标的索引

            // 遍历重叠区域的所有目标，查找匹配对中的两个目标
            for (unsigned int sot = 0; sot < splicing_overlap_target_vec.size(); sot++)
            {
                // 当目标不在稳定跟踪区域内时跳过（y坐标超过阈值）
                if (splicing_overlap_target_vec[sot].target_input.target.y > splicing_device_stably_track_distance_threshold)
                {
#ifdef DEBUG1
                    ZINFO("device_id:%d id:%d is not TARGET_IN_TRACKING \n",
                          splicing_overlap_target_vec[sot].device_id,
                          splicing_overlap_target_vec[sot].id);
#endif
                    continue;
                }

                // 检查当前目标是否是匹配对中的第一个目标
                if (spilcing_match_ids_vec[smi].device1_id == splicing_overlap_target_vec[sot].device_id && spilcing_match_ids_vec[smi].id1 == splicing_overlap_target_vec[sot].id)
                {
                    exist_device1 = true;
                    device1_target_index = sot;
                    continue;
                }

                // 检查当前目标是否是匹配对中的第二个目标
                if (spilcing_match_ids_vec[smi].device2_id == splicing_overlap_target_vec[sot].device_id && spilcing_match_ids_vec[smi].id2 == splicing_overlap_target_vec[sot].id)
                {
                    exist_device2 = true;
                    device2_target_index = sot;
                    continue;
                }

                // 如果两个目标都已找到，则结束遍历
                if (exist_device1 == true && exist_device2 == true)
                {
                    break;
                }
            }

            // 如果两个目标都存在，则进行误匹配修正
            if (exist_device1 == true && exist_device2 == true)
            {
#ifdef DEBUG1
                ZDEBUG("splicing_mismatch_correct,MATCH_IDS c_id:%d id1:%d dv_id1:%d id2:%d dv_id2:%d \n",
                       splicing_channel_id, splicing_overlap_target_vec[device1_target_index].id, splicing_overlap_target_vec[device1_target_index].device_id,
                       splicing_overlap_target_vec[device2_target_index].id, splicing_overlap_target_vec[device2_target_index].device_id);
#endif
                // 当存在两个目标时，则回退目标到之前的目标
                // 遍历输出目标列表，查找与匹配对中目标相对应的输出目标
                for (unsigned int sotv = 0; sotv < splicing_output_targets_vec.size(); sotv++)
                {
                    // 如果找到与第一个设备目标对应的输出目标
                    if (splicing_output_targets_vec[sotv].target_output.id == splicing_overlap_target_vec[device1_target_index].id && splicing_output_targets_vec[sotv].target_output.device_id == splicing_overlap_target_vec[device1_target_index].device_id)
                    {
                        // 创建临时目标用于修正
                        SplicingTarget target_output_correct;
                        target_output_correct = splicing_output_targets_vec[sotv];

                        // 将输出目标的信息替换为第二个设备目标的信息
                        target_output_correct.target_output.device_id = splicing_overlap_target_vec[device2_target_index].device_id;
                        target_output_correct.target_output.id = splicing_overlap_target_vec[device2_target_index].id;
                        target_output_correct.target_output.target = splicing_overlap_target_vec[device2_target_index].target_input.target;
                        target_output_correct.target_output.target_area_status = splicing_overlap_target_vec[device2_target_index].target_area_status;
                        target_output_correct.target_output.target_coor_in_base = splicing_overlap_target_vec[device2_target_index].target_input.target_coor_in_base;
#ifdef DEBUG1
                        ZDEBUG("splicing_mismatch_correct,CORRECT_BEFORE c_id:%d f_id:%d id:%d dv_id:%d p_number:%s \n",
                               splicing_channel_id, splicing_output_targets_vec[sotv].target_output.fusion_id, splicing_output_targets_vec[sotv].target_output.id,
                               splicing_output_targets_vec[sotv].target_output.device_id, splicing_output_targets_vec[sotv].target_output.target.number_plate);
#endif
                        // 更新输出目标
                        splicing_output_targets_vec[sotv] = target_output_correct;

#ifdef DEBUG1
                        ZDEBUG("splicing_mismatch_correct,CORRECT_AFTER c_id:%d f_id:%d id:%d dv_id:%d p_number:%s \n",
                               splicing_channel_id, splicing_output_targets_vec[sotv].target_output.fusion_id, splicing_output_targets_vec[sotv].target_output.id,
                               splicing_output_targets_vec[sotv].target_output.device_id, splicing_output_targets_vec[sotv].target_output.target.number_plate);
#endif
                        break;
                    }

                    // 如果找到与第二个设备目标对应的输出目标
                    if (splicing_output_targets_vec[sotv].target_output.id == splicing_overlap_target_vec[device2_target_index].id && splicing_output_targets_vec[sotv].target_output.device_id == splicing_overlap_target_vec[device2_target_index].device_id)
                    {
                        // 创建临时目标用于修正
                        SplicingTarget target_output_correct;
                        target_output_correct = splicing_output_targets_vec[sotv];

                        // 将输出目标的信息替换为第一个设备目标的信息
                        target_output_correct.target_output.device_id = splicing_overlap_target_vec[device1_target_index].device_id;
                        target_output_correct.target_output.id = splicing_overlap_target_vec[device1_target_index].id;
                        target_output_correct.target_output.target = splicing_overlap_target_vec[device1_target_index].target_input.target;
                        target_output_correct.target_output.target_area_status = splicing_overlap_target_vec[device1_target_index].target_area_status;
                        target_output_correct.target_output.target_coor_in_base = splicing_overlap_target_vec[device1_target_index].target_input.target_coor_in_base;
#ifdef DEBUG1
                        ZDEBUG("splicing_mismatch_correct,CORRECT_BEFORE c_id:%d f_id:%d id:%d dv_id:%d p_number:%s \n",
                               splicing_channel_id, splicing_output_targets_vec[sotv].target_output.fusion_id, splicing_output_targets_vec[sotv].target_output.id,
                               splicing_output_targets_vec[sotv].target_output.device_id, splicing_output_targets_vec[sotv].target_output.target.number_plate);
#endif
                        // 更新输出目标
                        splicing_output_targets_vec[sotv] = target_output_correct;
#ifdef DEBUG1
                        ZDEBUG("splicing_mismatch_correct,CORRECT_AFTER c_id:%d f_id:%d id:%d dv_id:%d p_number:%s \n",
                               splicing_channel_id, splicing_output_targets_vec[sotv].target_output.fusion_id, splicing_output_targets_vec[sotv].target_output.id,
                               splicing_output_targets_vec[sotv].target_output.device_id, splicing_output_targets_vec[sotv].target_output.target.number_plate);
#endif

                        break;
                    }
                }
            }
            else
            {
                // 只存在一个目标时，则不进行处理
            }
        }
    }
}

/*
 * @Name: spilcing_batch_id_update
 * @Description: 更新目标批处理匹配信息
 *              根据匹配权重更新或创建目标匹配对，维护匹配状态和相关参数
 *
 * @Input
 * splicing_overlap_target_vec: 重叠区域的目标列表
 * splicing_match_weights: 目标间的匹配权重矩阵
 * splicing_match: KM算法得到的最优匹配结果
 */
void TrackSplicing::spilcing_batch_id_update(std::vector<SplicingInputTarget> splicing_overlap_target_vec,
                                            std::vector<std::vector<double>> splicing_match_weights,
                                            int *splicing_match)
{
    // 遍历所有目标，处理匹配结果
    for (unsigned int d = 0; d < splicing_overlap_target_vec.size(); d++)
    {
        // 获取当前匹配对的权重
        float max_weight = float(splicing_match_weights[splicing_match[d]][d]);

        // 判断匹配权重是否满足阈值要求
        if (max_weight < 100 * (1 - splicing_match_score_threshold))
        {
            // 检查该匹配对是否已存在于匹配列表中
            bool is_exist_splicing_match_ids = false;
            unsigned int exist_splicing_match_ids_index = 0;
            for (unsigned int smi = 0; smi < spilcing_match_ids_vec.size(); smi++)
            {
                // 检查正向匹配关系（device1->device2）
                if (spilcing_match_ids_vec[smi].device1_id == splicing_overlap_target_vec[splicing_match[d]].device_id &&
                    spilcing_match_ids_vec[smi].id1 == splicing_overlap_target_vec[splicing_match[d]].id &&
                    spilcing_match_ids_vec[smi].device2_id == splicing_overlap_target_vec[d].device_id &&
                    spilcing_match_ids_vec[smi].id2 == splicing_overlap_target_vec[d].id)
                {
                    is_exist_splicing_match_ids = true;
                    exist_splicing_match_ids_index = smi;
#ifdef DEBUG1
                    ZINFO("spilcing_batch_id_update,EXIST_MATCH_IDS(1->2) device1_id:%d id1:%d device2_id:%d id2:%d\n",
                          spilcing_match_ids_vec[smi].device1_id, spilcing_match_ids_vec[smi].id1,
                          spilcing_match_ids_vec[smi].device2_id, spilcing_match_ids_vec[smi].id2);
#endif
                    break;
                }
                // 检查反向匹配关系（device2->device1）
                if (spilcing_match_ids_vec[smi].device1_id == splicing_overlap_target_vec[d].device_id &&
                    spilcing_match_ids_vec[smi].id1 == splicing_overlap_target_vec[d].id &&
                    spilcing_match_ids_vec[smi].device2_id == splicing_overlap_target_vec[splicing_match[d]].device_id &&
                    spilcing_match_ids_vec[smi].id2 == splicing_overlap_target_vec[splicing_match[d]].id)
                {
                    is_exist_splicing_match_ids = true;
                    exist_splicing_match_ids_index = smi;
#ifdef DEBUG1
                    ZINFO("spilcing_batch_id_update,EXIST_MATCH_IDS(2->1) device1_id:%d id1:%d device2_id:%d id2:%d\n",
                          spilcing_match_ids_vec[smi].device1_id, spilcing_match_ids_vec[smi].id1,
                          spilcing_match_ids_vec[smi].device2_id, spilcing_match_ids_vec[smi].id2);
#endif
                    break;
                }
            }

            // 计算匹配目标间的位置误差
            float x_error = splicing_overlap_target_vec[splicing_match[d]].target_input.target_coor_in_base.x -
                          splicing_overlap_target_vec[d].target_input.target_coor_in_base.x;
            float y_error = splicing_overlap_target_vec[splicing_match[d]].target_input.target_coor_in_base.y -
                          splicing_overlap_target_vec[d].target_input.target_coor_in_base.y;

            // 计算匹配目标的基准坐标
            CalibPoint splicing_base_coor = spilcing_coor_cacl(splicing_overlap_target_vec[splicing_match[d]],
                                                              splicing_overlap_target_vec[d]);

            if (is_exist_splicing_match_ids == true)
            {
                // 更新已存在的匹配对信息
                if (spilcing_match_ids_vec[exist_splicing_match_ids_index].spilcing_batch_flag == false)
                {
                    // 如果未完成批处理，添加新的匹配分数
                    spilcing_match_ids_vec[exist_splicing_match_ids_index].spilcing_match_score_vec.push_back(
                        float((100.0 - max_weight) / 100.0));
                }
                // 更新匹配对的各项参数
                spilcing_match_ids_vec[exist_splicing_match_ids_index].batch_x_error = x_error;
                spilcing_match_ids_vec[exist_splicing_match_ids_index].batch_y_error = y_error;
                // 计算平均速度
                spilcing_match_ids_vec[exist_splicing_match_ids_index].speed =
                    (splicing_overlap_target_vec[splicing_match[d]].target_input.target.speed +
                     splicing_overlap_target_vec[d].target_input.target.speed) / 2.0f;
                spilcing_match_ids_vec[exist_splicing_match_ids_index].splicing_update_time = splicing_timestamp_ms;
                spilcing_match_ids_vec[exist_splicing_match_ids_index].splicing_coor = splicing_base_coor;
            }
            else
            {
                // 创建新的匹配对
                SpilcingMatchIds temp_splicing_match_ids;
                // 设置目标ID和设备ID
                temp_splicing_match_ids.device1_id = splicing_overlap_target_vec[d].device_id;
                temp_splicing_match_ids.id1 = splicing_overlap_target_vec[d].id;
                temp_splicing_match_ids.device2_id = splicing_overlap_target_vec[splicing_match[d]].device_id;
                temp_splicing_match_ids.id2 = splicing_overlap_target_vec[splicing_match[d]].id;
                // 计算平均速度
                temp_splicing_match_ids.speed =
                    (splicing_overlap_target_vec[splicing_match[d]].target_input.target.speed +
                     splicing_overlap_target_vec[d].target_input.target.speed) / 2.0f;
                // 设置位置误差
                temp_splicing_match_ids.batch_x_error = x_error;
                temp_splicing_match_ids.batch_y_error = y_error;
                // 设置时间戳
                temp_splicing_match_ids.splicing_start_time = splicing_timestamp_ms;
                temp_splicing_match_ids.splicing_update_time = splicing_timestamp_ms;
                // 设置基准坐标
                temp_splicing_match_ids.splicing_coor = splicing_base_coor;
                // 初始化批处理相关参数
                temp_splicing_match_ids.spilcing_batch_flag = false;
                temp_splicing_match_ids.splicing_batch_count = 1;
                temp_splicing_match_ids.spilcing_match_score_vec.push_back(float((100.0 - max_weight) / 100.0));
                // 添加到匹配列表
                spilcing_match_ids_vec.push_back(temp_splicing_match_ids);
#ifdef DEBUG1
                ZDEBUG("spilcing_batch_id_update,NEW_MATCH_IDS device1_id:%d id:%d device2_id:%d id2:%d max_weight:%.2f \n",
                       splicing_overlap_target_vec[d].device_id, splicing_overlap_target_vec[d].id,
                       splicing_overlap_target_vec[splicing_match[d]].device_id, splicing_overlap_target_vec[splicing_match[d]].id,
                       max_weight);
#endif
            }
        }
    }
}

void TrackSplicing::spilcing_match_id_update(std::vector<SplicingInputTarget> splicing_overlap_target_vec)
{
    // 遍历所有匹配ID向量
    for (unsigned int smi = 0; smi < spilcing_match_ids_vec.size(); smi++)
    {
        // 只处理已经批处理标记的匹配项
        if (spilcing_match_ids_vec[smi].spilcing_batch_flag == true)
        {
            // 初始化设备1的基准坐标和存在标志
            float device1_x_base_coor = 0;
            float device1_y_base_coor = 0;
            bool exist_device1 = false;
            unsigned int device1_target_index = 0;

            // 初始化设备2的基准坐标和存在标志
            float device2_x_base_coor = 0;
            float device2_y_base_coor = 0;
            bool exist_device2 = false;
            unsigned int device2_target_index = 0;

            // 在重叠目标向量中查找匹配的设备和ID
            for (unsigned int sot = 0; sot < splicing_overlap_target_vec.size(); sot++)
            {
                // 检查是否找到设备1的目标
                if (spilcing_match_ids_vec[smi].device1_id == splicing_overlap_target_vec[sot].device_id &&
                    spilcing_match_ids_vec[smi].id1 == splicing_overlap_target_vec[sot].id)
                {
                    exist_device1 = true;
                    // 记录设备1目标在基准坐标系中的位置
                    device1_x_base_coor = splicing_overlap_target_vec[sot].target_input.target_coor_in_base.x;
                    device1_y_base_coor = splicing_overlap_target_vec[sot].target_input.target_coor_in_base.y;
                    device1_target_index = sot;
                    continue;
                }

                // 检查是否找到设备2的目标
                if (spilcing_match_ids_vec[smi].device2_id == splicing_overlap_target_vec[sot].device_id &&
                    spilcing_match_ids_vec[smi].id2 == splicing_overlap_target_vec[sot].id)
                {
                    exist_device2 = true;
                    // 记录设备2目标在基准坐标系中的位置
                    device2_x_base_coor = splicing_overlap_target_vec[sot].target_input.target_coor_in_base.x;
                    device2_y_base_coor = splicing_overlap_target_vec[sot].target_input.target_coor_in_base.y;
                    device2_target_index = sot;
                    continue;
                }

                // 如果两个设备的目标都已找到，则跳出循环
                if (exist_device1 == true && exist_device2 == true)
                {
                    break;
                }
            }

            // 当两个设备的目标都存在时进行处理
            if (exist_device1 == true && exist_device2 == true)
            {
                // 检查两个目标的车牌号是否一致
                float plate_score = 0.0f;
                int ch_ret = checkout_plate_ipentity(splicing_overlap_target_vec[device1_target_index].target_input.target.number_plate,
                                                     splicing_overlap_target_vec[device2_target_index].target_input.target.number_plate,
                                                     0.0f,
                                                     0.0f,
                                                     plate_score);

                // 计算两个目标的拼接基准坐标
                CalibPoint splicing_base_coor = spilcing_coor_cacl(splicing_overlap_target_vec[device1_target_index],
                                                                  splicing_overlap_target_vec[device2_target_index]);
                // 更新拼接坐标
                spilcing_match_ids_vec[smi].splicing_coor = splicing_base_coor;

                // 如果车牌号一致，直接更新时间戳
                if (ch_ret == 0)
                {
                    spilcing_match_ids_vec[smi].splicing_update_time = splicing_timestamp_ms;
                }
                else
                {
                    // 车牌号不一致时，需要进一步验证位置关系

                    // 计算道路方向以确定坐标系斜率
                    float road_direction = 0.0f;
                    int direction_ret = roadDirectionCacl->get_road_direction(splicing_overlap_target_vec[device1_target_index].device_id,
                                                                              splicing_overlap_target_vec[device1_target_index].target_input.target_coor_in_base,
                                                                              road_direction);
                    // 如果获取道路方向失败，默认为0
                    if (direction_ret == -1)
                    {
                        road_direction = 0.0f;
                    }

                    // 根据设备1目标位置和道路方向，计算一个矩形区域
                    std::vector<CalibPoint> splicing_rect = calculateRectangleCorners(
                        splicing_overlap_target_vec[device1_target_index].target_input.target_coor_in_base,
                        road_direction,
                        splicing_match_xcoor_distance_theshold,
                        splicing_match_ycoor_distance_theshold * 1.5f);  // 对于已经匹配成功的目标，放宽匹配范围 TODO 待验证

                    // 判断设备2的目标是否在这个矩形区域内
                    bool target_in_rect = judgePointInRect(splicing_rect,
                                                          splicing_overlap_target_vec[device2_target_index].target_input.target_coor_in_base);

                    // 如果设备2的目标在矩形区域内，则更新时间戳
                    if (target_in_rect == true)
                    {
                        spilcing_match_ids_vec[smi].splicing_update_time = splicing_timestamp_ms;
                    }
                    else
                    {
                        // 位置偏差太大，且车牌号码不相同，不更新时间戳
#ifdef DEBUG1
                        ZINFO("device_id:%d id:%d is not TARGET_IN_RECT(id:%d device_id:%d) don't update time bx:%.2f by:%.2f rect:(%.2f,%.2f),(%.2f,%.2f),(%.2f,%.2f),(%.2f,%.2f) \n",
                              splicing_overlap_target_vec[device1_target_index].device_id,
                              splicing_overlap_target_vec[device1_target_index].id,
                              splicing_overlap_target_vec[device2_target_index].id,
                              splicing_overlap_target_vec[device2_target_index].device_id,
                              splicing_overlap_target_vec[device2_target_index].target_input.target_coor_in_base.x,
                              splicing_overlap_target_vec[device2_target_index].target_input.target_coor_in_base.y,
                              splicing_rect[0].x, splicing_rect[0].y, splicing_rect[1].x, splicing_rect[1].y,
                              splicing_rect[2].x, splicing_rect[2].y, splicing_rect[3].x, splicing_rect[3].y);
#endif
                    }
                }
            }
        }
    }
}

/*
 * @Name: spilcing_coor_cacl
 * @Description: 拼接目标位置的加权计算
 *
 * @Input
 * device1_target: 设备1探测的目标信息
 * device2_target: 设备2探测的目标信息
 * splicing_area_vec_: 目标的所占区域的判断
 *
 * @Output
 * CalibPoint: 拼接后的目标信息
 *
 * @Edit History
 * Date: 2024/9/4
 * Time: 10:32
 * Author: WangXing
 * Content: Create
 */
CalibPoint TrackSplicing::spilcing_coor_cacl(SplicingInputTarget device1_target, SplicingInputTarget device2_target)
{
    float splicing_rate = 0.5f;
    CalibPoint splicing_coor = device1_target.target_input.target_coor_in_base;

    splicing_coor.x = (1.0 - splicing_rate) * device1_target.target_input.target_coor_in_base.x + splicing_rate * device2_target.target_input.target_coor_in_base.x;
    splicing_coor.y = (1.0 - splicing_rate) * device1_target.target_input.target_coor_in_base.y + splicing_rate * device2_target.target_input.target_coor_in_base.y;

    return splicing_coor;
}

void TrackSplicing::spilcing_match_id_vec_batch()
{
    std::vector<SpilcingMatchIds>::iterator spilcing_match_ids_vec_iter;
    for (spilcing_match_ids_vec_iter = spilcing_match_ids_vec.begin(); spilcing_match_ids_vec_iter != spilcing_match_ids_vec.end();)
    {
        if (spilcing_match_ids_vec_iter->spilcing_batch_flag == false)
        {
            long long batch_time = splicing_timestamp_ms - spilcing_match_ids_vec_iter->splicing_start_time;
            if (batch_time < splicing_batch_min_time)
            {
                // 时间累计
                spilcing_match_ids_vec_iter++;
            }
            else if (batch_time >= splicing_batch_min_time && batch_time <= splicing_batch_max_time)
            {
                unsigned int match_length = spilcing_match_ids_vec_iter->spilcing_match_score_vec.size();
                float match_plate_score = match_length > 0 ? spilcing_match_ids_vec_iter->spilcing_match_score_vec[match_length - 1] : splicing_batch_score_threshold;
                if (fabsf(match_plate_score - splicing_plate_match_top1_score) < 1e-4 || fabsf(match_plate_score - splicing_plate_match_top2_score) < 1e-4)
                {
                    spilcing_match_ids_vec_iter->spilcing_batch_flag = true;
#ifdef DEBUG1
                    ZDEBUG("spilcing_match_id_vec_update PLATE BATCH SUCCESS 1,channel_id:%d device1_id:%d id1:%d device2_id:%d id2:%d speed:%f x_error:%f y_error:%f\n",
                           splicing_channel_id, spilcing_match_ids_vec_iter->device1_id, spilcing_match_ids_vec_iter->id1, spilcing_match_ids_vec_iter->device2_id, spilcing_match_ids_vec_iter->id2,
                           spilcing_match_ids_vec_iter->speed, spilcing_match_ids_vec_iter->batch_x_error, spilcing_match_ids_vec_iter->batch_y_error);
#endif
                    trackFusionManger::get_match_error(splicing_channel_id, spilcing_match_ids_vec_iter->id1, spilcing_match_ids_vec_iter->id2,
                                                       spilcing_match_ids_vec_iter->batch_x_error, spilcing_match_ids_vec_iter->batch_y_error, tFusionManger);
                }
                else
                {
                    if (batch_time > (splicing_batch_min_time + splicing_batch_max_time) / 2)
                    {
                        float mean_match_score = 0;
                        if (spilcing_match_ids_vec_iter->spilcing_match_score_vec.size() > 0)
                        {
                            float sum_match_score = 0;
                            for (unsigned int sms = 0; sms < spilcing_match_ids_vec_iter->spilcing_match_score_vec.size(); sms++)
                            {
                                sum_match_score += spilcing_match_ids_vec_iter->spilcing_match_score_vec[sms];
                            }
                            mean_match_score = sum_match_score / spilcing_match_ids_vec_iter->spilcing_match_score_vec.size();
                        }
                        float batch_count_rate = float(spilcing_match_ids_vec_iter->spilcing_match_score_vec.size() / 2) / float(spilcing_match_ids_vec_iter->splicing_batch_count);
                        if (mean_match_score >= splicing_batch_score_threshold && batch_count_rate >= splicing_batch_count_rate_threshold)
                        {
                            spilcing_match_ids_vec_iter->spilcing_batch_flag = true;
#ifdef DEBUG1
                            ZDEBUG("spilcing_match_id_vec_update BATCH SUCCESS 1,channel_id:%d device1_id:%d id1:%d device2_id:%d id2:%d speed:%f x_error:%f y_error:%f\n",
                                   splicing_channel_id, spilcing_match_ids_vec_iter->device1_id, spilcing_match_ids_vec_iter->id1, spilcing_match_ids_vec_iter->device2_id, spilcing_match_ids_vec_iter->id2,
                                   spilcing_match_ids_vec_iter->speed, spilcing_match_ids_vec_iter->batch_x_error, spilcing_match_ids_vec_iter->batch_y_error);
#endif
                            trackFusionManger::get_match_error(splicing_channel_id, spilcing_match_ids_vec_iter->id1, spilcing_match_ids_vec_iter->id2,
                                                               spilcing_match_ids_vec_iter->batch_x_error, spilcing_match_ids_vec_iter->batch_y_error, tFusionManger);
                        }
                        else
                        {
#ifdef DEBUG1
                            ZDEBUG("spilcing_match_id_vec_update BATCH FAILURE 1,channel_id:%d device1_id:%d id1:%d device2_id:%d id2:%d speed:%f x_error:%f y_error:%f mean_match_score:%f batch_count_rate:%f\n",
                                   splicing_channel_id, spilcing_match_ids_vec_iter->device1_id, spilcing_match_ids_vec_iter->id1, spilcing_match_ids_vec_iter->device2_id, spilcing_match_ids_vec_iter->id2,
                                   spilcing_match_ids_vec_iter->speed, spilcing_match_ids_vec_iter->batch_x_error, spilcing_match_ids_vec_iter->batch_y_error, mean_match_score, batch_count_rate);
#endif
                        }
                    }
                }
                spilcing_match_ids_vec_iter++;
            }
            else
            {
#ifdef DEBUG
                ZDEBUG("spilcing_match_id_vec_update timeout BATCH FAILURE REMOVED 1,channel_id:%d device1_id:%d id1:%d device2_id:%d id2:%d batch failure!\n",
                       splicing_channel_id, spilcing_match_ids_vec_iter->device1_id, spilcing_match_ids_vec_iter->id1, spilcing_match_ids_vec_iter->device2_id, spilcing_match_ids_vec_iter->id2);

                ZDEBUG("spilcing_match_id_vec_update timeout BATCH FAILURE REMOVED 2,batch_time:%lld splicing_timestamp_ms:%lld splicing_start_time:%lld splicing_update_time:%lld splicing_batch_count:%d spilcing_batch_flag:%d\n",
                       batch_time, splicing_timestamp_ms, spilcing_match_ids_vec_iter->splicing_start_time, spilcing_match_ids_vec_iter->splicing_update_time, spilcing_match_ids_vec_iter->splicing_batch_count, spilcing_match_ids_vec_iter->spilcing_batch_flag);

                std::string score_vec_str = "";
                for (unsigned int i = 0; i < spilcing_match_ids_vec_iter->spilcing_match_score_vec.size(); i++)
                {
                    score_vec_str += " " + std::to_string(spilcing_match_ids_vec_iter->spilcing_match_score_vec[i]);
                }
                ZDEBUG("spilcing_match_id_vec_update timeout BATCH FAILURE REMOVED 3,spilcing_match_score_vec size:%d score list:%s\n",
                       spilcing_match_ids_vec_iter->spilcing_match_score_vec.size(), score_vec_str.c_str());
#endif
                spilcing_match_ids_vec_iter = spilcing_match_ids_vec.erase(spilcing_match_ids_vec_iter);
            }
        }
        else
        {
            if (abs(splicing_timestamp_ms - spilcing_match_ids_vec_iter->splicing_update_time) > splicing_batch_lose_time_threshold)
            {
#ifdef DBEUG
                ZDEBUG("spilcing_match_id_vec_update match EXCEED TIME LOSE REMOVED 1,channel_id:%d device1_id:%d id1:%d device2_id:%d id2:%d \n",
                       splicing_channel_id, spilcing_match_ids_vec_iter->device1_id, spilcing_match_ids_vec_iter->id1, spilcing_match_ids_vec_iter->device2_id, spilcing_match_ids_vec_iter->id2);

                ZDEBUG("spilcing_match_id_vec_update match EXCEED TIME LOSE REMOVED 2,splicing_timestamp_ms:%lld splicing_start_time:%lld splicing_update_time:%lld splicing_batch_count:%d spilcing_batch_flag:%d\n",
                       splicing_timestamp_ms, spilcing_match_ids_vec_iter->splicing_start_time, spilcing_match_ids_vec_iter->splicing_update_time, spilcing_match_ids_vec_iter->splicing_batch_count, spilcing_match_ids_vec_iter->spilcing_batch_flag);

                std::string score_vec_str = "";
                for (unsigned int i = 0; i < spilcing_match_ids_vec_iter->spilcing_match_score_vec.size(); i++)
                {
                    if (i % 2 == 0)
                    {
                        score_vec_str += " " + std::to_string(spilcing_match_ids_vec_iter->spilcing_match_score_vec[i]);
                    }
                }
                ZDEBUG("spilcing_match_id_vec_update match EXCEED TIME LOSE REMOVED 3,spilcing_match_score_vec size:%d score list:%s\n",
                       spilcing_match_ids_vec_iter->spilcing_match_score_vec.size(), score_vec_str.c_str());
#endif
                std::vector<float>().swap(spilcing_match_ids_vec_iter->spilcing_match_score_vec);
                spilcing_match_ids_vec_iter = spilcing_match_ids_vec.erase(spilcing_match_ids_vec_iter);
            }
            else
            {
                spilcing_match_ids_vec_iter++;
            }
        }
    }
}

/**
 * @Description: 拼接输入目标向量更新，更新上一台盒子的最后一个设备的信息到缓存中
 * 将缓存数据保存到splicing_input_target_vec中
 * @param last_output_targets 来自上一个盒子的最后一个设备接管的设备输出 带有拼接信息
 * @return void
 */
void TrackSplicing::splicing_input_target_vec_update(const OutputTargets& last_output_targets)
{
    // 遍历last_output_targets中的每个目标
#ifdef DEBUG1
    for (size_t sot = 0; sot < last_output_targets.target_cnt; sot++)
    {
        ZINFO("last_output_targets: splice_id:%d device_id:%d id:%d splicing_x:%f splicing_y:%f timestamp_ms:%lld \n",
            last_output_targets.fusion_targets[sot].splice_id,
            last_output_targets.fusion_targets[sot].device_id,
            last_output_targets.fusion_targets[sot].splice_target.id,
            last_output_targets.fusion_targets[sot].splicing_x,
            last_output_targets.fusion_targets[sot].splicing_y,
            last_output_targets.fusion_targets[sot].splice_target.timestamp_ms);
    }
    ZINFO("\n");
#endif

    for (unsigned int lot = 0; lot < last_output_targets.target_cnt; lot++)
    {
        unsigned int device_id = last_output_targets.fusion_targets[lot].device_id;
        unsigned int target_id = last_output_targets.fusion_targets[lot].splice_target.id;
        bool exist_splicing_input = false;     // 标记目标是否已经存在
        unsigned int exist_splicing_index = 0; // 存储已存在目标的索引
        // 遍历目标列表，检查是否已有该目标的拼接信息
        for (unsigned int sic = 0; sic < splicing_input_target_vec.size(); sic++)
        {
            // 采用设备id和目标id来判断是否存在缓存
            if (splicing_input_target_vec[sic].device_id == device_id && splicing_input_target_vec[sic].id == target_id)
            {
                exist_splicing_input = true; // 如果找到了相同目标，标记为已存在
                exist_splicing_index = sic;  // 记录该目标的索引
            }
        }
        // 计算目标的区域状态  TODO device_id目前是从0开始排序,应该按照唯一编码来区分
        // 获得设备经纬度
        double device_longitude = last_output_targets.fusion_targets[lot].splice_target.longitude;
        double device_latitude = last_output_targets.fusion_targets[lot].splice_target.latitude;
        double mercator_device_x = 0;
        double mercator_device_y = 0;
        Utils::lonlatToMercator(device_longitude, device_latitude, mercator_device_x, mercator_device_y);
        // 获取目标的设备坐标
        CalibPoint coor_in_device = {
            (float)mercator_device_x,
            (float)mercator_device_y
        };
        SplicingArea input_area_status = SplicingArea::TARGET_IN_TRACKING;

        // 计算目标在基准坐标系中的坐标（墨卡托坐标）
        CalibPoint coor_in_base;
        double mercator_x = 0;
        double mercator_y = 0;
        Utils::lonlatToMercator(last_output_targets.fusion_targets[lot].splice_target.longitude,
                                last_output_targets.fusion_targets[lot].splice_target.latitude,
                                mercator_x, mercator_y);

        // 将坐标转换为基准坐标系下的坐标
        coor_in_base.x = (float)mercator_x;
        coor_in_base.y = (float)mercator_y;

        // 相对于第一个设备的位置进行偏移 此处是相对于当前盒子的坐标系偏移
        coor_in_base.x -= splicing_devices_list.device_list[0].x;
        coor_in_base.y -= splicing_devices_list.device_list[0].y;

        // 如果找到目标，则更新缓存目标的上一个盒子的信息
        if (exist_splicing_input == true)
        {
            // 更新缓存目标的基本信息
            splicing_input_target_vec[exist_splicing_index].id = target_id;
            splicing_input_target_vec[exist_splicing_index].device_id = device_id;
            splicing_input_target_vec[exist_splicing_index].target_area_status = input_area_status;

            // 创建目标轨迹
            TargetTrace splicing_target_trace;
            splicing_target_trace.fusion_id = last_output_targets.fusion_targets[lot].splice_id;  // 上一个设备的目标的融合ID
            splicing_target_trace.target_coor_in_base = coor_in_base;
            splicing_target_trace.target = last_output_targets.fusion_targets[lot].splice_target;
            splicing_target_trace.target.direction = last_output_targets.fusion_targets[lot].splice_target.direction;
            splicing_target_trace.time_ms = last_output_targets.fusion_targets[lot].timestamp_ms;

            // 更新目标的轨迹
            splicing_input_target_vec[exist_splicing_index].target_input = splicing_target_trace;
            splicing_input_target_vec[exist_splicing_index].target_input_path.push_back(splicing_target_trace);
        }else{
            // 如果目标不存在，则创建新的目标
            SplicingInputTarget splicing_input_target;
            splicing_input_target.id = target_id;
            splicing_input_target.device_id = device_id;
            splicing_input_target.target_area_status = input_area_status;

            // 创建目标轨迹
            TargetTrace splicing_target_trace;
            splicing_target_trace.target_coor_in_base = coor_in_base;
            splicing_target_trace.target = last_output_targets.fusion_targets[lot].splice_target;
            splicing_target_trace.time_ms = last_output_targets.fusion_targets[lot].timestamp_ms;

            // 将新的轨迹添加到目标中
            splicing_input_target.target_input = splicing_target_trace;
            splicing_input_target.target_input.fusion_id = last_output_targets.fusion_targets[lot].splice_id;  // 上一个设备的目标的融合ID
            splicing_input_target.id = target_id;
            splicing_input_target.device_id = device_id;
            splicing_input_target.target_area_status = input_area_status;

            splicing_input_target.target_input_path.push_back(splicing_target_trace);
            splicing_input_target.target_direction_update_time = last_output_targets.fusion_targets[lot].splice_target.timestamp_ms;

            // 将新目标添加到目标列表
            splicing_input_target_vec.push_back(splicing_input_target);
        }

    }
    // 删除长时间未更新的目标
    std::vector<SplicingInputTarget>::iterator splicing_input_target_vec_iter;
    for (splicing_input_target_vec_iter = splicing_input_target_vec.begin(); splicing_input_target_vec_iter != splicing_input_target_vec.end();)
    {
        // 删除超时的目标 TODO 修改为一帧没有收到数据 则删除
        if (abs(splicing_timestamp_ms - splicing_input_target_vec_iter->target_input.time_ms) > 200)
        {
#ifdef DEBUG1
            ZINFO("splicing_input_target_vec  device_id:%d id:%d is exceed delete,area_status:%d splicing_timestamp_ms:%lld target_time_ms:%lld\n",
                    splicing_input_target_vec_iter->device_id, splicing_input_target_vec_iter->id, splicing_input_target_vec_iter->target_area_status,
                    splicing_timestamp_ms, splicing_input_target_vec_iter->target_input.time_ms);
#endif
            splicing_input_target_vec_iter = splicing_input_target_vec.erase(splicing_input_target_vec_iter);
        }
        else
        {
            splicing_input_target_vec_iter++;
        }

        // 更新轨迹长度
        for (unsigned int sit = 0; sit < splicing_input_target_vec.size(); sit++)
        {
            size_t input_path_length = splicing_input_target_vec[sit].target_input_path.size();
            if (input_path_length > 0)
            {
                long long path_time = splicing_input_target_vec[sit].target_input_path[input_path_length - 1].time_ms - splicing_input_target_vec[sit].target_input_path[0].time_ms;
                // 如果轨迹时间超过阈值，删除最早的轨迹点
                if (path_time > splicing_input_target_path_time_threshold)
                {
                    std::vector<TargetTrace>::iterator target_input_path_iter = splicing_input_target_vec[sit].target_input_path.begin();
                    splicing_input_target_vec[sit].target_input_path.erase(target_input_path_iter);
                }
            }
        }
    }
}


/**
 * @Description: 拼接输入目标向量更新，一次处理多个设备
 * 将缓存数据保存到splicing_input_target_vec中
 * @param multi_device_targets 输入的数据，包含多个设备的目标信息
 * @return void
 */
void TrackSplicing::splicing_input_target_vec_update(const MultiDeviceTargets& multi_device_targets)
{
    // 遍历每一个设备
    for (unsigned int mdt = 0; mdt < multi_device_targets.device_cnt; mdt++)
    {
        // 遍历当前设备上的每个目标
        for (unsigned int itc = 0; itc < multi_device_targets.device_input[mdt].target_cnt; itc++)
        {
            // 如果设备类型是Bayonet_Camera_Device，跳过该设备
            if (multi_device_targets.device_input[mdt].device_type == DeviceType::Bayonet_Camera_Device)
            {
                continue;
            }

            // 如果目标的y坐标（距离）大于最大阈值，跳过该目标
            if (multi_device_targets.device_input[mdt].targets.target_devices[itc].y > splicing_device_max_distance_threshold)
            {
                continue;
            }

            // 获取当前目标的设备ID和目标ID
            unsigned int device_id = multi_device_targets.device_input[mdt].device_id;
            unsigned int target_id = multi_device_targets.device_input[mdt].targets.target_devices[itc].id;

            #ifdef DEBUG_DATA_SAVE
            // 生成数据
            if (device_id == 3 || device_id == 4)
            {
                continue;
            }
            #endif

            bool exist_splicing_input = false;     // 标记目标是否已经存在
            unsigned int exist_splicing_index = 0; // 存储已存在目标的索引

            // 遍历目标列表，检查是否已有该目标的拼接信息
            for (unsigned int sic = 0; sic < splicing_input_target_vec.size(); sic++)
            {
                if (splicing_input_target_vec[sic].device_id == device_id && splicing_input_target_vec[sic].id == target_id)
                {
                    exist_splicing_input = true; // 如果找到了相同目标，标记为已存在
                    exist_splicing_index = sic;  // 记录该目标的索引
                }
            }

            // 基于车牌号对目标进行拼接检测
            for (unsigned int sic = 0; sic < splicing_input_target_vec.size(); sic++)
            {
                if (splicing_input_target_vec[sic].target_input.target.number_plate[0] != '\0' &&
                    multi_device_targets.device_input[mdt].targets.target_devices[itc].number_plate[0] != '\0' &&
                    splicing_input_target_vec[sic].device_id == device_id)
                {
                    // 计算当前目标和缓存目标的y坐标误差
                    float input_target_y = multi_device_targets.device_input[mdt].targets.target_devices[itc].y;
                    float input_buffer_target_y = splicing_input_target_vec[sic].target_input.target.y;
                    float input_target_y_error = input_target_y - input_buffer_target_y;

                    // 如果车牌号相同且y坐标误差小于阈值，认为是同一个目标
                    if (strcmp(splicing_input_target_vec[sic].target_input.target.number_plate,
                               multi_device_targets.device_input[mdt].targets.target_devices[itc].number_plate) == 0 &&
                        splicing_input_target_vec[sic].id != target_id && input_target_y_error < splicing_device_stably_detect_distance_threshold && input_target_y_error > 0)
                    {
#ifdef DEBUG
                        ZDEBUG("splicing_input_target_vec UPDATE, due number_plate is same, channel_id:%d device_id:%d input_id:%d buffer_id:%d n_plate:%s it_y_error:%f it_y_error_t:%f \n",
                               splicing_channel_id, multi_device_targets.device_input[device_id].device_id,
                               multi_device_targets.device_input[device_id].targets.target_devices[itc].id, splicing_input_target_vec[sic].target_input.target.id,
                               multi_device_targets.device_input[device_id].targets.target_devices[itc].number_plate,
                               input_target_y_error, splicing_device_stably_detect_distance_threshold);
#endif
                        exist_splicing_input = true; // 标记为已有拼接数据
                        exist_splicing_index = sic;  // 记录目标的索引
                        break;                       // 目标已找到，跳出循环
                    }
                }
            }

            // 计算目标在基准坐标系中的坐标（墨卡托坐标）
            CalibPoint coor_in_base;
            double mercator_x = 0;
            double mercator_y = 0;
            Utils::lonlatToMercator(multi_device_targets.device_input[mdt].targets.target_devices[itc].longitude,
                                    multi_device_targets.device_input[mdt].targets.target_devices[itc].latitude,
                                    mercator_x, mercator_y);

            // 将坐标转换为基准坐标系下的坐标
            coor_in_base.x = (float)mercator_x;
            coor_in_base.y = (float)mercator_y;

            // 相对于第一个设备的位置进行偏移
            coor_in_base.x -= splicing_devices_list.device_list[0].x;
            coor_in_base.y -= splicing_devices_list.device_list[0].y;

            // 获取目标的设备坐标
            CalibPoint coor_in_device;
            coor_in_device.x = multi_device_targets.device_input[mdt].targets.target_devices[itc].x;
            coor_in_device.y = multi_device_targets.device_input[mdt].targets.target_devices[itc].y;

            // 计算目标的区域状态
            SplicingArea input_area_status = SplicingArea::TARGET_IN_VANISH;
            splicing_target_area_status(input_area_status,
                                        coor_in_device,
                                        device_id,
                                        splicing_devices_list,
                                        multi_device_targets.device_input[mdt].targets.target_devices[itc].speed);

            // 如果目标已经存在，则更新目标信息
            if (exist_splicing_input == true)
            {
                float target_direction = splicing_input_target_vec[exist_splicing_index].target_input.target.direction;

                // 更新目标的基本信息
                splicing_input_target_vec[exist_splicing_index].id = multi_device_targets.device_input[mdt].targets.target_devices[itc].id;
                splicing_input_target_vec[exist_splicing_index].device_id = multi_device_targets.device_input[mdt].device_id;
                splicing_input_target_vec[exist_splicing_index].target_area_status = input_area_status;

                // 创建目标轨迹
                TargetTrace splicing_target_trace;
                splicing_target_trace.fusion_id = 0;
                splicing_target_trace.target_coor_in_base = coor_in_base;
                splicing_target_trace.target = multi_device_targets.device_input[mdt].targets.target_devices[itc];
                splicing_target_trace.target.direction = target_direction;
                splicing_target_trace.time_ms = multi_device_targets.device_input[mdt].timestamp_ms;

                // 更新目标的轨迹
                splicing_input_target_vec[exist_splicing_index].target_input = splicing_target_trace;
                splicing_input_target_vec[exist_splicing_index].target_input_path.push_back(splicing_target_trace);
            }
            else
            {
                // 如果目标不存在，则创建新的目标
                SplicingInputTarget splicing_input_target;
                splicing_input_target.id = multi_device_targets.device_input[mdt].targets.target_devices[itc].id;
                splicing_input_target.device_id = multi_device_targets.device_input[mdt].device_id;
                splicing_input_target.target_area_status = input_area_status;

                // 创建目标轨迹
                TargetTrace splicing_target_trace;
                splicing_target_trace.fusion_id = 0;
                splicing_target_trace.target_coor_in_base = coor_in_base;
                splicing_target_trace.target = multi_device_targets.device_input[mdt].targets.target_devices[itc];
                splicing_target_trace.time_ms = multi_device_targets.device_input[mdt].timestamp_ms;

                // 将新的轨迹添加到目标中
                splicing_input_target.target_input = splicing_target_trace;
                splicing_input_target.target_input_path.push_back(splicing_target_trace);
                splicing_input_target.target_direction_update_time = multi_device_targets.device_input[mdt].timestamp_ms;

                // 将新目标添加到目标列表
                splicing_input_target_vec.push_back(splicing_input_target);
            }
        }

        // 删除长时间未更新的目标
        std::vector<SplicingInputTarget>::iterator splicing_input_target_vec_iter;
        for (splicing_input_target_vec_iter = splicing_input_target_vec.begin(); splicing_input_target_vec_iter != splicing_input_target_vec.end();)
        {
            // 删除超时的目标 TODO 修改为一帧没有收到数据 则删除
            // BUG: 如果一帧没有收到数据，则删除目标，会导致目标丢失
            if (abs(multi_device_targets.device_input[mdt].timestamp_ms - splicing_input_target_vec_iter->target_input.time_ms) > 200)
            {
                // ZDEBUG("splicing_input_target_vec  device_id:%d id:%d is exceed delete,area_status:%d splicing_timestamp_ms:%lld target_time_ms:%lld\n",
                //        splicing_input_target_vec_iter->device_id, splicing_input_target_vec_iter->id, splicing_input_target_vec_iter->target_area_status,
                //        multi_device_targets.device_input[mdt].timestamp_ms, splicing_input_target_vec_iter->target_input.time_ms);
#ifdef DEBUG1
                ZINFO("splicing_input_target_vec  device_id:%d id:%d is exceed delete,area_status:%d splicing_timestamp_ms:%lld target_time_ms:%lld\n",
                      splicing_input_target_vec_iter->device_id, splicing_input_target_vec_iter->id, splicing_input_target_vec_iter->target_area_status,
                      multi_device_targets.device_input[mdt].timestamp_ms, splicing_input_target_vec_iter->target_input.time_ms);
#endif
                splicing_input_target_vec_iter = splicing_input_target_vec.erase(splicing_input_target_vec_iter);
            }
            else
            {
                splicing_input_target_vec_iter++;
            }

            // 更新轨迹长度
            for (unsigned int sit = 0; sit < splicing_input_target_vec.size(); sit++)
            {
                size_t input_path_length = splicing_input_target_vec[sit].target_input_path.size();
                if (input_path_length > 0)
                {
                    long long path_time = splicing_input_target_vec[sit].target_input_path[input_path_length - 1].time_ms - splicing_input_target_vec[sit].target_input_path[0].time_ms;
                    // 如果轨迹时间超过阈值，删除最早的轨迹点
                    if (path_time > splicing_input_target_path_time_threshold)
                    {
                        std::vector<TargetTrace>::iterator target_input_path_iter = splicing_input_target_vec[sit].target_input_path.begin();
                        splicing_input_target_vec[sit].target_input_path.erase(target_input_path_iter);
                    }
                }
            }
        }
    }
}

/*
 * @Name: splicing_input_target_interploation
 * @Description: 输入轨迹的邻近插值
 *
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/11/21
 * Time: 11:40
 * Author: WangXing
 * Content: Create
 */
void TrackSplicing::splicing_input_target_interploation(std::vector<SplicingInputTarget> &splicing_input_target_vec_,
                                                        long long splicing_time_ms)
{
    for (size_t sit = 0; sit < splicing_input_target_vec_.size(); sit++)
    {
        if (splicing_input_target_vec_[sit].target_input.time_ms >= splicing_time_ms)
        {
            // 不进行插值
        }
        else
        {
            long long interploation_duration = splicing_time_ms - splicing_input_target_vec_[sit].target_input.time_ms;
            // 找最邻近的两帧之间的间隔
            unsigned int track_length = splicing_input_target_vec_[sit].target_input_path.size();
            if (track_length <= 2)
            {
                // 不进行插值
            }
            else
            {
                long long near_first_time = splicing_input_target_vec_[sit].target_input_path[track_length - 1].time_ms;
                long long near_second_time = splicing_input_target_vec_[sit].target_input_path[track_length - 2].time_ms;
                long long near_duration = abs(near_first_time - near_second_time);
                if (near_duration > 100)
                {
                    float near_xmove = splicing_input_target_vec_[sit].target_input_path[track_length - 1].target_coor_in_base.x - splicing_input_target_vec_[sit].target_input_path[track_length - 2].target_coor_in_base.x;
                    float near_ymove = splicing_input_target_vec_[sit].target_input_path[track_length - 1].target_coor_in_base.y - splicing_input_target_vec_[sit].target_input_path[track_length - 2].target_coor_in_base.y;
                    float interploation_rate = float(interploation_duration) / float(near_duration);
#ifdef DEBUG
                    ZDEBUG("dv_id:%d id:%d near_xmove:%.2f near_ymove:%.2f near_duration:%lld interploation_duration:%lld interploation_rate:%.2f\n",
                           splicing_input_target_vec_[sit].device_id,
                           splicing_input_target_vec_[sit].id,
                           near_xmove, near_ymove,
                           near_duration,
                           interploation_duration,
                           interploation_rate);
#endif
                    splicing_input_target_vec_[sit].target_input.target_coor_in_base.x += near_xmove * interploation_rate;
                    splicing_input_target_vec_[sit].target_input.target_coor_in_base.y += near_ymove * interploation_rate;
                }
            }
        }
    }
}

void TrackSplicing::splicing_input_target_vec_clean()
{
    for (unsigned int st = 0; st < splicing_input_target_vec.size(); st++)
    {
        std::vector<TargetTrace>().swap(splicing_input_target_vec[st].target_input_path);
    }
    std::vector<SplicingInputTarget>().swap(splicing_input_target_vec);
}

int TrackSplicing::splicing_input_target_map(CalibPoint coor_in_device, CalibPoint &coor_in_base, unsigned int device_id)
{
    if (splicing_devices_map.device_map_mats.size() > 0)
    {
        for (auto it = splicing_devices_map.device_map_mats.begin(); it != splicing_devices_map.device_map_mats.end(); ++it)
        {
            if (it->second.device_id == device_id)
            {
                Eigen::Vector3d coor_in_device_eigen;
                coor_in_device_eigen << coor_in_device.x,
                    coor_in_device.y,
                    1;
                Eigen::Vector3d coor_in_base_eigen = it->second.calib_mat_ob * coor_in_device_eigen;
                coor_in_base.x = float(coor_in_base_eigen(0));
                coor_in_base.y = float(coor_in_base_eigen(1));
                return 0;
            }
        }
#ifdef DEBUG
        ZERROR("splicing_input_target_map failure! due to device_id:%d device_map_mats is not exist! \n",
               device_id);
#endif
        return -1;
    }
    else
    {
#ifdef DBEUG
        ZERROR("splicing_input_target_map failure! due to device_map_mats.size:%d \n", splicing_devices_map.device_map_mats.size());
#endif
        return -1;
    }
}

/*
 * @Name: splicing_target_area_status
 * @Description: 根据设备输出的目标坐标位置，以及设备的顺序判断其所在的区域
 *
 * @Input
 * target_area_status: 返回的设备区域
 * coor_in_device: 基于设备的位置
 * device_id: 设备的ID
 * splicing_device_list: 设备信息序号
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/9/26
 * Time: 9:48
 * Author: WangXing
 * Content: Create
 */
void TrackSplicing::splicing_target_area_status(SplicingArea &target_area_status,
                                                CalibPoint coor_in_device,
                                                unsigned int device_id,
                                                DeviceList splicing_device_list,
                                                float yspeed)
{
    if (splicing_device_list.device_cnt > 0)
    {
        for (size_t cn = 0; cn < splicing_device_list.device_cnt; cn++)
        {
            // 第一台设备并且是第一个盒子
            if (cn == 0 && (is_platform_relay_mode == 0 || is_platform_relay_mode == 3))
            {
                // 当前设备为第一台设备
                // 0 <-> 60m 速度==0 为盲区
                //           速度<0  为盲区
                //           速度>0  为检测区域
                // 60 <-> 300m 速度==0 跟踪区域
                //           速度<0  为跟踪区域
                //           速度>0  为跟踪区域
                // 300 ->    速度==0 为跟踪区域
                //           速度<0  为检测区域
                //           速度>0  为跟踪区域
                if (device_id == splicing_device_list.device_list[cn].device_id)
                {
                    if (coor_in_device.y >= 1e-4 && coor_in_device.y < splicing_device_stably_detect_distance_threshold)
                    {
                        if (fabs(yspeed) < 1e-4)
                        {
                            target_area_status = SplicingArea::TARGET_IN_DEAD; // 0228 检测区修改盲区
                        }
                        else
                        {
                            if (yspeed > 1e-4)
                            {
                                target_area_status = SplicingArea::TARGET_IN_DETECTION;
                            }
                            else
                            {
                                target_area_status = SplicingArea::TARGET_IN_DEAD;
                            }
                        }
                    }
                    else if (coor_in_device.y >= splicing_device_stably_detect_distance_threshold && coor_in_device.y < splicing_device_stably_track_distance_threshold)
                    {
                        target_area_status = SplicingArea::TARGET_IN_TRACKING;
                    }
                    else
                    {
                        if (fabs(yspeed) < 1e-4)
                        {
                            target_area_status = SplicingArea::TARGET_IN_VANISH;
                        }
                        else
                        {
                            if (yspeed > 1e-4)
                            {
                                target_area_status = SplicingArea::TARGET_IN_TRACKING;
                            }
                            else
                            {
                                target_area_status = SplicingArea::TARGET_IN_DETECTION;
                            }
                        }
                    }
                }
            }
            else if (cn == splicing_device_list.device_cnt - 1 && (is_platform_relay_mode == 2 || is_platform_relay_mode == 3))
            // 如果最后一个盒子的最后一个设备
            {
                if (device_id == splicing_device_list.device_list[cn].device_id)
                {
                    // 最后一台设备
                    // 0 <-> 60m 速度==0 为跟踪区域
                    //           速度<0  为消失区域
                    //           速度>0  为跟踪区域
                    // 60 <-> 300m 速度==0 跟踪区域
                    //           速度<0  为跟踪区域
                    //           速度>0  为跟踪区域
                    // 300 -> -- 速度==0 为跟踪区域
                    //           速度<0  为跟踪区域
                    //           速度>0  为消失区域
                    if (coor_in_device.y >= 1e-4 && coor_in_device.y < splicing_device_stably_detect_distance_threshold)
                    {
                        if (fabs(yspeed) < 1e-4)
                        {
                            target_area_status = SplicingArea::TARGET_IN_TRACKING;
                        }
                        else
                        {
                            if (yspeed < 1e-4)
                            {
                                target_area_status = SplicingArea::TARGET_IN_VANISH;
                            }
                            else
                            {
                                target_area_status = SplicingArea::TARGET_IN_TRACKING;
                            }
                        }
                    }
                    else if (coor_in_device.y >= splicing_device_stably_detect_distance_threshold && coor_in_device.y < splicing_device_stably_track_distance_threshold)
                    {
                        target_area_status = SplicingArea::TARGET_IN_TRACKING;
                    }
                    else
                    {
                        if (fabs(yspeed) < 1e-4)
                        {
                            target_area_status = SplicingArea::TARGET_IN_TRACKING;
                        }
                        else
                        {
                            if (yspeed > 1e-4)
                            {
                                target_area_status = SplicingArea::TARGET_IN_VANISH;
                            }
                            else
                            {
                                target_area_status = SplicingArea::TARGET_IN_TRACKING;
                            }
                        }
                    }
                }
            }
            else
            {
                if (device_id == splicing_device_list.device_list[cn].device_id)
                {
                    // 中间设备
                    // 0 <-> 60m 速度==0 为跟踪区域
                    //           速度<0  为盲区
                    //           速度>0  为跟踪区域
                    // 60 <-> 300m 速度==0 为跟踪区域
                    //           速度<0  为跟踪区域
                    //           速度>0  为跟踪区域
                    // 300 -> -- 速度==0 为跟踪区域
                    //           速度==0 为跟踪区域
                    //           速度==0 为跟踪区域
                    if (coor_in_device.y >= 1e-4 && coor_in_device.y < splicing_device_stably_detect_distance_threshold)
                    {
                        if (fabs(yspeed) < 1e-4)
                        {
                            target_area_status = SplicingArea::TARGET_IN_TRACKING;
                        }
                        else
                        {
                            if (yspeed < 1e-4)
                            {
                                target_area_status = SplicingArea::TARGET_IN_DEAD;
                            }
                            else
                            {
                                target_area_status = SplicingArea::TARGET_IN_TRACKING;
                            }
                        }
                    }
                    else if (coor_in_device.y >= splicing_device_stably_detect_distance_threshold && coor_in_device.y < splicing_device_stably_track_distance_threshold)
                    {
                        target_area_status = SplicingArea::TARGET_IN_TRACKING;
                    }
                    else
                    {
                        target_area_status = SplicingArea::TARGET_IN_TRACKING;
                    }
                }
            }
        }
    }
    else
    {
#ifdef DEBUG
        ZERROR("splicing_target_area_status splicing_device_list device_cnt:%d <= 0! \n",
               splicing_device_list.device_cnt);
#endif
    }
}

void TrackSplicing::splicing_targets_track()
{
    std::vector<SplicingInputTarget> temp_splicing_input_target_vec;
    temp_splicing_input_target_vec.assign(splicing_input_target_vec.begin(), splicing_input_target_vec.end());

    // 当拼接成功后，且目标处于拼接状态时，对待拼接的目标位置进行重新赋值
    spiling_target_weight_merge(temp_splicing_input_target_vec);

    // 重叠区域内目标的删除
    splicing_overlap_delete(temp_splicing_input_target_vec);

    // 重叠区域和非重叠区域目标的前后帧关联
    std::vector<SplicingInputTarget> new_splicing_input_target_vec;
    splicing_output_targets_associate(temp_splicing_input_target_vec, new_splicing_input_target_vec);

    // 将未关联成功的目标当作新出现的目标添加到容器当中
    splicing_output_targets_add(new_splicing_input_target_vec);

    // 目标的预测
    splicing_targets_track_predict();

    // 丢失目标和新出现目标的关联，丢失目标进行预测
    splicing_output_targets_reassociate();

    splicing_output_targets_second_reassociate();

    // 目标的更新
    splicing_targets_track_update();

    // 输出目标的fusion_id起始
    splicing_output_targets_id_update();

    // 输出目标的超时删除
    splicing_output_targets_remove();
}

void TrackSplicing::splicing_targets_track_predict()
{
    // 遍历所有输出目标
    for (unsigned int sot = 0; sot < splicing_output_targets_vec.size(); sot++)
    {
        // 获取当前目标的测量速度
        float meas_speed = splicing_output_targets_vec[sot].target_output.target.speed;
        // std::cout << "splicing_targets_track_predict, id:" << splicing_output_targets_vec[sot].target_output.target.id << " speed:" << meas_speed << " meas_speed / (splicing_fps * 3.60f):" << meas_speed / (splicing_fps * 3.60f) << std::endl;
        // 获取目标在基准坐标系中的坐标
        CalibPoint coor_in_mertor = splicing_output_targets_vec[sot].target_output.target_coor_in_base;
        // 获取目标在雷达坐标系中的坐标
        CalibPoint coor_in_radar = {
            splicing_output_targets_vec[sot].target_output.target.x,
            splicing_output_targets_vec[sot].target_output.target.y};
        // 判断目标是否处于丢失状态
        bool target_is_lose = (splicing_output_targets_vec[sot].target_output.splicing_state == SplicingTrackState::Lost);
        // 获取目标方向
        double target_direction = splicing_output_targets_vec[sot].target_output.target.direction;

        // 如果目标轨迹点数量为1（新目标）
        if (splicing_output_targets_vec[sot].target_output_path.size() == 1)
        {
            // 初始化卡尔曼滤波器
            splicing_output_targets_vec[sot].target_kalman->kf_smooth_init(
                splicing_output_targets_vec[sot].target_output.device_id,  // 设备ID
                splicing_output_targets_vec[sot].target_output.target.id,  // 目标ID
                coor_in_radar,  // 雷达坐标系坐标
                coor_in_mertor, // 基准坐标系坐标
                kf_x_error,     // X轴误差
                kf_y_error);    // Y轴误差
        }
        else  // 已有轨迹的目标
        {
            // 获取卡尔曼滤波器预测结果
            CalibPoint output_direction_predict = {
                splicing_output_targets_vec[sot].target_kalman->kf_update_res[0],
                splicing_output_targets_vec[sot].target_kalman->kf_update_res[1]};

            // 计算道路方向修正值
            float road_direction = roadDirectionCacl->road_direction_amend(
                splicing_output_targets_vec[sot].target_output.device_id,  // 设备ID
                output_direction_predict,  // 预测方向
                target_direction);         // 目标方向

            // 进行卡尔曼滤波预测
            splicing_output_targets_vec[sot].target_kalman->kf_smooth_predict(
                splicing_output_targets_vec[sot].target_output.fusion_id,  // 融合ID
                splicing_output_targets_vec[sot].target_output.device_id,  // 设备ID
                splicing_output_targets_vec[sot].target_output.target.id,  // 目标ID
                coor_in_radar,  // 雷达坐标系坐标
                coor_in_mertor, // 基准坐标系坐标
                meas_speed / (splicing_fps * 3.60f),  // 速度转换（km/h -> m/s）
                target_is_lose,  // 目标是否丢失
                road_direction); // 道路方向修正值
        }

        // 更新预测坐标
        splicing_output_targets_vec[sot].target_predict_xcoor = splicing_output_targets_vec[sot].target_kalman->kf_predict_res[0];
        splicing_output_targets_vec[sot].target_predict_ycoor = splicing_output_targets_vec[sot].target_kalman->kf_predict_res[1];
    }
}

/*
 * @Name: splicing_targets_track_update
 * @Description: 目标轨迹的更新
 *
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/11/4
 * Time: 14:01
 * Author: WangXing
 * Content: Create
 *
 * @Edit History
 * Date: 2025/02/25
 * Time: 11:57
 * Author: JiaTao
 * Content: 修改目标删除逻辑，删除距离远的目标
 */
void TrackSplicing::splicing_targets_track_update()
{
    for (unsigned int sot = 0; sot < splicing_output_targets_vec.size(); sot++)
    {
        if (splicing_output_targets_vec[sot].target_output_path.size() == 1)
        {
            continue;
        }
        float meas_speed = splicing_output_targets_vec[sot].target_output.target.speed;
        CalibPoint coor_in_mertor = splicing_output_targets_vec[sot].target_output.target_coor_in_base;
        bool target_is_lose = (splicing_output_targets_vec[sot].target_output.splicing_state == SplicingTrackState::Lost);
        double target_direction = splicing_output_targets_vec[sot].target_output.target.direction;
        // float amend_target_direction = roadDirectionCacl->road_direction_amend(splicing_output_targets_vec[sot].target_output.device_id,
        //                                                                        splicing_output_targets_vec[sot].target_output.target_coor_predict,
        //                                                                        target_direction);

        float amend_target_direction ;
        if(target_is_lose)
        {
            amend_target_direction = target_direction;
        }
        else
        {
            amend_target_direction = roadDirectionCacl->road_direction_amend(splicing_output_targets_vec[sot].target_output.device_id,
                                                                               splicing_output_targets_vec[sot].target_output.target_coor_predict,
                                                                               splicing_output_targets_vec[sot].target_output_path,
                                                                               target_direction);
            // amend_target_direction = target_direction;
        }

        // 基于目标的测量值结合道路方向进行方向修正,避免去噪之后便道延迟
        // ZINFO("splicing_targets_track_update, id:%d, speed:%f, direction:%f, amend_direction:%f \n",
        //       splicing_output_targets_vec[sot].target_output.target.id, meas_speed, target_direction, amend_target_direction);
        // 最小值为80km/h
        float smooth_min_speed = fabs(SMOOTHING_MIN_SPEED_MULTIPLE * meas_speed) > SMOOTH_MIN_SPEED ? fabs(SMOOTHING_MIN_SPEED_MULTIPLE * meas_speed) : SMOOTH_MIN_SPEED;
        splicing_output_targets_vec[sot].target_kalman->kf_smooth_update(
            splicing_output_targets_vec[sot].target_output.fusion_id,
            splicing_output_targets_vec[sot].target_output.device_id,
            splicing_output_targets_vec[sot].target_output.target.id,
            coor_in_mertor,
            meas_speed / (splicing_fps * 3.60f),
            fabs(smooth_min_speed / (splicing_fps * 3.60f)),
            fabs(SMOOTHING_MAX_SPEED_MULTIPLE * smooth_min_speed / (splicing_fps * 3.60f)),
            SMOOTHING_INTERVAL_SPEED / (splicing_fps * 3.60f),
            target_is_lose,
            amend_target_direction,
            use_input_noise_filter);
    }
}

// 目标的去重处理
void TrackSplicing::splicing_overlap_delete(std::vector<SplicingInputTarget> &temp_splicing_input_target_vec)
{
    for (unsigned int smi = 0; smi < spilcing_match_ids_vec.size(); smi++)
    {
        if (spilcing_match_ids_vec[smi].spilcing_batch_flag == false)
        {
            continue;
        }
        int device1_id = -1;
        int device2_id = -1;
        for (unsigned int tsi = 0; tsi < temp_splicing_input_target_vec.size(); tsi++)
        {
            if (spilcing_match_ids_vec[smi].device1_id == temp_splicing_input_target_vec[tsi].device_id && spilcing_match_ids_vec[smi].id1 == temp_splicing_input_target_vec[tsi].id)
            {
                device1_id = temp_splicing_input_target_vec[tsi].device_id;
                continue;
            }
            if (spilcing_match_ids_vec[smi].device2_id == temp_splicing_input_target_vec[tsi].device_id && spilcing_match_ids_vec[smi].id2 == temp_splicing_input_target_vec[tsi].id)
            {
                device2_id = temp_splicing_input_target_vec[tsi].device_id;
                continue;
            }
        }
        //        ZDEBUG("spilcing_match_ids_vec info channel_id:%d device1_id:%d id:%d device2_id:%d id2:%d speed:%f \n",
        //			    splicing_channel_id, spilcing_match_ids_vec[smi].device1_id, spilcing_match_ids_vec[smi].id1, spilcing_match_ids_vec[smi].device2_id, spilcing_match_ids_vec[smi].id2,
        //			    spilcing_match_ids_vec[smi].speed);
        if (device1_id == -1 || device2_id == -1)
        {
            continue;
        }
        bool is_delete_device1_id = false;
        // if (device1_id < device2_id)
        // {
        //     is_delete_device1_id = true;
        // }
        // 基于距离决策
        // 从input中获取对应的目标距离
        float current_target_distance = 0.0f;
        float matched_target_distance = 0.0f;
        for (unsigned int tsi = 0; tsi < temp_splicing_input_target_vec.size(); tsi++)
        {
            if (temp_splicing_input_target_vec[tsi].id == spilcing_match_ids_vec[smi].id1 && temp_splicing_input_target_vec[tsi].device_id == spilcing_match_ids_vec[smi].device1_id)
            {
                current_target_distance = temp_splicing_input_target_vec[tsi].target_input.target.distance;
            }
            if (temp_splicing_input_target_vec[tsi].id == spilcing_match_ids_vec[smi].id2 && temp_splicing_input_target_vec[tsi].device_id == spilcing_match_ids_vec[smi].device2_id)
            {
                matched_target_distance = temp_splicing_input_target_vec[tsi].target_input.target.distance;
            }
        }
        if (current_target_distance == 0.0f || matched_target_distance == 0.0f)
        {
            ZERROR("splicing_overlap_delete, id1:%d(%d), id2:%d(%d), current_target_distance:%f, matched_target_distance:%f \n",
                  spilcing_match_ids_vec[smi].id1, spilcing_match_ids_vec[smi].device1_id, spilcing_match_ids_vec[smi].id2, spilcing_match_ids_vec[smi].device2_id, current_target_distance, matched_target_distance);
            continue;
        }
        if (current_target_distance > matched_target_distance)
        {
            is_delete_device1_id = true;
#ifdef DEBUG1
            ZDEBUG("splicing_overlap_delete, id1:%d(%d), id2:%d(%d), current_target_distance:%f, matched_target_distance:%f is_delete_device1_id:%d \n",
                  spilcing_match_ids_vec[smi].id1, spilcing_match_ids_vec[smi].device1_id, spilcing_match_ids_vec[smi].id2, spilcing_match_ids_vec[smi].device2_id, current_target_distance, matched_target_distance, is_delete_device1_id);
#endif
        }
        if (is_delete_device1_id == true)
        {
            std::vector<SplicingInputTarget>::iterator temp_splicing_input_target_vec_iter;
            for (temp_splicing_input_target_vec_iter = temp_splicing_input_target_vec.begin(); temp_splicing_input_target_vec_iter != temp_splicing_input_target_vec.end();)
            {
                if (temp_splicing_input_target_vec_iter->id == spilcing_match_ids_vec[smi].id1 && temp_splicing_input_target_vec_iter->device_id == spilcing_match_ids_vec[smi].device1_id)
                {
                    //                    ZINFO("temp_splicing_input_target_vec overlap delete chanel_id:%d device1_id:%d id1:%d,base_x:%f base_y:%f \n",
                    //                           splicing_channel_id, temp_splicing_input_target_vec_iter->device_id, temp_splicing_input_target_vec_iter->id,
                    //                           temp_splicing_input_target_vec_iter->target_input.target.x, temp_splicing_input_target_vec_iter->target_input.target.y);
                    temp_splicing_input_target_vec_iter = temp_splicing_input_target_vec.erase(temp_splicing_input_target_vec_iter);
                }
                else
                {
                    temp_splicing_input_target_vec_iter++;
                }
            }
        }
        else
        {
            std::vector<SplicingInputTarget>::iterator temp_splicing_input_target_vec_iter;
            for (temp_splicing_input_target_vec_iter = temp_splicing_input_target_vec.begin(); temp_splicing_input_target_vec_iter != temp_splicing_input_target_vec.end();)
            {
                if (temp_splicing_input_target_vec_iter->id == spilcing_match_ids_vec[smi].id2 && temp_splicing_input_target_vec_iter->device_id == spilcing_match_ids_vec[smi].device2_id)
                {
                    //                    ZINFO("temp_splicing_input_target_vec overlap delete2 chanel_id:%d device2_id:%d id2:%d,x:%f y:%f \n",
                    //                           splicing_channel_id, temp_splicing_input_target_vec_iter->device_id, temp_splicing_input_target_vec_iter->id,
                    //                           temp_splicing_input_target_vec_iter->target_input.target.x, temp_splicing_input_target_vec_iter->target_input.target.y);
                    temp_splicing_input_target_vec_iter = temp_splicing_input_target_vec.erase(temp_splicing_input_target_vec_iter);
                }
                else
                {
                    temp_splicing_input_target_vec_iter++;
                }
            }
        }
    }
}


/*
 * @Name: splicing_output_targets_associate
 * @Description: 目标的关联处理
 *
 * @Input
 * std::vector<SplicingInputTarget> temp_splicing_input_target_vec: 输入的目标
 * std::vector<SplicingInputTarget> &new_splicing_input_target_vec: 输出的目标
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2025/02/25
 * Time: 11:57
 * Author: JiaTao
 * Content: 修改目标删除逻辑，删除距离远的目标
 */
void TrackSplicing::splicing_output_targets_associate(std::vector<SplicingInputTarget> temp_splicing_input_target_vec, std::vector<SplicingInputTarget> &new_splicing_input_target_vec)
{
    std::vector<unsigned int> splicing_tracked_output_target_ids;  // 拼接输出的目标ID
    std::vector<unsigned int> splicing_tracked_output_target_device_ids;  // 拼接输出的目标设备ID
    std::vector<unsigned int> splicing_tracked_output_target_fusion_ids;  // 拼接输出的目标融合ID

    for (unsigned int sit = 0; sit < temp_splicing_input_target_vec.size(); sit++)
    {
        size_t path_length_i = temp_splicing_input_target_vec[sit].target_input_path.size();
        if (path_length_i == 0)
        {
            continue;
        }
        bool is_exist_output_target = false;  // 是否存在拼接输出的目标
        unsigned int exist_output_target_index = 0;  // 拼接输出的目标索引
        for (unsigned int sot = 0; sot < splicing_output_targets_vec.size(); sot++)
        {
            bool is_exist_overlap_target = false;  // 是否存在重叠的目标
            for (unsigned int smi = 0; smi < spilcing_match_ids_vec.size(); smi++)
            {
                if (spilcing_match_ids_vec[smi].spilcing_batch_flag == false)
                {
                    continue;
                }
                if (temp_splicing_input_target_vec[sit].device_id == spilcing_match_ids_vec[smi].device1_id &&
                temp_splicing_input_target_vec[sit].id == spilcing_match_ids_vec[smi].id1 &&
                splicing_output_targets_vec[sot].target_output.device_id == spilcing_match_ids_vec[smi].device2_id &&
                splicing_output_targets_vec[sot].target_output.id == spilcing_match_ids_vec[smi].id2)
                {
                    is_exist_overlap_target = true;
                    break;
                }
                if (temp_splicing_input_target_vec[sit].device_id == spilcing_match_ids_vec[smi].device2_id &&
                temp_splicing_input_target_vec[sit].id == spilcing_match_ids_vec[smi].id2 &&
                splicing_output_targets_vec[sot].target_output.device_id == spilcing_match_ids_vec[smi].device1_id &&
                splicing_output_targets_vec[sot].target_output.id == spilcing_match_ids_vec[smi].id1)
                {
                    is_exist_overlap_target = true;
                    break;
                }
            }
            // 重叠区域的目标ID前后关联
            if (is_exist_overlap_target == true)
            {
                is_exist_output_target = true;
                exist_output_target_index = sot;
                break;
            }
            // 非重叠区域的目标ID前后关联
            if (temp_splicing_input_target_vec[sit].device_id == splicing_output_targets_vec[sot].target_output.device_id &&
            temp_splicing_input_target_vec[sit].id == splicing_output_targets_vec[sot].target_output.id)
            {
                is_exist_output_target = true;
                exist_output_target_index = sot;
                break;
            }
        }
        if (is_exist_output_target == true)
        {
            TargetOutput new_target_output;
            new_target_output.id = temp_splicing_input_target_vec[sit].id;
            new_target_output.device_id = temp_splicing_input_target_vec[sit].device_id;
            new_target_output.target_area_status = temp_splicing_input_target_vec[sit].target_area_status;
            new_target_output.target = temp_splicing_input_target_vec[sit].target_input.target;
            new_target_output.target_coor_in_base = temp_splicing_input_target_vec[sit].target_input.target_coor_in_base;
            new_target_output.target_coor_predict = temp_splicing_input_target_vec[sit].target_input.target_coor_in_base;
            new_target_output.target_timestamp_ms = temp_splicing_input_target_vec[sit].target_input.time_ms;

            // 更新
            unsigned int temp_target_output_fusion_id = splicing_output_targets_vec[exist_output_target_index].target_output.fusion_id;
            SplicingTrackState temp_target_output_state = splicing_output_targets_vec[exist_output_target_index].target_output.splicing_state;
            long long target_start_time_ms = splicing_output_targets_vec[exist_output_target_index].target_output.target_start_timestamp_ms;
            if (temp_target_output_state == SplicingTrackState::Lost)
            {
                temp_target_output_state = SplicingTrackState::Tracked;
            }
            new_target_output.fusion_id = temp_target_output_fusion_id;
            new_target_output.splicing_state = temp_target_output_state;
            new_target_output.target_start_timestamp_ms = target_start_time_ms;

            // 车辆语义信息的更新
            splicing_target_info_update(new_target_output, splicing_output_targets_vec[exist_output_target_index].target_output, temp_splicing_input_target_vec[sit].target_input);

            splicing_output_targets_vec[exist_output_target_index].target_output = new_target_output;
            splicing_output_targets_vec[exist_output_target_index].target_output_path.push_back(new_target_output);

            splicing_tracked_output_target_ids.push_back(splicing_output_targets_vec[exist_output_target_index].target_output.id);
            splicing_tracked_output_target_device_ids.push_back(splicing_output_targets_vec[exist_output_target_index].target_output.device_id);
            splicing_tracked_output_target_fusion_ids.push_back(splicing_output_targets_vec[exist_output_target_index].target_output.fusion_id);
        }
        else
        {
            // 添加
            new_splicing_input_target_vec.push_back(temp_splicing_input_target_vec[sit]);
        }
    }

    // 目标丢失状态的赋值
    for (unsigned int sot = 0; sot < splicing_output_targets_vec.size(); sot++)
    {
        bool is_lost_status_output_target = true;  // 是否丢失状态的目标
        for (unsigned int ids = 0; ids < splicing_tracked_output_target_ids.size(); ids++)
        {
            if (splicing_output_targets_vec[sot].target_output.id == splicing_tracked_output_target_ids[ids] &&
            splicing_output_targets_vec[sot].target_output.device_id == splicing_tracked_output_target_device_ids[ids] &&
            splicing_output_targets_vec[sot].target_output.fusion_id == splicing_tracked_output_target_fusion_ids[ids])
            {
                is_lost_status_output_target = false;
                break;
            }
        }
        if (is_lost_status_output_target == true)
        {
            // 当重叠区域的目标未关联上后，判断其拼接匹配的目标是否在已起始的目标中是否存在，当存在时则可以进行丢失，否则不能进行丢失
            bool is_overlap_target_lose = false;
            unsigned int matched_target_index = UINT_MAX;  // 匹配目标的索引

            for (unsigned int smi = 0; smi < spilcing_match_ids_vec.size(); smi++)
            {
                if (spilcing_match_ids_vec[smi].spilcing_batch_flag == false)
                {
                    continue;
                }

                // 当前目标是设备1的目标
                if (splicing_output_targets_vec[sot].target_output.id == spilcing_match_ids_vec[smi].id1 &&
                    splicing_output_targets_vec[sot].target_output.device_id == spilcing_match_ids_vec[smi].device1_id)
                {
                    // 查找设备2的匹配目标
                    for (unsigned int sotv = 0; sotv < splicing_output_targets_vec.size(); sotv++)
                    {
                        if (splicing_output_targets_vec[sotv].target_output.id == spilcing_match_ids_vec[smi].id2 &&
                            splicing_output_targets_vec[sotv].target_output.device_id == spilcing_match_ids_vec[smi].device2_id)
                        {
                            matched_target_index = sotv;
                            is_overlap_target_lose = true;
                            break;
                        }
                    }
                    break;
                }

                // 当前目标是设备2的目标
                if (splicing_output_targets_vec[sot].target_output.id == spilcing_match_ids_vec[smi].id2 &&
                    splicing_output_targets_vec[sot].target_output.device_id == spilcing_match_ids_vec[smi].device2_id)
                {
                    // 查找设备1的匹配目标
                    for (unsigned int sotv = 0; sotv < splicing_output_targets_vec.size(); sotv++)
                    {
                        if (splicing_output_targets_vec[sotv].target_output.id == spilcing_match_ids_vec[smi].id1 &&
                            splicing_output_targets_vec[sotv].target_output.device_id == spilcing_match_ids_vec[smi].device1_id)
                        {
                            matched_target_index = sotv;
                            is_overlap_target_lose = true;
                            break;
                        }
                    }
                    break;
                }
            }

            // 重叠区域的去重: 基于距离决策
            if (is_overlap_target_lose == true && matched_target_index != UINT_MAX)
            {
                // 获取当前目标与其设备的距离
                float current_target_distance = splicing_output_targets_vec[sot].target_output.target.distance;

                // 获取匹配目标与其设备的距离
                float matched_target_distance = splicing_output_targets_vec[matched_target_index].target_output.target.distance;

                // 比较距离，距离较远的目标被标记为Removed
                if (current_target_distance > matched_target_distance)
                {
                    // 当前目标距离较远，标记为Removed
                    splicing_output_targets_vec[sot].target_output.splicing_state = SplicingTrackState::Removed;
#ifdef DEBUG1
                    ZDEBUG("splicing_output_targets_vec OVERLAP Removed (distance-based) channel_id:%d fusion_id:%d id:%d device_id:%d, distance:%f > %f \n",
                           splicing_channel_id,
                           splicing_output_targets_vec[sot].target_output.fusion_id,
                           splicing_output_targets_vec[sot].target_output.id,
                           splicing_output_targets_vec[sot].target_output.device_id,
                           current_target_distance, matched_target_distance);
#endif
                }
                else
                {
                    // 匹配目标距离较远，标记匹配目标为Removed
                    splicing_output_targets_vec[matched_target_index].target_output.splicing_state = SplicingTrackState::Removed;
#ifdef DEBUG1
                    ZDEBUG("splicing_output_targets_vec OVERLAP Removed (distance-based) channel_id:%d fusion_id:%d id:%d device_id:%d, distance:%f > %f \n",
                           splicing_channel_id,
                           splicing_output_targets_vec[matched_target_index].target_output.fusion_id,
                           splicing_output_targets_vec[matched_target_index].target_output.id,
                           splicing_output_targets_vec[matched_target_index].target_output.device_id,
                           matched_target_distance, current_target_distance);
#endif
                }
            }
            else
            {
                // 非重叠区域的目标丢失
                if (splicing_output_targets_vec[sot].target_output.splicing_state == SplicingTrackState::Tracked)
                {
                    splicing_output_targets_vec[sot].target_output.splicing_state = SplicingTrackState::Lost;
#ifdef DEBUG1
                    ZDEBUG("splicing_output_targets_vec Lost channel_id:%d fusion_id:%d id:%d device_id:%d, due to not correlation! \n",
                           splicing_channel_id, splicing_output_targets_vec[sot].target_output.fusion_id,
                           splicing_output_targets_vec[sot].target_output.id,
                           splicing_output_targets_vec[sot].target_output.device_id);
#endif
                }
            }
        }
    }
}

void TrackSplicing::splicing_output_targets_add(std::vector<SplicingInputTarget> new_splicing_input_target_vec)
{
    for (unsigned int sit = 0; sit < new_splicing_input_target_vec.size(); sit++)
    {
        TargetOutput new_target_output;
        // 如果new_splicing_input_target_vec[sit]存在融合id，则使用融合id,并且设置为Tracked状态
        if (new_splicing_input_target_vec[sit].target_input.fusion_id != 0)
        {
            new_target_output.splicing_state = SplicingTrackState::Tracked;
            new_target_output.fusion_id = new_splicing_input_target_vec[sit].target_input.fusion_id;
        }else{
            new_target_output.splicing_state = SplicingTrackState::New;
            new_target_output.fusion_id = splicing_fusion_id_init;
        }
        new_target_output.id = new_splicing_input_target_vec[sit].id;
        new_target_output.device_id = new_splicing_input_target_vec[sit].device_id;
        new_target_output.target_area_status = new_splicing_input_target_vec[sit].target_area_status;
        new_target_output.target = new_splicing_input_target_vec[sit].target_input.target;
        new_target_output.target_coor_in_base = new_splicing_input_target_vec[sit].target_input.target_coor_in_base;

        new_target_output.target_coor_predict = new_splicing_input_target_vec[sit].target_input.target_coor_in_base;

        new_target_output.target_timestamp_ms = new_splicing_input_target_vec[sit].target_input.time_ms;

        // 添加
        new_target_output.target_start_timestamp_ms = new_splicing_input_target_vec[sit].target_input.time_ms;
        SplicingTarget new_splicing_target_output;
        new_splicing_target_output.target_output = new_target_output;
        new_splicing_target_output.target_output_path.push_back(new_target_output);
        new_splicing_target_output.target_predict_xcoor = new_target_output.target_coor_in_base.x;
        new_splicing_target_output.target_predict_ycoor = new_target_output.target_coor_in_base.y;
        new_splicing_target_output.target_kalman = new KalmanSmooth(kf_state_size, kf_meas_size, kf_control_size, kf_t, kf_rs_rate, kf_qs_rate);
        splicing_output_targets_vec.push_back(new_splicing_target_output);
#ifdef DEBUG
        ZDEBUG("splicing_output_targets_vec ADD TARGET channel_id:%d fusion_id:%d splicing_state:%d id:%d device_id:%d,target_start_timestamp_ms:%lld \n",
               splicing_channel_id, new_target_output.fusion_id, new_target_output.splicing_state, new_target_output.id, new_target_output.device_id, new_target_output.target_start_timestamp_ms);
#endif
    }
}

/*
 * @Name: splicing_target_info_update
 * @Description: 目标信息信息的更新,隧道场景假设速度不为负
 *
 * @Input
 * new_target_output: 新出现的输出目标，默认使用已匹配成功的新出现的目标作为初始值
 * old_target_output: 上一帧轨迹出现的目标，
 * new_target_input:  新出现的输入目标, 其赋值于new_target_output；
 * dt: 时间差 ms
 *
 * @Output
 * new_target_output: 更新后的新出现的输出目标
 *
 * @Edit History
 * Date: 2024/8/7
 * Time: 9:39
 * Author: WangXing
 * Content: Create
 */
void TrackSplicing::splicing_target_info_update(TargetOutput &new_target_output, TargetOutput old_target_output, TargetTrace new_target_input)
{
    // 高速公路的车辆加速度最大值
    float max_speed_change_rate = 2.0f; // m/s^2
    // 计算dt内最大的速度变化
    float max_speed_change = max_speed_change_rate * dt / 1000.0f * 3.6f;
    // 行驶方向，速度的正负
    int new_speed_sign = new_target_input.target.speed > 0 ? 1 : -1;

    // 车辆速度信息使用最新的速度信息，速度一致性不足应该提高设备的速度探测准确性；
    if (fabs(old_target_output.target.speed) < 1e-4)
    {
        if (fabs(new_target_input.target.speed) < 1e-4)
        {
            new_target_output.target.speed = 0;
        }
        else
        {
            new_target_output.target.speed = new_target_input.target.speed;
        }
    }
    else
    {
        if (fabs(new_target_input.target.speed) < 1e-4)
        {
            new_target_output.target.speed = old_target_output.target.speed;
        }
        else
        {
            if (fabs(fabs(new_target_input.target.speed) - fabs(old_target_output.target.speed)) < max_speed_change)
            {
                new_target_output.target.speed = new_target_input.target.speed;
            }
            else
            {
                if (fabs(new_target_input.target.speed) - fabs(old_target_output.target.speed) > 1e-4)
                {
                    new_target_output.target.speed = fabs(old_target_output.target.speed) + max_speed_change;
                }
                else
                {
                    new_target_output.target.speed = fabs(old_target_output.target.speed) - max_speed_change;
                }
                // 新的方向与输入速度方向一致
                new_target_output.target.speed = new_target_output.target.speed * new_speed_sign;

            }
        }
    }

    // 车牌号码更新
    if (old_target_output.target.number_plate[0] == '\0')
    {
        if (new_target_input.target.number_plate[0] != '\0')
        {
            memcpy(new_target_output.target.number_plate, new_target_input.target.number_plate, sizeof(new_target_input.target.number_plate));
            new_target_output.target.np_score = new_target_input.target.np_score;
        }
        else
        {
            memcpy(new_target_output.target.number_plate, old_target_output.target.number_plate, sizeof(new_target_output.target.number_plate));
            new_target_output.target.np_score = old_target_output.target.np_score;
        }
    }
    else
    {
        if (new_target_input.target.number_plate[0] != '\0')
        {
            if (new_target_input.target.np_score - old_target_output.target.np_score > plate_number_update_threshold)
            {
                memcpy(new_target_output.target.number_plate, new_target_input.target.number_plate, sizeof(new_target_input.target.number_plate));
                new_target_output.target.np_score = new_target_input.target.np_score;
            }
            else
            {
                memcpy(new_target_output.target.number_plate, old_target_output.target.number_plate, sizeof(new_target_output.target.number_plate));
                new_target_output.target.np_score = old_target_output.target.np_score;
            }
        }
        else
        {
            memcpy(new_target_output.target.number_plate, old_target_output.target.number_plate, sizeof(new_target_output.target.number_plate));
            new_target_output.target.np_score = old_target_output.target.np_score;
        }
    }

    // 车牌颜色
    if (old_target_output.target.plate_color == PlateColor::PLATE_COLOR_UNKNOWN)
    {
        if (new_target_input.target.plate_color != PlateColor::PLATE_COLOR_UNKNOWN)
        {
            new_target_output.target.plate_color = new_target_input.target.plate_color;
            new_target_output.target.pc_score = new_target_input.target.pc_score;
        }
        else
        {
            new_target_output.target.plate_color = old_target_output.target.plate_color;
            new_target_output.target.pc_score = old_target_output.target.pc_score;
        }
    }
    else
    {
        if (new_target_input.target.plate_color != PlateColor::PLATE_COLOR_UNKNOWN)
        {
            if (new_target_input.target.pc_score - old_target_output.target.pc_score > plate_color_update_threshold)
            {
                new_target_output.target.plate_color = new_target_input.target.plate_color;
                new_target_output.target.pc_score = new_target_input.target.pc_score;
            }
            else
            {
                new_target_output.target.plate_color = old_target_output.target.plate_color;
                new_target_output.target.pc_score = old_target_output.target.pc_score;
            }
        }
        else
        {
            new_target_output.target.plate_color = old_target_output.target.plate_color;
            new_target_output.target.pc_score = old_target_output.target.pc_score;
        }
    }

    // 车辆类别
    new_target_output.target.target_type = new_target_input.target.target_type;

    // 车身颜色
    if (old_target_output.target.vehicle_color == VehicleColor::VEHICLE_COLOR_UNKNOWN)
    {
        if (new_target_input.target.vehicle_color != VehicleColor::VEHICLE_COLOR_UNKNOWN)
        {
            new_target_output.target.vehicle_color = new_target_input.target.vehicle_color;
            new_target_output.target.vc_score = new_target_input.target.vc_score;
        }
        else
        {
            new_target_output.target.vehicle_color = old_target_output.target.vehicle_color;
            new_target_output.target.vc_score = old_target_output.target.vc_score;
        }
    }
    else
    {
        if (new_target_input.target.vehicle_color != VehicleColor::VEHICLE_COLOR_UNKNOWN)
        {
            if (new_target_input.target.vc_score - old_target_output.target.vc_score > vehicle_color_update_threshold)
            {
                new_target_output.target.vehicle_color = new_target_input.target.vehicle_color;
                new_target_output.target.vc_score = new_target_input.target.vc_score;
            }
            else
            {
                new_target_output.target.vehicle_color = old_target_output.target.vehicle_color;
                new_target_output.target.vc_score = old_target_output.target.vc_score;
            }
        }
        else
        {
            new_target_output.target.vehicle_color = old_target_output.target.vehicle_color;
            new_target_output.target.vc_score = old_target_output.target.vc_score;
        }
    }

    // 车辆类型
    if (old_target_output.target.vehicle_type == VehicleType::VEHICLE_TYPE_UNKNOWN)
    {
        if (new_target_input.target.vehicle_type != VehicleType::VEHICLE_TYPE_UNKNOWN)
        {
            new_target_output.target.vehicle_type = new_target_input.target.vehicle_type;
            new_target_output.target.vt_score = new_target_input.target.vt_score;
        }
        else
        {
            new_target_output.target.vehicle_type = old_target_output.target.vehicle_type;
            new_target_output.target.vt_score = old_target_output.target.vt_score;
        }
    }
    else
    {
        if (new_target_input.target.vehicle_type != VehicleType::VEHICLE_TYPE_UNKNOWN)
        {
            if (new_target_input.target.vt_score - old_target_output.target.vt_score > vehicle_type_update_threshold)
            {
                new_target_output.target.vehicle_type = new_target_input.target.vehicle_type;
                new_target_output.target.vt_score = new_target_input.target.vt_score;
            }
            else
            {
                new_target_output.target.vehicle_type = old_target_output.target.vehicle_type;
                new_target_output.target.vt_score = old_target_output.target.vt_score;
            }
        }
        else
        {
            new_target_output.target.vehicle_type = old_target_output.target.vehicle_type;
            new_target_output.target.vt_score = old_target_output.target.vt_score;
        }
    }
}
/**
 * @brief 更新目标的fusion_id
 * 起批逻辑：
 * 1. 当目标所在区域为检测区且车牌号码存在时
 * 2. 目标刚开始进入跟踪区域，上一帧目标还处于初次检测区域
 */
void TrackSplicing::splicing_output_targets_id_update()
{
    // 目标fusion_id的起始, 对于优先探测到的目标的设备
    for (unsigned int sot = 0; sot < splicing_output_targets_vec.size(); sot++)
    {
        size_t path_length_o = splicing_output_targets_vec[sot].target_output_path.size();
        if (path_length_o == 0)
        {
            splicing_output_targets_vec[sot].target_output.splicing_state = SplicingTrackState::Removed;
#ifdef DEBUG1
            ZERROR("splicing_output_targets_id_update Exception, due to channel_id:%d, id:%d device_id:%d fusion_id:%d, path_length:%d \n",
                   splicing_channel_id, splicing_output_targets_vec[sot].target_output.id, splicing_output_targets_vec[sot].target_output.device_id, path_length_o);
#endif
            continue;
        }
        if (splicing_output_targets_vec[sot].target_output.splicing_state != SplicingTrackState::New)
        {
            continue;
        }
        if (fabs(splicing_output_targets_vec[sot].target_output.target.direction) < 1e-4)
        {
            continue;
        }

        // 当目标所在区域为检测区且车牌号码存在时，则直接起始
        if (splicing_output_targets_vec[sot].target_output.target_area_status == SplicingArea::TARGET_IN_DETECTION)
        {
            if (splicing_output_targets_vec[sot].target_output.target.number_plate[0] != '\0')
            {
                if (splicing_output_targets_vec[sot].target_output.fusion_id == 0)
                {
                    splicing_output_targets_vec[sot].target_output.fusion_id = get_fusion_id();
                }
                splicing_output_targets_vec[sot].target_output.splicing_state = SplicingTrackState::Tracked;
#ifdef DEBUG1
                ZDEBUG("splicing_output_targets_id_update, TARGTE GENERATE ID, due to exist plate channel_id:%d, fusion_id:%d id:%d device_id:%d p_length:%d n_plate:%s\n",
                       splicing_channel_id,
                       splicing_output_targets_vec[sot].target_output.fusion_id,
                       splicing_output_targets_vec[sot].target_output.id,
                       splicing_output_targets_vec[sot].target_output.device_id, path_length_o,
                       splicing_output_targets_vec[sot].target_output.target.number_plate);
#endif
                continue;
            }
        }

        // 当目标所在区域为检测区且车牌号码存在时，则直接起始
        if (splicing_output_targets_vec[sot].target_output.device_id == splicing_devices_list.device_list[0].device_id)
        {
            if (splicing_output_targets_vec[sot].target_output.target.number_plate[0] != '\0' && splicing_output_targets_vec[sot].target_output.target_area_status == SplicingArea::TARGET_IN_DETECTION)
            // if (splicing_output_targets_vec[sot].target_output.target.number_plate[0] != '\0' && splicing_output_targets_vec[sot].target_output.target.y < lose_number_plate_distance)
            {
                if (splicing_output_targets_vec[sot].target_output.fusion_id == 0)
                {
                    splicing_output_targets_vec[sot].target_output.fusion_id = get_fusion_id();
                }
                splicing_output_targets_vec[sot].target_output.splicing_state = SplicingTrackState::Tracked;
#ifdef DEBUG1
                ZDEBUG("splicing_output_targets_id_update, TARGTE GENERATE ID, due to exist plate and device_id is first,  channel_id:%d, fusion_id:%d id:%d device_id:%d p_length:%d n_plate:%s\n",
                       splicing_channel_id,
                       splicing_output_targets_vec[sot].target_output.fusion_id,
                       splicing_output_targets_vec[sot].target_output.id,
                       splicing_output_targets_vec[sot].target_output.device_id, path_length_o,
                       splicing_output_targets_vec[sot].target_output.target.number_plate);
#endif
                continue;
            }
        }
        // 目标刚开始进入跟踪区域，上一帧目标还处于初次检测区域，则目标起批
        if (splicing_output_targets_vec[sot].target_output.target_area_status == SplicingArea::TARGET_IN_TRACKING)
        {
            if (path_length_o >= splicing_batch_frame_count)
            {
                SplicingArea previous_frame_area_status = splicing_output_targets_vec[sot].target_output_path[path_length_o - splicing_batch_frame_count].target_area_status;
                if (splicing_timestamp_ms - splicing_output_targets_vec[sot].target_output.target_timestamp_ms < splicing_fusion_near_time_threshold && previous_frame_area_status == SplicingArea::TARGET_IN_DETECTION)
                {
                    if (splicing_output_targets_vec[sot].target_output.fusion_id == 0)
                    {
                        splicing_output_targets_vec[sot].target_output.fusion_id = get_fusion_id();
                    }
                    splicing_output_targets_vec[sot].target_output.splicing_state = SplicingTrackState::Tracked;
#ifdef DEBUG1
                    ZDEBUG("splicing_output_targets_id_update, TARGTE GENERATE ID,due to previous_frame_area is IN_DETECTION, currect_frame_area is TARGET_IN_TRACKING  channel_id:%d, get_fusion_id:%d id:%d device_id:%d path_length:%d splicing_timestamp_ms:%lld target_timestamp_ms:%lld \n",
                           splicing_channel_id, splicing_output_targets_vec[sot].target_output.fusion_id, splicing_output_targets_vec[sot].target_output.id,
                           splicing_output_targets_vec[sot].target_output.device_id, path_length_o,
                           splicing_timestamp_ms, splicing_output_targets_vec[sot].target_output.target_start_timestamp_ms);
#endif
                    continue;
                }
            }
        }
    }
}

void TrackSplicing::splicing_output_targets_remove()
{
    // 遍历所有拼接输出目标，检查哪些目标需要被删除（状态置为 Removed）
    for (unsigned int sot = 0; sot < splicing_output_targets_vec.size(); sot++)
    {
        // 如果当前目标的跟踪状态为 Lost（丢失状态）
        if (splicing_output_targets_vec[sot].target_output.splicing_state == SplicingTrackState::Lost)
        {
            // 计算当前时间与该目标最后一次更新的时间差
            // 如果时间差超过预设的丢失时间阈值，则认为该目标已经长时间未更新
            if (abs(splicing_timestamp_ms - splicing_output_targets_vec[sot].target_output.target_timestamp_ms) > splicing_fusion_lost_time_threshold)
            {
                // 将目标状态置为 Removed，表示该目标将被删除
                splicing_output_targets_vec[sot].target_output.splicing_state = SplicingTrackState::Removed;

#ifdef DEBUG1
                // 调试模式下打印详细日志，记录删除原因和相关参数
                ZDEBUG("splicing_output_targets_vec TIMEOUT Removed channel_id:%d fusion_id:%d id:%d device_id:%d, due to lost time exceed threshold splicing_time_ms:%lld target_time_ms:%lld splicing_fusion_lost_time_threshold:%lld \n",
                       splicing_channel_id,
                       splicing_output_targets_vec[sot].target_output.fusion_id,
                       splicing_output_targets_vec[sot].target_output.id,
                       splicing_output_targets_vec[sot].target_output.device_id,
                       splicing_timestamp_ms,
                       splicing_output_targets_vec[sot].target_output.target_timestamp_ms,
                       splicing_fusion_lost_time_threshold);
#endif
                // 当前目标处理完毕，继续下一个目标的检查
                continue;
            }

            // 如果目标的区域状态为 TARGET_IN_DETECTION（处于检测区域内）
            // 可能表示目标在检测区域内持续丢失，此时直接置为 Removed
            if (splicing_output_targets_vec[sot].target_output.target_area_status == SplicingArea::TARGET_IN_DETECTION)
            {
                splicing_output_targets_vec[sot].target_output.splicing_state = SplicingTrackState::Removed;
#ifdef DEBUG1
                ZDEBUG("splicing_output_targets_vec IN DETECTION Removed channel_id:%d fusion_id:%d id:%d device_id:%d, due to lose target is area_in_detection,target_area_status:%d \n",
                       splicing_channel_id,
                       splicing_output_targets_vec[sot].target_output.fusion_id,
                       splicing_output_targets_vec[sot].target_output.id,
                       splicing_output_targets_vec[sot].target_output.device_id,
                       splicing_output_targets_vec[sot].target_output.target_area_status);
#endif
                continue;
            }

            // 如果目标的区域状态为 TARGET_IN_VANISH（处于消失区域）
            // 此时目标已进入消失区域，也直接置为 Removed
            if (splicing_output_targets_vec[sot].target_output.target_area_status == SplicingArea::TARGET_IN_VANISH)
            {
                splicing_output_targets_vec[sot].target_output.splicing_state = SplicingTrackState::Removed;
#ifdef DEBUG1
                ZDEBUG("splicing_output_targets_vec IN VANISH Removed channel_id:%d fusion_id:%d id:%d device_id:%d, due to lose target is area_in_detection,target_area_status:%d \n",
                       splicing_channel_id,
                       splicing_output_targets_vec[sot].target_output.fusion_id,
                       splicing_output_targets_vec[sot].target_output.id,
                       splicing_output_targets_vec[sot].target_output.device_id,
                       splicing_output_targets_vec[sot].target_output.target_area_status);
#endif
                continue;
            }

            // 如果目标具有预测坐标（预测值不为0），则计算预测值与实际检测值之间的距离
            if (fabsf(splicing_output_targets_vec[sot].target_predict_xcoor) > 0 || fabsf(splicing_output_targets_vec[sot].target_predict_ycoor) > 0)
            {
                // 计算 x 方向的预测误差：预测的 x 坐标与当前实际 x 坐标之间的绝对差值
                float x_predict_distance = fabsf(splicing_output_targets_vec[sot].target_predict_xcoor - splicing_output_targets_vec[sot].target_output.target_coor_in_base.x);
                // 计算 y 方向的预测误差：预测的 y 坐标与当前实际 y 坐标之间的绝对差值
                float y_predict_distance = fabsf(splicing_output_targets_vec[sot].target_predict_ycoor - splicing_output_targets_vec[sot].target_output.target_coor_in_base.y);
                // 根据 x 和 y 方向的误差计算欧氏距离作为总体预测误差
                float predict_distance = sqrt(pow(x_predict_distance, 2) + pow(y_predict_distance, 2));
                // 根据目标所在的区域状态决定允许的最大预测距离
                // 如果目标处于"死区"（TARGET_IN_DEAD），则允许的最大预测距离会增加 dead_predict_distance，否则使用 max_predict_distance
                float temp_max_predict_distance = splicing_output_targets_vec[sot].target_output.target_area_status == TARGET_IN_DEAD ? max_predict_distance + dead_predict_distance : max_predict_distance;
                // 如果预测误差超过允许的最大值，则置该目标状态为 Removed
                if (predict_distance > temp_max_predict_distance)
                {
                    splicing_output_targets_vec[sot].target_output.splicing_state = SplicingTrackState::Removed;
#ifdef DEBUG1
                    ZDEBUG("splicing_output_targets_vec PREDICT EXCEED Removed channel_id:%d fusion_id:%d id:%d device_id:%d, due to lose target predict exceed threshold,target_area_status:%d max_predict_distance:%f predict_distance:%f \n",
                           splicing_channel_id,
                           splicing_output_targets_vec[sot].target_output.fusion_id,
                           splicing_output_targets_vec[sot].target_output.id,
                           splicing_output_targets_vec[sot].target_output.device_id,
                           splicing_output_targets_vec[sot].target_output.target_area_status,
                           max_predict_distance,
                           predict_distance);
#endif
                    continue;
                }
            }
        }
        // 如果当前目标状态为 New（新检测到的目标）
        else if (splicing_output_targets_vec[sot].target_output.splicing_state == SplicingTrackState::New)
        {
            // 判断当前时间与目标的初始检测时间之间的差值
            // 如果差值超过预设的新目标保存时间阈值，则认为该新目标没有及时更新，需要删除
            if (abs(splicing_timestamp_ms - splicing_output_targets_vec[sot].target_output.target_timestamp_ms) > splicing_input_target_save_time_threshold)
            {
                splicing_output_targets_vec[sot].target_output.splicing_state = SplicingTrackState::Removed;

#ifdef DEBUG1
                ZDEBUG("splicing_output_targets_vec NEW TARGET TIMEOUT Removed channel_id:%d fusion_id:%d id:%d device_id:%d, due to new target timeout update, splicing_time_ms:%lld target_time_ms:%lld splicing_input_target_save_time_threshold:%lld\n",
                       splicing_channel_id,
                       splicing_output_targets_vec[sot].target_output.fusion_id,
                       splicing_output_targets_vec[sot].target_output.id,
                       splicing_output_targets_vec[sot].target_output.device_id,
                       splicing_timestamp_ms,
                       splicing_output_targets_vec[sot].target_output.target_timestamp_ms,
                       splicing_input_target_save_time_threshold);
#endif
                continue;
            }
        }
    } // end for 遍历所有目标更新状态

    // 对每个目标的轨迹队列进行清理，删除过旧的轨迹点，防止轨迹队列无限增长
    for (unsigned int sot = 0; sot < splicing_output_targets_vec.size(); sot++)
    {
        // 获取当前目标轨迹队列中的记录点个数
        size_t p_length = splicing_output_targets_vec[sot].target_output_path.size();
        // 如果轨迹记录点数不足，则不进行处理
        if (p_length <= 2)
        {
            continue;
        }
        // 计算该目标轨迹队列中最早记录和最新记录之间的时间差
        long long path_time = splicing_output_targets_vec[sot].target_output_path[p_length - 1].target_timestamp_ms -
                                splicing_output_targets_vec[sot].target_output_path[0].target_timestamp_ms;
        // 如果时间差超过预设的轨迹保存时间阈值，则删除最早的轨迹点
        if (path_time > splicing_fusion_path_time_threshold)
        {
            // 获取轨迹队列的起始迭代器，指向最早记录的点
            std::vector<TargetOutput>::iterator path_iter = splicing_output_targets_vec[sot].target_output_path.begin();
            // 删除最早的轨迹点
            splicing_output_targets_vec[sot].target_output_path.erase(path_iter);
        }
    } // end for 清理轨迹队列

    // 遍历整个输出目标集合，将状态为 Removed 的目标彻底删除，并释放关联的 Kalman 滤波平滑对象
    std::vector<SplicingTarget>::iterator splicing_output_targets_vec_iter;
    for (splicing_output_targets_vec_iter = splicing_output_targets_vec.begin();
         splicing_output_targets_vec_iter != splicing_output_targets_vec.end(); )
    {
        // 如果当前目标的状态已经标记为 Removed
        if (splicing_output_targets_vec_iter->target_output.splicing_state == SplicingTrackState::Removed)
        {
            // 保存目标关联的 Kalman 滤波器对象指针，用于后续内存释放
            KalmanSmooth *kf_smooth_delete = splicing_output_targets_vec_iter->target_kalman;
            // 从目标集合中删除该目标，并更新迭代器指向下一个有效元素
            splicing_output_targets_vec_iter = splicing_output_targets_vec.erase(splicing_output_targets_vec_iter);

            // 如果该目标的 Kalman 对象非空，则释放其占用的内存
            if (nullptr != kf_smooth_delete)
            {
                delete kf_smooth_delete;
                kf_smooth_delete = nullptr;
            }
        }
        else
        {
            // 如果目标状态不为 Removed，则继续遍历下一个目标
            splicing_output_targets_vec_iter++;
        }
    }
}

unsigned int TrackSplicing::get_fusion_id()
{
    splicing_fusion_id_count++;
    if (splicing_fusion_id_count > splicing_fusion_id_max)
    {
        splicing_fusion_id_count = splicing_fusion_id_min;
    }
    if (splicing_output_targets_vec.size() > 0)
    {
        for (unsigned int ci = 0; ci < splicing_fusion_id_max; ci++)
        {
            bool exist_repeat_fusion_id = false;
            for (unsigned int sot = 0; sot < splicing_output_targets_vec.size(); sot++)
            {
                if (splicing_output_targets_vec[sot].target_output.fusion_id == splicing_fusion_id_count)
                {
                    splicing_fusion_id_count++;
                    exist_repeat_fusion_id = true;
                    if (splicing_fusion_id_count > splicing_fusion_id_max)
                    {
                        splicing_fusion_id_count = splicing_fusion_id_min;
                    }
                    break;
                }
            }
            if (exist_repeat_fusion_id == false)
            {
                break;
            }
        }
    }
    return splicing_fusion_id_count;
}

void TrackSplicing::splicing_output_targets_vec_clean()
{
    for (unsigned int st = 0; st < splicing_output_targets_vec.size(); st++)
    {
        std::vector<TargetOutput>().swap(splicing_output_targets_vec[st].target_output_path);
    }
    std::vector<SplicingTarget>().swap(splicing_output_targets_vec);
}

void TrackSplicing::tracks_splicing_config_update(DeviceList device_list,
                                                  DevicesMap devices_map,
                                                  CalibCameraRadar *rccalib_vec,
                                                  int edgebox_id)
{
    ZINFO("tracks_splicing_config_update channel_id:%d device_list cnt:%d \n",
          splicing_channel_id, device_list.device_cnt);
    for (unsigned int dl = 0; dl < device_list.device_cnt; dl++)
    {
        double mercator_x = 0;
        double mercator_y = 0;
        Utils::lonlatToMercator(device_list.device_list[dl].device_longitude,
                                device_list.device_list[dl].device_latitude,
                                mercator_x,
                                mercator_y);
        device_list.device_list[dl].x = mercator_x;
        device_list.device_list[dl].y = mercator_y;
        ZINFO("tracks_splicing_config_update device_list device_id:%d device_enable:%d device_longitude:%f device_latitude:%f device_direction:%f x:%.2f y:%.2f\n",
              device_list.device_list[dl].device_id, device_list.device_list[dl].device_enable,
              device_list.device_list[dl].device_longitude, device_list.device_list[dl].device_latitude,
              device_list.device_list[dl].device_direction, device_list.device_list[dl].x,
              device_list.device_list[dl].y);
    }
    deviceListSort(&device_list);

    ZINFO("tracks_splicing_config_update channel_id:%d devices_map device_map_mats size:%d \n",
          splicing_channel_id, devices_map.device_map_mats.size());
    for (auto it = devices_map.device_map_mats.begin(); it != devices_map.device_map_mats.end(); ++it)
    {
        Eigen::Matrix3d device_map_mat = it->second.calib_mat_ob;
        std::string device_map_str = "";
        trackFusionManger::print_matrix3d_info(device_map_mat, device_map_str);
        ZINFO("tracks_splicing_config_update channel_id:%d devices_map device_id_key:%d device_mat:%s, device_id:%d \n",
              splicing_channel_id, it->first, device_map_str.c_str(), it->second.device_id);
    }

    ZINFO("tracks_splicing_config_update channel_id:%d rccalib_vec size:%d \n", splicing_channel_id, rccalib_vec->rv_calib_vec.size());
    for (unsigned int rc = 0; rc < rccalib_vec->rv_calib_vec.size(); rc++)
    {
        ZINFO("tracks_splicing_config_update channel_id:%d rc_device_id:%d bc_device_id:%d \n",
              splicing_channel_id, rccalib_vec->rv_calib_vec[rc].rc_calib_infos.rc_device.device_id, rccalib_vec->rv_calib_vec[rc].rc_calib_infos.bc_device.device_id);
        ZINFO("tracks_splicing_config_update channel_id:%d bc_device_info device_height:%.2f cmos_width:%.2f cmos_height:%.2f image_width:%d image_height:%d focus:%d \n",
              splicing_channel_id, rccalib_vec->rv_calib_vec[rc].rc_calib_infos.bc_device.device_height,
              rccalib_vec->rv_calib_vec[rc].rc_calib_infos.bc_device.cmos_width, rccalib_vec->rv_calib_vec[rc].rc_calib_infos.bc_device.cmos_height,
              rccalib_vec->rv_calib_vec[rc].rc_calib_infos.bc_device.image_width, rccalib_vec->rv_calib_vec[rc].rc_calib_infos.bc_device.image_height,
              rccalib_vec->rv_calib_vec[rc].rc_calib_infos.bc_device.focus);
        std::string camera_mat_str = "";
        std::string rotation_mat_str = "";
        std::string translation_mat_str = "";
        std::string rotation_mat_invert_str = "";
        std::string camera_mat_invert_str = "";
        trackFusionManger::print_cvmat_info(rccalib_vec->rv_calib_vec[rc].camera_mat, camera_mat_str);
        trackFusionManger::print_cvmat_info(rccalib_vec->rv_calib_vec[rc].rotation_mat, rotation_mat_str);
        trackFusionManger::print_cvmat_info(rccalib_vec->rv_calib_vec[rc].translation_mat, translation_mat_str);
        trackFusionManger::print_cvmat_info(rccalib_vec->rv_calib_vec[rc].rotation_mat_invert, rotation_mat_invert_str);
        trackFusionManger::print_cvmat_info(rccalib_vec->rv_calib_vec[rc].camera_mat_invert, camera_mat_invert_str);
        ZINFO("tracks_splicing_config_update channel_id:%d \ncamera_mat_str:%s \nrotation_mat_str:%s \ntranslation_mat_str:%s \nrotation_mat_invert_str:%s \ncamera_mat_invert_str:%s \n",
              splicing_channel_id, camera_mat_str.c_str(), rotation_mat_str.c_str(), translation_mat_str.c_str(), rotation_mat_invert_str.c_str(), camera_mat_invert_str.c_str());
    }

    splicing_devices_list = device_list;
    for (unsigned int sdl = 0; sdl < splicing_devices_list.device_cnt; sdl++)
    {
        double mer_x = 0;
        double mer_y = 0;
        Utils::lonlatToMercator(splicing_devices_list.device_list[sdl].device_longitude, splicing_devices_list.device_list[sdl].device_latitude, mer_x, mer_y);
        splicing_devices_list.device_list[sdl].x = mer_x;
        splicing_devices_list.device_list[sdl].y = mer_y;
    }

    ZINFO("tracks_splicing_config_update channel_id:%d splicing_devices_list cnt:%d \n",
          splicing_channel_id, splicing_devices_list.device_cnt);
    for (unsigned int dl = 0; dl < splicing_devices_list.device_cnt; dl++)
    {
        ZINFO("tracks_splicing_config_update device_list device_id:%d device_enable:%d device_longitude:%f device_latitude:%f device_direction:%f x:%.2f y:%.2f\n",
              splicing_devices_list.device_list[dl].device_id, splicing_devices_list.device_list[dl].device_enable,
              splicing_devices_list.device_list[dl].device_longitude, splicing_devices_list.device_list[dl].device_latitude,
              splicing_devices_list.device_list[dl].device_direction, splicing_devices_list.device_list[dl].x,
              splicing_devices_list.device_list[dl].y);
    }

    splicing_devices_map = devices_map;
    splicing_rvcalib = rccalib_vec;
    ZINFO("tracks_splicing_config_update splicing_devices_list cnt:%d splicing_devices_map size:%d\n",
          splicing_devices_list.device_cnt, splicing_devices_map.device_map_mats.size());

    splicing_fusion_id_count = edgebox_id * 10000;
    splicing_fusion_id_min = edgebox_id * 10000 + 1;
    splicing_fusion_id_max = edgebox_id * 10000 + (DEVICE_MAX_NUM * MAX_OBJECT_NUM);

    ZINFO("tracks_splicing_config_update splicing_fusion_id_count:%d splicing_fusion_id_min:%d splicing_fusion_id_max:%d \n",
          splicing_fusion_id_count, splicing_fusion_id_min, splicing_fusion_id_max);
}

/*
 * @Name: devices_speed_map_update
 * @Description: 设备内的目标平均速度的更新 一次处理多个设备
 *
 * @Input
 * multi_device_targets: 输入数据
 * devices_speed_map_: 输入数据的速度均值计算
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/8/7
 * Time: 11:24
 * Author: WangXing
 * Content: Create
 */
void TrackSplicing::devices_speed_map_update(const MultiDeviceTargets &multi_device_targets, std::map<unsigned int, std::vector<float>> &devices_speed_map_)
{
    // 遍历每一个设备
    for (size_t device_id = 0; device_id < multi_device_targets.device_cnt; device_id++)
    {
        // 获取每个设备
        // InputTargets input_targets = multi_device_targets.device_input[device_id];

        if (multi_device_targets.device_input[device_id].device_type == DeviceType::Bayonet_Camera_Device)
        {
        }
        else
        {
            float speed_sum = 0.0f;
            float speed_count = 0.0f;
            for (unsigned int it = 0; it < multi_device_targets.device_input[device_id].target_cnt; it++)
            {
                if (multi_device_targets.device_input[device_id].targets.target_devices[it].y < lose_number_plate_distance)
                {
                    continue;
                }
                speed_sum += multi_device_targets.device_input[device_id].targets.target_devices[it].speed;
                speed_count++;
            }
            float mean_speed = 0.0f;
            if (speed_count > 1e-4)
            {
                mean_speed = speed_sum / speed_count;
            }
            if (devices_speed_map_.find(multi_device_targets.device_input[device_id].device_id) == devices_speed_map_.end())
            {
                std::vector<float> speed_vec = {mean_speed};
                devices_speed_map_.insert(std::make_pair(multi_device_targets.device_input[device_id].device_id, speed_vec));
            }
            else
            {
                devices_speed_map_[multi_device_targets.device_input[device_id].device_id].push_back(mean_speed);
            }
        }
        for (auto it = devices_speed_map_.begin(); it != devices_speed_map_.end();)
        {
            if (it->second.size() > 6)
            {
                it->second.erase(it->second.begin());
            }
            it++;
        }
    }
}

double Angle2Radian(double value)
{
    return value * M_PI / 180.0;
}

double Radian2Angle(double value)
{
    return value * 180.0 / M_PI;
}

void targetGPSPos(double curLng, double curLat, double bearing, double distance, double &newLng, double &newLat)
{
    // extrapolate latitude/longitude given a heading and distance
    // thanks to http://www.movable-type.co.uk/scripts/latlong.html
    // from math import sin, asin, cos, atan2, radians, degrees
    double radius_of_earth = 6378100.0; // # in meters

    double lat1 = Angle2Radian(curLat);
    double lon1 = Angle2Radian(curLng);
    double brng = Angle2Radian(bearing);
    double dr = distance / radius_of_earth;

    double lat2 = asin(sin(lat1) * cos(dr) + cos(lat1) * sin(dr) * cos(brng));
    double lon2 = lon1 + atan2(sin(brng) * sin(dr) * cos(lat1), cos(dr) - sin(lat1) * sin(lat2));
    newLat = Radian2Angle(lat2);
    newLng = Radian2Angle(lon2);
}

/*
 * @Name:
 * @Description:
 *
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/10/23
 * Time: 16:26
 * Author: WangXing
 * Content: Create
 */
double TrackSplicing::calculateAngle(const CalibPoint &A, const CalibPoint &B)
{
    double deltaX = B.x - A.x;
    double deltaY = B.y - A.y;

    // 计算向量的模长
    double magnitude = std::sqrt(deltaX * deltaX + deltaY * deltaY);

    // 计算与x轴正方向的夹角的余弦值
    double cosineAngle = deltaX / magnitude;

    // 计算角度（弧度）
    double angleRadians = std::acos(cosineAngle);

    // 将角度转换为度数
    double angleDegrees = angleRadians * (180.0 / M_PI);

    // 由于acos返回的是0到π之间的值，所以这里的角度是0到180度之间的
    // 如果需要得到完整的角度（0到360度），可以根据deltaY的符号来判断
    if (deltaY < 0)
    {
        angleDegrees = 360.0 - angleDegrees;
    }

    // 注意：这里假设了角度是在0到360度之间，且逆时针为正方向。

    return angleDegrees;
}

/*
 * @Name:compare_device_info
 * @Description: 两个设备的排序
 *
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/10/30
 * Time: 11:13
 * Author: WangXing
 * Content: Create
 */
int compare_device_info(const void *a, const void *b)
{
    DeivceInfo *deviceA = (DeivceInfo *)a;
    DeivceInfo *deviceB = (DeivceInfo *)b;
    return (int)(deviceA->device_id - deviceB->device_id);
}

/*
 * @Name: deviceListSort
 * @Description: 对DeviceList中的设备信息按照编号由小到大进行排序
 *
 * @Input
 * DeviceList: device_list
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/10/30
 * Time: 11:10
 * Author: WangXing
 * Content: Create
 */
void TrackSplicing::deviceListSort(DeviceList *list)
{
    if (list->device_cnt > 0)
    {
        qsort(list->device_list, list->device_cnt, sizeof(DeivceInfo), compare_device_info);
    }
}

/*
 * @Name:calculateRectangleCorners
 * @Description:计算以给定点为中心，边与给定斜率平行的矩形的四个顶点坐标，并按顺序排列
 *
 * @Input
 * centerPoint:给定目标的中心点
 * slope: 斜率
 * width: 矩形的宽度
 * height: 矩形的高度
 *
 * @Output
 * std::vector<cv::Point2d>: 返回矩形
 *
 * @Edit History
 * Date: 2024/11/20
 * Time: 13:24
 * Author: WangXing
 * Content: Create
 */
std::vector<CalibPoint> TrackSplicing::calculateRectangleCorners(CalibPoint centerPoint, double angle_slope, double width, double height)
{

    auto endpoints = calculate_segment_endpoints(centerPoint, angle_slope, height);

    std::vector<CalibPoint> corners = calculate_parallel_segments_with_rectangle(endpoints.first.x, endpoints.first.y,
                                                                                 endpoints.second.x, endpoints.second.y,
                                                                                 width);
    return corners;
}

std::vector<CalibPoint> TrackSplicing::calculate_parallel_segments_with_rectangle(double x1, double y1, double x2, double y2, double offset)
{
    // 计算线段A的方向角
    double dx = x2 - x1;
    double dy = y2 - y1;
    double angle = atan2(dy, dx);

    // 计算垂直方向的单位向量
    double perp_dx = -sin(angle);
    double perp_dy = cos(angle);

    // 偏移后线段的端点
    float line1_start_x = x1 + offset * perp_dx;
    float line1_start_y = y1 + offset * perp_dy;
    float line1_end_x = x2 + offset * perp_dx;
    float line1_end_y = y2 + offset * perp_dy;

    float line2_start_x = x1 - offset * perp_dx;
    float line2_start_y = y1 - offset * perp_dy;
    float line2_end_x = x2 - offset * perp_dx;
    float line2_end_y = y2 - offset * perp_dy;

    // 矩形顶点
    std::vector<CalibPoint> rectangle_points = {
        {line1_start_x, line1_start_y},
        {line1_end_x, line1_end_y},
        {line2_end_x, line2_end_y},
        {line2_start_x, line2_start_y}};

    // 返回矩形顶点
    return rectangle_points;
}

/*
 * @Name: calculate_segment_endpoints
 * @Description: 计算线段端点的函数
 *
 * @Input
 * centerPoint: 待计算的中心点
 * angle: 角度
 * length: 移动距离
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/11/20
 * Time: 17:27
 * Author: WangXing
 * Content: Create
 */
std::pair<CalibPoint, CalibPoint> TrackSplicing::calculate_segment_endpoints(CalibPoint centerPoint, double angle, double length)
{
    double radians = angle / 180.0 * M_PI; // 将角度转换为弧度
    double half_length = length / 2.0;

    double x2 = centerPoint.x + half_length * std::cos(radians);
    double y2 = centerPoint.y + half_length * std::sin(radians);
    double x3 = centerPoint.x - half_length * std::cos(radians);
    double y3 = centerPoint.y - half_length * std::sin(radians);

    // 使用round函数进行四舍五入，注意C++中的round函数返回double类型
    // 但我们可以将其转换为int类型后再赋值给Point结构体的成员，这里为了模拟Python的行为，我们保留小数位
    CalibPoint p2 = {std::round(x2 * 10.0) / 10.0, std::round(y2 * 10.0) / 10.0}; // 保留一位小数
    CalibPoint p3 = {std::round(x3 * 10.0) / 10.0, std::round(y3 * 10.0) / 10.0}; // 保留一位小数

    return std::make_pair(p2, p3);
}

bool TrackSplicing::judgePointInRect(std::vector<CalibPoint> polygon, CalibPoint point)
{
    std::vector<cv::Point> cv_polygon;
    for (size_t py = 0; py < polygon.size(); py++)
    {
        cv::Point polygon_point =
            {
                polygon[py].x,
                polygon[py].y};
        cv_polygon.push_back(polygon_point);
    }
    cv::Point cv_point = {
        point.x,
        point.y};
    double result = cv::pointPolygonTest(cv_polygon, cv_point, false);
    // 判断结果
    if (result > 0)
    {
        // std::cout << "点在多边形内部" << std::endl;
        return true;
    }
    else if (result == 0)
    {
        // std::cout << "点在多边形边界上" << std::endl;
        return true;
    }
    else
    {
        // std::cout << "点在多边形外部" << std::endl;
        return false;
    }
}

// 打印 MultiBoxTargets 结构体的所有属性(一行格式)
void TrackSplicing::print_box_targets(long long timestamp_ms) {
    if(log_info_level == 0) {
        return;
    }else if(log_info_level == 1){
        ZINFO("splicing_input_target_vec splicing_input_target_vec.size:%d fps:%d \n", splicing_input_target_vec.size(), splicing_fps);
        ZINFO("multi_boxes_output: splicing_output_targets target_cnt:%d \n", splicing_output_targets.target_cnt);
    }else if(log_info_level == 2){
        // 输入打印
        ZINFO("splicing_input_target_vec splicing_input_target_vec.size:%d fps:%d \n", splicing_input_target_vec.size(), splicing_fps);
        for (unsigned int sot = 0; sot < splicing_input_target_vec.size(); sot++)
        {
            ZINFO("splicing_input_target_vec id:%d fusion_id:%d device_id:%d bx:%.2f by:%.2f longitude:%.7f latitude:%.7f target_direction:%.2f distance:%.2f x:%.2f y:%.2f s:%.2f target_area_status:%d,n_plate:%s np_score:%.2f p_color:%d pc_score:%.2f v_color:%d vc_score:%.2f v_type:%d vt_socre:%.2f tt_type:%d, tt_score:%.2f time:%lld\n",
                splicing_input_target_vec[sot].id, splicing_input_target_vec[sot].target_input.fusion_id, splicing_input_target_vec[sot].device_id,
                splicing_input_target_vec[sot].target_input.target_coor_in_base.x, splicing_input_target_vec[sot].target_input.target_coor_in_base.y,
                splicing_input_target_vec[sot].target_input.target.longitude, splicing_input_target_vec[sot].target_input.target.latitude,
                splicing_input_target_vec[sot].target_input.target.direction, splicing_input_target_vec[sot].target_input.target.distance,
                splicing_input_target_vec[sot].target_input.target.x, splicing_input_target_vec[sot].target_input.target.y,
                splicing_input_target_vec[sot].target_input.target.speed,
                splicing_input_target_vec[sot].target_area_status,
                splicing_input_target_vec[sot].target_input.target.number_plate, splicing_input_target_vec[sot].target_input.target.np_score,
                splicing_input_target_vec[sot].target_input.target.plate_color, splicing_input_target_vec[sot].target_input.target.pc_score,
                splicing_input_target_vec[sot].target_input.target.vehicle_color, splicing_input_target_vec[sot].target_input.target.vc_score,
                splicing_input_target_vec[sot].target_input.target.vehicle_type, splicing_input_target_vec[sot].target_input.target.vt_score,
                splicing_input_target_vec[sot].target_input.target.target_type, splicing_input_target_vec[sot].target_input.target.tt_score,
                splicing_input_target_vec[sot].target_input.time_ms);
        }
        ZINFO("splicing_input_target_vec end\n");
        ZINFO("\n");
        // 输出打印
        ZINFO("multi_boxes_output: splicing_output_targets target_cnt:%d \n", splicing_output_targets.target_cnt);
            for (size_t sot = 0; sot < splicing_output_targets.target_cnt; sot++)
            {
                ZINFO("multi_boxes_output: splice_id:%d device_id:%d id:%d splicing_x:%f splicing_y:%f long:%.7f lat:%.7f long_input:%.7f lat_input:%.7f timestamp_ms:%lld direction:%.2f \n",
                    splicing_output_targets.fusion_targets[sot].splice_id,
                    splicing_output_targets.fusion_targets[sot].device_id,
                    splicing_output_targets.fusion_targets[sot].splice_target.id,
                    splicing_output_targets.fusion_targets[sot].splicing_x,
                    splicing_output_targets.fusion_targets[sot].splicing_y,
                    splicing_output_targets.fusion_targets[sot].splice_target.longitude,
                    splicing_output_targets.fusion_targets[sot].splice_target.latitude,
                    splicing_output_targets.fusion_targets[sot].splice_target.longitude,
                    splicing_output_targets.fusion_targets[sot].splice_target.latitude,
                    timestamp_ms,
                    splicing_output_targets.fusion_targets[sot].splice_target.direction);
            }

            ZINFO("multi_boxes_output: splicing_output_targets_next target_cnt:%d \n", splicing_output_targets_next.target_cnt);
            for (size_t sot = 0; sot < splicing_output_targets_next.target_cnt; sot++)
            {
                ZINFO("multi_boxes_output: splice_id:%d device_id:%d id:%d splicing_x:%f splicing_y:%f long:%.7f lat:%.7f long_input:%.7f lat_input:%.7f timestamp_ms:%lld direction:%.2f \n",
                    splicing_output_targets_next.fusion_targets[sot].splice_id,
                    splicing_output_targets_next.fusion_targets[sot].device_id,
                    splicing_output_targets_next.fusion_targets[sot].splice_target.id,
                    splicing_output_targets_next.fusion_targets[sot].splicing_x,
                    splicing_output_targets_next.fusion_targets[sot].splicing_y,
                    splicing_output_targets_next.fusion_targets[sot].splice_target.longitude,
                    splicing_output_targets_next.fusion_targets[sot].splice_target.latitude,
                    splicing_output_targets_next.fusion_targets[sot].splice_target.longitude,
                    splicing_output_targets_next.fusion_targets[sot].splice_target.latitude,
                    timestamp_ms,
                    splicing_output_targets.fusion_targets[sot].splice_target.direction);
            }
        ZINFO("\n output end \n");
    }
}


/**
 * 检查每个目标的id是否大于1000，小于1000则设置为设备id*1000+targetid
 */
void TrackSplicing::check_update_multi_box_targets(tecu1000_alg::MultiBoxTargets& multi_box_targets, long long timestamp_ms)
{
    for (unsigned int mdt = 0; mdt < multi_box_targets.multi_device_targets.device_cnt; mdt++)
    {
        multi_box_targets.multi_device_targets.device_input[mdt].timestamp_ms = timestamp_ms;
    }
    // 处理多设备目标信息
    tecu1000_alg::MultiDeviceTargets& mdt = multi_box_targets.multi_device_targets;

    // 遍历每个设备
    for (unsigned int i = 0; i < mdt.device_cnt; i++) {
        tecu1000_alg::InputTargets& device = mdt.device_input[i];
        unsigned int device_id = device.device_id;

        // 遍历每个设备的目标
        for (unsigned int j = 0; j < device.target_cnt; j++) {
            tecu1000_alg::Target& target = device.targets.target_devices[j];


        // 检查目标ID是否小于1000
        if (target.id > 1000) {
            // 删除第一位
            target.id = target.id % 1000;
        }
        }
    }

    // 处理融合目标信息
    tecu1000_alg::OutputTargets& lot = multi_box_targets.last_output_target;

    // 遍历每个融合目标
    for (unsigned int i = 0; i < lot.target_cnt; i++) {
        tecu1000_alg::OutputTarget& ot = lot.fusion_targets[i];
        unsigned int device_id = ot.device_id;
        tecu1000_alg::Target& target = ot.splice_target;

        // 检查目标ID是否小于1000
        if (target.id > 1000) {
            // 删除第一位
            target.id = target.id % 1000;
        }
    }
}