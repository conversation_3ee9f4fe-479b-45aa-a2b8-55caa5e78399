#include "tracks_splicing.h"
#include "pipeline_track_fusion.h"
#include "kalman_filter.h"
#include "Dense"
#include "zlogger.h"

ZLOGGER_HEADER_DEFINE("ALG_TARGETS_REASSOCIATE")

using namespace tecu_r1000_0_algorithm;

/*
graph TD
    A[开始重新关联] --> B[初始化存储容器]
    B --> C[遍历所有目标]
    C --> D{路径为空?}
    D -->|是| E[标记为Removed]
    D -->|否| F{坐标为0?}
    F -->|是| G[标记为Removed]
    F -->|否| H{状态判断}
    H -->|Lost| I[运动模型预测]
    I --> J{预测成功?}
    J -->|否| K[标记为Removed]
    J -->|是| L[加入丢失目标列表]
    H -->|New| M[加入新目标列表]
    
    L & M --> N{列表是否为空?}
    N -->|是| O[直接返回]
    N -->|否| P[调整列表大小]
    P --> Q[填充空目标]
    Q --> R[调用匹配函数]
    
    subgraph 匹配过程
    R --> S[计算道路方向]
    S --> T[生成匹配区域]
    T --> U[多维度匹配]
    U --> V[车牌匹配]
    U --> W[车型颜色匹配]
    U --> X[速度匹配]
    U --> Y[坐标误差匹配]
    V & W & X & Y --> Z[KM算法最优匹配]
    end
    
    Z --> AA[处理匹配结果]
    AA --> AB{有效匹配?}
    AB -->|是| AC[创建新目标]
    AC --> AD[继承融合ID]
    AC --> AE[更新轨迹信息]
    AB -->|否| AF[跳过]
    
    AD & AE --> AG[标记已关联目标]
    AG --> AH[更新目标状态]
    AH --> AI[结束流程]
*/

/// @brief 丢失目标与新目标的重新关联
void TrackSplicing::splicing_output_targets_reassociate()
{
	// 对拼接输出目标进行分类处理
	TargetClassificationResult classification_result = classifyAndPredictTargets(splicing_output_targets_vec, splicing_channel_id);

	// 获取分类结果
	std::vector<SplicingTarget> splicing_output_lose_vec = classification_result.lose_targets;
	std::vector<unsigned int> splicing_output_lose_index_vec = classification_result.lose_indices;
	std::vector<SplicingTarget> splicing_output_new_vec = classification_result.new_targets;
	std::vector<unsigned int> splicing_output_new_index_vec = classification_result.new_indices;

	// 如果没有待匹配目标则直接返回
	if (splicing_output_lose_vec.size() == 0 || splicing_output_new_vec.size() == 0)
	{
		return;
	}

	// 调整两个列表大小使其相等（KM算法需要方阵）
	// 用空目标填充较短的列表（坐标为0的目标会被后续流程自动过滤）
	size_t weight_size_t = std::max(splicing_output_lose_vec.size(), splicing_output_new_vec.size());
	unsigned int weight_size = static_cast<unsigned int>(weight_size_t);
	
	// 填充丢失目标列表
	unsigned int sub_output_size = weight_size - splicing_output_lose_vec.size();
	for (unsigned soz = 0; soz < sub_output_size; soz++) {
		// 创建空目标用于占位
		SplicingTarget sub_splicing_target_lose;
		sub_splicing_target_lose.target_output.target_coor_in_base.x = 0;
		sub_splicing_target_lose.target_output.target_coor_in_base.y = 0;
		splicing_output_lose_vec.push_back(sub_splicing_target_lose);
	}

	unsigned int sub_input_size = weight_size - splicing_output_new_vec.size();
	if (sub_input_size > 0)
	{
		for (unsigned siz = 0; siz < sub_input_size; siz++)
		{
			SplicingTarget sub_splicing_target_new;
			sub_splicing_target_new.target_output.target_coor_in_base.x = 0;
			sub_splicing_target_new.target_output.target_coor_in_base.y = 0;
			splicing_output_new_vec.push_back(sub_splicing_target_new);
		}
	}

	// 进行目标匹配
	std::vector<std::vector<double>> match_weights;
	int *match = splcing_target_lose_match(splicing_output_lose_vec, splicing_output_new_vec, match_weights);

	// 处理匹配结果
	std::vector<unsigned int> splicing_output_new_reassociate_index;
	for (unsigned int d = 0; d < splicing_output_new_vec.size(); d++)
	{
		// 过滤无效匹配（占位目标）
		if ((splicing_output_lose_vec[match[d]].target_output.target_coor_in_base.x == 0 
		  && splicing_output_lose_vec[match[d]].target_output.target_coor_in_base.y == 0)
		 || (splicing_output_new_vec[d].target_output.target_coor_in_base.x == 0 
		  && splicing_output_new_vec[d].target_output.target_coor_in_base.y == 0))
		{
			continue;
		}

		// 获取匹配权重并判断是否有效
		double target_match_weight = match_weights[match[d]][d];
		if (target_match_weight < spilcing_default_match_weight * (1.0 - lose_match_weigth_threshold))
		{
			// 创建新目标输出
			TargetOutput new_target_output;
			// 继承新目标的属性
			new_target_output.id = splicing_output_new_vec[d].target_output.id;
			new_target_output.device_id = splicing_output_new_vec[d].target_output.device_id;
			new_target_output.target_area_status = splicing_output_new_vec[d].target_output.target_area_status;
			new_target_output.target = splicing_output_new_vec[d].target_output.target;
			new_target_output.target_coor_in_base.x = splicing_output_new_vec[d].target_output.target_coor_in_base.x;
			new_target_output.target_coor_in_base.y = splicing_output_new_vec[d].target_output.target_coor_in_base.y;
			new_target_output.target_coor_predict = new_target_output.target_coor_in_base;

			new_target_output.target_timestamp_ms = splicing_output_new_vec[d].target_output.target_timestamp_ms;

			// 继承丢失目标的融合ID和起始时间
			unsigned int temp_target_output_fusion_id = splicing_output_targets_vec[splicing_output_lose_index_vec[match[d]]].target_output.fusion_id;
			long long target_start_time_ms = splicing_output_targets_vec[splicing_output_lose_index_vec[match[d]]].target_output.target_start_timestamp_ms;
			SplicingTrackState temp_target_output_state = SplicingTrackState::Tracked;
			
			new_target_output.fusion_id = temp_target_output_fusion_id;
			new_target_output.splicing_state = temp_target_output_state;
			new_target_output.target_start_timestamp_ms = target_start_time_ms;

			TargetTrace new_target_input;
			memcpy(new_target_input.target.number_plate, splicing_output_new_vec[d].target_output.target.number_plate, sizeof(new_target_input.target.number_plate));
			new_target_input.target = splicing_output_new_vec[d].target_output.target;

			splicing_target_info_update(new_target_output, 
				splicing_output_targets_vec[splicing_output_lose_index_vec[match[d]]].target_output,
				new_target_input);
#ifdef DEBUG1
			// 输出拼接误差
			// 如果存在车牌，并且车牌相同，则认为是正确拼接
			bool is_correct_splicing = false;
			if (new_target_output.target.number_plate[0] != '\0' && splicing_output_targets_vec[splicing_output_lose_index_vec[match[d]]].target_output.target.number_plate[0] != '\0' &&
			strcmp(new_target_output.target.number_plate, splicing_output_targets_vec[splicing_output_lose_index_vec[match[d]]].target_output.target.number_plate) == 0)
			{
				is_correct_splicing = true;
			}
			float splicing_error_x = new_target_output.target_coor_in_base.x - splicing_output_targets_vec[splicing_output_lose_index_vec[match[d]]].target_predict_xcoor;
			float splicing_error_y = new_target_output.target_coor_in_base.y - splicing_output_targets_vec[splicing_output_lose_index_vec[match[d]]].target_predict_ycoor;
			ZDEBUG("splicing_output_targets_reassociate success channel_id:%d fusion_id:%d splicing_state:%d lose_id:%d lose_device_id:%d new_id:%d new_device_id:%d splicing_error_x:%f splicing_error_y:%f is_correct_splicing:%d \n",
				   splicing_channel_id, temp_target_output_fusion_id, temp_target_output_state,
				   splicing_output_targets_vec[splicing_output_lose_index_vec[match[d]]].target_output.id, splicing_output_targets_vec[splicing_output_lose_index_vec[match[d]]].target_output.device_id,
				   new_target_output.id, new_target_output.device_id, splicing_error_x, splicing_error_y, is_correct_splicing);
#endif
			splicing_output_targets_vec[splicing_output_lose_index_vec[match[d]]].target_output = new_target_output;
			splicing_output_targets_vec[splicing_output_lose_index_vec[match[d]]].target_output_path.push_back(new_target_output);

			splicing_output_new_reassociate_index.push_back(splicing_output_new_index_vec[d]);
		}
	}

	// 移除已重新关联的新目标
	for (unsigned int sot = 0; sot < splicing_output_targets_vec.size(); sot++)
	{
		for (auto son : splicing_output_new_reassociate_index) {
			if (sot == son) {
#ifdef DEBUG1
				ZDEBUG("splicing_output_targets_reassociate NEW_TARGET Removed,que to target is reassociate, channel_id:%d fusion_id:%d splicing_state:%d id:%d device_id:%d \n",
					splicing_channel_id, splicing_output_targets_vec[sot].target_output.fusion_id, splicing_output_targets_vec[sot].target_output.splicing_state,
					splicing_output_targets_vec[sot].target_output.id, splicing_output_targets_vec[sot].target_output.device_id);
#endif
				splicing_output_targets_vec[sot].target_output.splicing_state = SplicingTrackState::Removed;
				break;
			}
		}
	}
}

/*
 * @Name: splicing_output_targets_second_reassociate
 * @Description: 二次匹配
 * 针对预测后的目标与新出现的目标距离差距过大，遵循目标不会凭空消失的原则，对没有匹配成功的新出现的剩余目标和loss目标进行大范围的匹配
 * 修改变量：与splicing_output_targets_reassociate函数类似
 * 
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2025/06/03
 * Time: 11:54
 * Author: JiaTao
 * Content: 新增
 */
void TrackSplicing::splicing_output_targets_second_reassociate()
{
	// 对拼接输出目标进行分类处理
	TargetClassificationResult classification_result = classifyAndPredictTargets(splicing_output_targets_vec, splicing_channel_id);

	// 获取分类结果
	std::vector<SplicingTarget> splicing_output_lose_vec = classification_result.lose_targets;
	std::vector<unsigned int> splicing_output_lose_index_vec = classification_result.lose_indices;
	std::vector<SplicingTarget> splicing_output_new_vec = classification_result.new_targets;
	std::vector<unsigned int> splicing_output_new_index_vec = classification_result.new_indices;

	// 如果没有待匹配目标则直接返回
	if (splicing_output_lose_vec.size() == 0 || splicing_output_new_vec.size() == 0)
	{
		return;
	}
    
}

/*
 * @Name: splcing_target_lose_match
 * @Description: 丢失目标与新目标的匹配函数
 *
 * @Input
 * splicing_output_lose_vec: 丢失目标的向量
 * splicing_output_new_vec: 新目标的向量
 * match_weight: 匹配权重矩阵(引用传递)
 *
 * @Output
 * return: 返回匹配结果数组
 */
int* TrackSplicing::splcing_target_lose_match(std::vector<SplicingTarget> splicing_output_lose_vec, 
    std::vector<SplicingTarget> splicing_output_new_vec, 
    std::vector<std::vector<double>> &match_weight)
{
    // 初始化匹配权重矩阵大小
    match_weight.resize(splicing_output_lose_vec.size());
    for (size_t i = 0; i < match_weight.size(); i++) {
        match_weight[i].resize(splicing_output_lose_vec.size());
    }

    // 遍历每个丢失的目标
    for (unsigned int r = 0; r < match_weight.size(); r++) {
        // 获取丢失目标的预测位置点
        CalibPoint predict_point = {
            splicing_output_lose_vec[r].target_predict_xcoor,
            splicing_output_lose_vec[r].target_predict_ycoor
        };

        // 获取道路方向
        float road_direction = 0.0f;
        int direction_ret = roadDirectionCacl->get_road_direction(
            splicing_output_lose_vec[r].target_output.device_id,
            predict_point,
            road_direction);
        if(direction_ret == -1) {
            road_direction = 0.0f;
        }

        // 计算预测点周围的两个矩形区域(用于匹配容差)
        // 第一个矩形区域使用较小的阈值
        std::vector<CalibPoint> splicing_rect = calculateRectangleCorners(
            predict_point,
            road_direction,
            lose_x_threshold,
            lose_y_threshold);
        // 第二个矩形区域使用较大的阈值
        std::vector<CalibPoint> splicing_rect_second = calculateRectangleCorners(
            predict_point,
            road_direction,
            second_lose_x_threshold,
            second_lose_y_threshold);
        
        // 计算坐标误差阈值
        float coor_error_threshold = sqrt(pow(second_lose_x_threshold, 2) + pow(second_lose_y_threshold, 2));

        // 记录最小坐标误差及其对应的索引
        float min_coor_error = FLT_MAX;
        unsigned int min_coor_error_index = 0;
        bool rematch_success = false;

        // 遍历每个新目标,计算匹配权重
        for (unsigned int d = 0; d < match_weight.size(); d++) {
            // 如果目标坐标接近原点,则设置默认权重
            if ((fabs(splicing_output_lose_vec[r].target_output.target_coor_in_base.x) < 1e-4 
                && fabs(splicing_output_lose_vec[r].target_output.target_coor_in_base.y) < 1e-4)
                || (fabs(splicing_output_new_vec[d].target_output.target_coor_in_base.x) < 1e-4 
                && fabs(splicing_output_new_vec[d].target_output.target_coor_in_base.y) < 1e-4)) {
                match_weight[r][d] = spilcing_default_match_weight;
            } else {
                // 判断是否为相邻设备
                bool is_near_devices = false;
                if(splicing_output_new_vec[d].target_output.device_id - splicing_output_lose_vec[r].target_output.device_id == 1
                    || splicing_output_new_vec[d].target_output.device_id - splicing_output_lose_vec[r].target_output.device_id == 0) {
                    is_near_devices = true;
                }
                
                // 如果不是相邻设备,则跳过匹配
                if (is_near_devices == false) {
                    match_weight[r][d] = spilcing_default_match_weight * (1.0f - 0.0f);
                    continue;
                }

                // 计算位置误差
                float temp_lose_x_error = fabs(splicing_output_lose_vec[r].target_predict_xcoor 
                    - splicing_output_new_vec[d].target_output.target_coor_in_base.x);
                float temp_lose_y_error = fabs(splicing_output_lose_vec[r].target_predict_ycoor 
                    - splicing_output_new_vec[d].target_output.target_coor_in_base.y);
                float temp_lose_coor_error = sqrt(pow(temp_lose_x_error, 2) + pow(temp_lose_y_error, 2));
                // 更新最小误差
                if(temp_lose_coor_error < min_coor_error) {
                    min_coor_error = temp_lose_coor_error;
                    min_coor_error_index = d;
                }

#ifdef FRONT_BACK_SOLUTION
                // 前后方案:如果目标距离较近且无车牌号,则跳过匹配
                if (splicing_output_new_vec[d].target_output.target.y < lose_number_plate_distance
                    && splicing_output_new_vec[d].target_output.device_id != splicing_devices_map.base_device_id
                    && splicing_output_new_vec[d].target_output.target.number_plate[0] == '\0') {
                    match_weight[r][d] = spilcing_default_match_weight * (1.0f - 0.0f);
                    continue;
                }
#endif

                // 目标类型匹配检查
                if (splicing_output_new_vec[d].target_output.target.target_type == TargetType::TARGET_TYPE_DOGCART
                    && (splicing_output_lose_vec[r].target_output.target.target_type == TargetType::TARGET_TYPE_SMALL_VEHICLE
                    || splicing_output_lose_vec[r].target_output.target.target_type == TargetType::TARGET_TYPE_MEDIUM_VEHICLE
                    || splicing_output_lose_vec[r].target_output.target.target_type == TargetType::TARGET_TYPE_BIG_VEHICLE)) {
                    match_weight[r][d] = spilcing_default_match_weight * (1.0f - 0.0f);
                    continue;
                }

                // 运动方向检查
                if(fabs(splicing_output_lose_vec[r].target_output.target.direction) < 1e-4
                    || fabs(splicing_output_new_vec[d].target_output.target.direction) < 1e-4) {
                    match_weight[r][d] = spilcing_default_match_weight * (1.0f - 0.0f);
                    continue;
                }

                // 方向差异检查
                if(fabs(splicing_output_lose_vec[r].target_output.target.direction 
                    - splicing_output_new_vec[d].target_output.target.direction) > splicing_match_direction_threshold) {
                    match_weight[r][d] = spilcing_default_match_weight * (1.0f - 0.0f);
                    continue;
                }

                // 检查目标是否在预测矩形区域内
                bool target_in_rect = judgePointInRect(splicing_rect, 
                    splicing_output_new_vec[d].target_output.target_coor_in_base);

                // 如果目标在第一个矩形区域内
                if(target_in_rect == true) {
                    // 车牌号匹配
                    float plate_score = 0;
                    int ck_ret = checkout_plate_ipentity(
                        splicing_output_lose_vec[r].target_output.target.number_plate,
                        splicing_output_new_vec[d].target_output.target.number_plate,
                        lose_plate_top1_score,
                        lose_plate_top2_score,
                        plate_score);
                        
                    // 车牌匹配成功
                    if(ck_ret == 0) {
                        match_weight[r][d] = spilcing_default_match_weight * (1.0f - plate_score);
                        rematch_success = true;
# ifdef DEBUG1
                if(rematch_success == true) {
                    ZINFO("匹配成功的误差 device_id:%d id:%d temp_lose_coor_error:%f temp_lose_x_error:%f temp_lose_y_error:%f \n",
                          splicing_output_lose_vec[r].target_output.device_id, splicing_output_lose_vec[r].target_output.id, temp_lose_coor_error, temp_lose_x_error, temp_lose_y_error);
                }
# endif
                    } else {
                        // 车型和颜色匹配
                        float lose_speed_error = fabs(splicing_output_lose_vec[r].target_output.target.speed 
                            - splicing_output_new_vec[d].target_output.target.speed);
                            
                        // 如果车型和颜色都匹配
                        if(splicing_output_lose_vec[r].target_output.target.vehicle_type == splicing_output_new_vec[d].target_output.target.vehicle_type
                            && splicing_output_lose_vec[r].target_output.target.vehicle_color == splicing_output_new_vec[d].target_output.target.vehicle_color
                            && splicing_output_lose_vec[r].target_output.target.vehicle_type != VehicleType::VEHICLE_TYPE_UNKNOWN
                            && splicing_output_lose_vec[r].target_output.target.vehicle_color != VehicleColor::VEHICLE_COLOR_UNKNOWN) {
                            match_weight[r][d] = spilcing_default_match_weight * (1.0f - lose_plate_top2_score);
                            rematch_success = true;
                            continue;
                        }
                        // 速度匹配
                        else if(lose_speed_error < lose_speed_threshold) {
                            double lose_speed_weight = (lose_speed_threshold - lose_speed_error) / lose_speed_threshold;
                            match_weight[r][d] = spilcing_default_match_weight * (1.0f - lose_speed_weight);
                            rematch_success = true;
                        } else {
                            // 检查是否在第二个矩形区域内
                            bool target_in_rect_second = judgePointInRect(splicing_rect_second, 
                                splicing_output_new_vec[d].target_output.target_coor_in_base);
                            if(target_in_rect_second == true) {
                                double lose_coor_weight = (coor_error_threshold - temp_lose_coor_error) / coor_error_threshold;
                                match_weight[r][d] = spilcing_default_match_weight * (1.0f - lose_coor_weight);
                                rematch_success = true;
                            } else {
                                match_weight[r][d] = spilcing_default_match_weight * (1.0f - 0.0f);
                            }
                        }
                    }
                } else {
                    match_weight[r][d] = spilcing_default_match_weight * (1.0f - 0.0f);
                    continue;
                }

            }
        }
    }

    // 使用KM算法进行最优匹配
    splicing_km->init(int(match_weight.size()), match_weight);
    splicing_km->KM_Calc();
    double weight_sum = splicing_km->getdiffSum();
    int *match = splicing_km->getMatch();
    return match;
}

int TrackSplicing::splcing_target_model_predict(SplicingTarget &splicing_target)
{

    return 0;
}

/*
 * @Name: classifyAndPredictTargets
 * @Description: 对拼接输出目标进行分类处理，包括过滤无效目标、对LOST状态目标进行运动模型预测、分别收集LOST和NEW状态的有效目标
 *
 * @Input
 * targets_vec: 拼接输出目标集合（引用）
 * channel_id: 拼接通道ID
 *
 * @Output
 * return: 分类结果结构体，包含LOST和NEW状态的目标及其索引
 *
 * @Edit History
 * Date: 2024-12-19
 * Time: 14:30
 * Author: JiaTao
 * Content: 新增目标分类处理函数
 */
TrackSplicing::TargetClassificationResult TrackSplicing::classifyAndPredictTargets(std::vector<SplicingTarget> &targets_vec, int channel_id)
{
	TargetClassificationResult result;

	// 遍历所有输出目标进行分类处理
	for (unsigned int sot = 0; sot < targets_vec.size(); sot++)
	{
		// 情况1: 目标路径为空（无效目标）
		if (targets_vec[sot].target_output_path.size() == 0)
		{
			targets_vec[sot].target_output.splicing_state = SplicingTrackState::Removed;
			ZERROR("PATH=0 Removed channel_id:%d fusion_id:%d splicing_state:%d id:%d device_id:%d \n",
				channel_id, targets_vec[sot].target_output.fusion_id, targets_vec[sot].target_output.splicing_state,
				targets_vec[sot].target_output.id, targets_vec[sot].target_output.device_id);
			continue;
		}

		// 情况2: 目标坐标为原点（无效坐标）
		if (targets_vec[sot].target_output.target_coor_in_base.x == 0
		 && targets_vec[sot].target_output.target_coor_in_base.y == 0)
		{
			targets_vec[sot].target_output.splicing_state = SplicingTrackState::Removed;
			ZERROR("target_coor_in_base.x == 0 target_coor_in_base.y == 0 Removed channel_id:%d fusion_id:%d splicing_state:%d id:%d device_id:%d \n",
				channel_id, targets_vec[sot].target_output.fusion_id, targets_vec[sot].target_output.splicing_state,
				targets_vec[sot].target_output.id, targets_vec[sot].target_output.device_id);
			continue;
		}

		// 处理LOST状态目标
		if (targets_vec[sot].target_output.splicing_state == SplicingTrackState::Lost)
		{
			// 进行运动模型预测
			int ret_predict = splcing_target_model_predict(targets_vec[sot]);
			if (ret_predict < 0) // 预测失败
			{
				targets_vec[sot].target_output.splicing_state = SplicingTrackState::Removed;
				ZERROR("MODEL_PREDICT Removed channel_id:%d fusion_id:%d splicing_state:%d id:%d device_id:%d \n",
					channel_id, targets_vec[sot].target_output.fusion_id, targets_vec[sot].target_output.splicing_state,
					targets_vec[sot].target_output.id, targets_vec[sot].target_output.device_id);
			}
			else // 预测成功，加入待匹配列表
			{
				result.lose_targets.push_back(targets_vec[sot]);
				result.lose_indices.push_back(sot);
			}
		}

		// 处理NEW状态目标
		if (targets_vec[sot].target_output.splicing_state == SplicingTrackState::New)
		{
			result.new_targets.push_back(targets_vec[sot]);
			result.new_indices.push_back(sot);
		}
	}

	return result;
}



int TrackSplicing::checkout_plate_ipentity(char number_plate_1[MAX_PLATE_TEXT], char number_plate_2[MAX_PLATE_TEXT], float score_top_1, float score_top_2, float &plate_score)
{
#ifdef DEBUG
    ZDEBUG("number_plate_1:%s number_plate_2:%s \n",
           number_plate_1, number_plate_2);
#endif
	if (number_plate_1[0] == '\0' || number_plate_2[0] == '\0')
	{
		return -1;
	}
	else
	{
        if (strcmp(number_plate_1, number_plate_2) == 0)
        {
            plate_score = score_top_1;
            return 0;
        }
        else
        {
            //�Ų麺�ֵ�ǰ���£��ж������ַ��Ƿ���ͬ
            unsigned int plate_char_count = 0;
            unsigned int plate_same_char_count = 0;
            unsigned int hanzi_char_size = 3;
            for (unsigned int i = 0; number_plate_1[i] != '\0'; ++i)
            {
                if(i < hanzi_char_size)
                {
                    continue;
                }
                else
                {
                    if (number_plate_1[i] == number_plate_2[i])
                    {
                        plate_same_char_count++;
                    }
                    plate_char_count++;
                }
            }
#ifdef DEBUG
            ZDEBUG("hanzi number_plate_1:%s number_plate_2:%s plate_char_count:%d plate_same_char_count:%d \n",
                   number_plate_1, number_plate_2,
                   plate_char_count, plate_same_char_count);
#endif
            //���ƺ���һ�����ֲ���ͬʱ��ֱ�ӹ�����������ֵΪ0.8
            if (plate_char_count == plate_same_char_count)
            {
                plate_score = score_top_2;
                return 0;
            }

            //���ƺ���һ�����ֲ���ͬʱ��ֱ�ӹ�����������ֵΪ0.8
            if (plate_char_count - plate_same_char_count == 1)
            {
                plate_score = score_top_2;
                return 0;
            }
            //���ƺ��������ֲ���ͬʱ��ֱ�ӹ�����������ֵΪ0.8
            if (plate_char_count - plate_same_char_count == 2)
            {
                plate_score = score_top_2;
                return 0;
            }
        }
        return -1;
	}
}