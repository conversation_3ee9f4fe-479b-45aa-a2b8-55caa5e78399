#include "Dense"
#include <deque>
#include "tracks_splicing_header.h"
#include <unordered_map>
#include <unordered_set>

#define KALMAN_NOISE_STAT

namespace tecu_r1000_0_algorithm {  // 添加命名空间

// 添加命名空间使用声明
// using SplicingTarget;

// 目标平滑器类
class TargetSmoother {
// 平滑器状态结构体
struct SmoothState {
    double x;      // x位置
    double y;      // y位置
    double vx;     // x速度
    double vy;     // y速度
    long long timestamp_ms;  // 时间戳
};
public:
    TargetSmoother();
    TargetSmoother(int target_id, int window_size = 5, double alpha = 0.5);
    ~TargetSmoother();

    // 拷贝构造函数
    TargetSmoother(const TargetSmoother& other) = default;
    // 拷贝赋值运算符
    TargetSmoother& operator=(const TargetSmoother& other) = default;
    enum class OutputMode {
        LATEST,         // 最新值（无延迟，但平滑效果最差）
        HALF_WINDOW,    // 半窗口延迟（平衡延迟和平滑效果）
        FULL_WINDOW     // 全窗口延迟（最大延迟，但平滑效果最好）
    };

    void setOutputMode(OutputMode mode) {
        _output_mode = mode;
    }

    // 添加参数配置接口
    void configure(const Eigen::Matrix4d& Q, const Eigen::Matrix2d& R) {
        _Q = Q;
        _R = R;
    }

    void update(double x, double y, long long timestamp_ms);
    std::pair<double, double> getSmoothedPosition() const;


private:
    OutputMode _output_mode = OutputMode::LATEST;
    int _target_id;
    int _window_size;
    double _alpha;
    std::deque<SmoothState> _state_buffer;
    
    // 卡尔曼滤波状态
    Eigen::Vector4d _current_state;  // [x, y, vx, vy]
    Eigen::Matrix4d _current_cov;    // 当前协方差
    
    double _dt;  
    Eigen::Matrix4d _F;  
    Eigen::Matrix4d _P;  
    Eigen::Matrix4d _Q;  
    Eigen::Matrix2d _R;  
    
    void initializeMatrices();
    void predict(long long current_time);
    void correct(const Eigen::Vector2d& measurement);
    void smooth_windows();
    
    // 添加RTS平滑相关数据结构
    struct KalmanState {
        Eigen::Vector4d state;
        Eigen::Matrix4d covariance;
        long long timestamp_ms;
    };
    std::deque<KalmanState> _kalman_states;
};

} // namespace tecu_r1000_0_algorithm