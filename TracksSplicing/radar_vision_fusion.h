#pragma once
#include "tracks_splicing_header.h"
#include "config.h"
#include "calib_header.h"
#include "calib_radar_camera.h"

namespace tecu_r1000_0_algorithm
{
    class RadarVisionFusion
    {

    public:
        RadarVisionFusion(int channel_id, Config config);
        ~RadarVisionFusion();

        void vision_target_associated(InputTargets input_targets,
                                      long long timestamp_ms,
                                      std::vector<SplicingInputTarget> &splicing_input_target_vec,
                                      CalibCameraRadar *calib_cr);
    private:
        //雷达目标与卡口相机检测的目标绑定
        std::vector<RadarVisionTarget> rv_target_vec;

        int rvfusion_channel_id;

        //相机和雷达融合时的时间间隔阈值, ms
        long long rc_fusion_time_threshold;
        //相机目标和雷达目标在雷达坐标系匹配的最大关联阈值 m;
        float error_x_threshold;
        //相机目标和雷达目标在雷达坐标系匹配的最大关联阈值 m;
        float error_y_threshold;
        //相机目标和雷达目标关联的时误差在x方向的权重
        float error_x_rate;
    };
}



