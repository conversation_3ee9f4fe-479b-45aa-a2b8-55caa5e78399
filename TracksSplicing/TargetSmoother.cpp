#include "TargetSmoother.h"
#include "tecu1000_algorithm_header.h"
#include "zlogger.h"
#include "utils.h"

ZLOGGER_HEADER_DEFINE("ALG_TARGET_SMOOTHER")

using namespace tecu_r1000_0_algorithm;
using namespace tecu1000_alg;

// 实现默认构造函数
TargetSmoother::TargetSmoother() 
    : _target_id(-1), _window_size(5), _dt(0.2), _alpha(0.5) {
    initializeMatrices();
}

// 实现带参数的构造函数
TargetSmoother::TargetSmoother(int target_id, int window_size, double alpha) 
    : _target_id(target_id), _window_size(window_size), _dt(0.2), _alpha(alpha) {
    initializeMatrices();
}

/*
 * @Name: initializeMatrices
 * @Description: 初始化卡尔曼滤波器所需的状态转移矩阵、协方差矩阵等
 *
 * 状态向量定义: x = [px, py, vx, vy]
 * px,py: 位置坐标
 * vx,vy: 速度分量
 */
void TargetSmoother::initializeMatrices() {
    // 状态转移矩阵F初始化
    // F = [1 0 dt 0 ]
    //     [0 1 0  dt]  
    //     [0 0 1  0 ]
    //     [0 0 0  1 ]
    _F = Eigen::Matrix4d::Identity();
    _F(0,2) = _dt;  // x方向速度对位置的影响
    _F(1,3) = _dt;  // y方向速度对位置的影响
    
    // 初始状态协方差矩阵P
    // 较大的初始协方差表示对初始状态估计的不确定性
    _P = Eigen::Matrix4d::Identity() * 10.0;
    
    // 过程噪声协方差矩阵Q
    // 位置噪声较小,速度噪声较大
    _Q = Eigen::Matrix4d::Identity() * 0.05;
    _Q.block<2,2>(0,0) *= 0.05;  // 位置分量噪声
    _Q.block<2,2>(2,2) *= 0.5;  // 速度分量噪声
    
    // 测量噪声协方差矩阵R
    // 对测量值的信任程度
    _R = Eigen::Matrix2d::Identity() * 2.0;
}

void TargetSmoother::update(double x, double y, long long timestamp_ms) {
    try {
        // 检查时间戳有效性
        if (timestamp_ms <= 0) {
            ZERROR("Invalid timestamp: %lld", timestamp_ms);
            return;
        }

        // 计算时间间隔
        if (_state_buffer.empty()) {
            _dt = 0.2;  // 默认30Hz
            // 初始化状态
            _current_state = Eigen::Vector4d(x, y, 0, 0);
            _current_cov = _P;
        } else {
            _dt = (timestamp_ms - _state_buffer.back().timestamp_ms) / 1000.0;  // 转换为秒
            if(_dt < 1e-6) {
                _dt = 1;
            }
        }
        
        // 更新状态转移矩阵
        _F(0,2) = _dt;
        _F(1,3) = _dt;
        
        // 预测步骤
        predict(timestamp_ms);
        
        // 更新步骤
        Eigen::Vector2d measurement(x, y);
        correct(measurement);
        
        // 保存状态前检查内存
        if (_state_buffer.size() >= _window_size * 2) {
            // ZERROR("State buffer overflow, clearing old states");
            while (_state_buffer.size() >= _window_size) {
                _state_buffer.pop_front();
                _kalman_states.pop_front();
            }
        }
        
        // 保存状态
        SmoothState current_state = {
            _current_state[0], _current_state[1],
            _current_state[2], _current_state[3],
            timestamp_ms
        };
        _state_buffer.push_back(current_state);
        
        // 保存卡尔曼状态
        KalmanState kalman_state = {
            _current_state,
            _current_cov,
            timestamp_ms
        };
        _kalman_states.push_back(kalman_state);
        
        // 执行RTS平滑
        if (_state_buffer.size() >= 5) {
            smooth_windows();
        }
        
    } catch (const std::exception& e) {
        ZERROR("Exception in TargetSmoother::update: %s", e.what());
    } catch (...) {
        ZERROR("Unknown exception in TargetSmoother::update");
    }
}

void TargetSmoother::smooth_windows() {
    try {
        // 检查状态缓冲区大小
        if (_state_buffer.size() < 2) return;
        
        // 确定实际窗口大小
        int actual_window = std::min(static_cast<int>(_state_buffer.size()), _window_size);
        
        // 计算权重总和用于归一化
        double weight_sum = 0.0;
        std::vector<double> weights(actual_window);
        for(int i = 0; i < actual_window; ++i) {
            // 使用指数权重
            weights[i] = std::exp(_alpha * i);
            weight_sum += weights[i];
        }
        
        // 归一化权重
        for(auto& w : weights) {
            w /= weight_sum;
        }
        
        // 获取最新状态的索引
        int latest_idx = _state_buffer.size() - 1;
        
        // 初始化加权和
        Eigen::Vector4d weighted_state = Eigen::Vector4d::Zero();
        
        // 计算加权平均
        for(int i = 0; i < actual_window; ++i) {
            int idx = latest_idx - (actual_window - 1) + i;
            if(idx >= 0 && idx < _state_buffer.size()) {
                const auto& state = _state_buffer[idx];
                Eigen::Vector4d current_state(state.x, state.y, state.vx, state.vy);
                weighted_state += weights[i] * current_state;
            }
        }
        
        // 更新最新状态
        if(latest_idx >= 0 && latest_idx < _state_buffer.size()) {
            _state_buffer[latest_idx].x = weighted_state[0];
            _state_buffer[latest_idx].y = weighted_state[1];
            _state_buffer[latest_idx].vx = weighted_state[2];
            _state_buffer[latest_idx].vy = weighted_state[3];
            
            // 同步更新卡尔曼状态
            if(!_kalman_states.empty()) {
                _kalman_states.back().state = weighted_state;
            }
        }
        
    } catch (const std::exception& e) {
        ZERROR("Exception in TargetSmoother::smooth_windows: %s", e.what());
    } catch (...) {
        ZERROR("Unknown exception in TargetSmoother::smooth_windows");
    }
}

std::pair<double, double> TargetSmoother::getSmoothedPosition() const {
    if (_state_buffer.empty()) {
        return {0.0, 0.0};
    }
    return {_state_buffer.back().x, _state_buffer.back().y};
}


void TargetSmoother::predict(long long current_time) {
    try {
        // 预测状态
        _current_state = _F * _current_state;
        
        // 预测协方差
        Eigen::Matrix4d F_trans = _F.transpose();
        _current_cov = _F * _current_cov * F_trans + _Q;
        
    } catch (const std::exception& e) {
        ZINFO("Exception in predict: %s", e.what());
    } catch (...) {
        ZINFO("Unknown exception in predict");
    }
}

void TargetSmoother::correct(const Eigen::Vector2d& measurement) {
    try {
        // 构建观测矩阵 H
        // H 将状态向量 [x, y, vx, vy] 映射到观测空间 [x, y]
        Eigen::MatrixXd H(2, 4);
        H << 1, 0, 0, 0,
             0, 1, 0, 0;
        
        // 计算卡尔曼增益
        Eigen::MatrixXd H_trans = H.transpose();
        Eigen::MatrixXd S = H * _current_cov * H_trans + _R;
        Eigen::MatrixXd K = _current_cov * H_trans * S.inverse();
        
        // 计算观测残差
        Eigen::Vector2d innovation = measurement - H * _current_state;
        
        // 更新状态
        _current_state = _current_state + K * innovation;
        
        // 更新协方差
        Eigen::Matrix4d I = Eigen::Matrix4d::Identity();
        _current_cov = (I - K * H) * _current_cov;
        
    } catch (const std::exception& e) {
        ZINFO("Exception in correct: %s", e.what());
    } catch (...) {
        ZINFO("Unknown exception in correct");
    }
}

TargetSmoother::~TargetSmoother() {
    _state_buffer.clear();
    _kalman_states.clear();
}
