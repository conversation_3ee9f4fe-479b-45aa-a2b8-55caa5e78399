//
// Created by Administrator on 2024/7/16.
//
#include "radar_vision_fusion.h"
#include "zlogger.h"

ZLOGGER_HEADER_DEFINE("ALG_RADRA_VISION_FUSION")

using namespace tecu_r1000_0_algorithm;



RadarVisionFusion::RadarVisionFusion(int channel_id,  Config config)
{
    rvfusion_channel_id = channel_id;

    rc_fusion_time_threshold = 500;        //ms

    error_x_threshold  = 1.5f;             //m
    error_y_threshold  = 3.0f;             //m

    error_x_rate  = 0.5f;
    ZINFO("Create RadarVisionFusion, channel_id:%d rc_fusion_time_threshold:%lld error_x_threshold:%.2f error_y_threshold:%.2f error_x_rate:%.2f \n",
          rvfusion_channel_id, rc_fusion_time_threshold, error_x_threshold, error_y_threshold, error_x_rate);
}


RadarVisionFusion::~RadarVisionFusion()
{

    std::vector<RadarVisionTarget>().swap(rv_target_vec);
    ZINFO("RadarVisionFusion release! rv_target_vec size:%d \n",rv_target_vec.size());

}





/*
 * @Name:vision_target_associated
 * @Description:视觉目标被委派到雷视目标上，委派的信息为车牌号码，其委派的依据是车辆目标与雷达目标在雷达坐标系距离小于设定的阈值，并且在时间上接近
 *
 * @Input
 * input_targets: 卡口相机探测的视觉目标信息
 * timestamp_ms: 目标接收的时间戳
 * splicing_input_target_vec：缓存的用于多设备探测的目标信息
 * calib_cr:该通道内，卡口相机与雷达标定结构
 * @Output
 * (splicing_input_target_vec):结构体中的车牌号码将被赋值
 *
 * @Edit History
 * Date: 2024/7/16
 * Time: 15:04
 * Author: WangXing
 * Content: Create
*/
void RadarVisionFusion::vision_target_associated(InputTargets input_targets,
                                                 long long timestamp_ms,
                                                 std::vector<SplicingInputTarget> &splicing_input_target_vec,
                                                 CalibCameraRadar *calib_cr
                                                 )
{
    //将卡口相机与雷达目标id和设备id绑定在一起
    if(input_targets.device_type != DeviceType::Bayonet_Camera_Device)
    {
        //当设备类型不为卡口相机时, 则不更新
    }
    else if(calib_cr == nullptr)
    {
        //当该通道不存在卡口相机时，则不进行数据的绑定
        ZERROR("vision_target_associated channel_id:%d calib_cr is nullptr! \n",
               rvfusion_channel_id);
    }
    else
    {
        //则开始更新
        //判断是否存在关联设备
        unsigned int camera_device_id = input_targets.device_id;
        bool exist_radar_device = false;
        unsigned int radar_device_index = 0;
        for(size_t cv = 0; cv < calib_cr->rv_calib_vec.size(); cv++)
        {
            if(camera_device_id == calib_cr->rv_calib_vec[cv].rc_calib_infos.bc_device.device_id)
            {
                exist_radar_device = true;
                radar_device_index = cv;
                break;
            }
        }
        if(exist_radar_device == false)
        {
            //不更新
            ZERROR("vision_target_associated channel_id:%d camera_device_id:%d is not calib, device type is Bayonet_Camera_Device! \n",
                   rvfusion_channel_id, camera_device_id);
        }
        else
        {
            ZERROR("vision_target_associated channel_id:%d camera_device_id:%d is calib, radar device id:%d, image_width:%d image_height:%d \n",
                   rvfusion_channel_id, camera_device_id,calib_cr->rv_calib_vec[radar_device_index].rc_calib_infos.rc_device.device_id,
                   calib_cr->rv_calib_vec[radar_device_index].rc_calib_infos.bc_device.image_width, calib_cr->rv_calib_vec[radar_device_index].rc_calib_infos.bc_device.image_height);
            for(unsigned int it = 0; it < input_targets.target_cnt; it++)
            {
                //卡口相机发送的目标不存在车牌号码,
                if(input_targets.targets.bayonet_vision_target->exist_plate == false)
                {
                    continue;
                }
                //卡口相机发送的目标不存在目标框
                if (input_targets.targets.bayonet_vision_target->exist_box == false)
                {
                    continue;
                }
                //坐标转换
                //单位:pixel(未归一化)
                CalibPoint vision_point ={
                        input_targets.targets.bayonet_vision_target->target_box.x * calib_cr->rv_calib_vec[radar_device_index].rc_calib_infos.bc_device.image_width +
                        (input_targets.targets.bayonet_vision_target->target_box.width * calib_cr->rv_calib_vec[radar_device_index].rc_calib_infos.bc_device.image_width) / 2.0f,
                        input_targets.targets.bayonet_vision_target->target_box.y * calib_cr->rv_calib_vec[radar_device_index].rc_calib_infos.bc_device.image_height +
                        input_targets.targets.bayonet_vision_target->target_box.y * calib_cr->rv_calib_vec[radar_device_index].rc_calib_infos.bc_device.image_height
                };
                //单位:mm
                CalibPoint vision_radar_point = {
                        0.0f,
                        0.0f
                };
                RcMatchPoints vr_points = {
                        vision_point,
                        vision_radar_point
                };
                int ret_coor = calib_cr->rccoor_convert(&vr_points, calib_cr->rv_calib_vec[radar_device_index], false);

                vr_points.radar_point.x = vr_points.radar_point.x / 1000.0f;
                vr_points.radar_point.y = vr_points.radar_point.y / 1000.0f; //mm -> m
                ZDEBUG("vision_target_associated channel_id:%d vision target plate_n:%s bx:%.2f by:%.2f bw:%.2f bh:%.2f ret_coor:%d cx:%.2f cy:%.2f c_rx:%.2f c_ry:%.2f \n",
                       rvfusion_channel_id, input_targets.targets.bayonet_vision_target->plate_text,
                       input_targets.targets.bayonet_vision_target->target_box.x,     input_targets.targets.bayonet_vision_target->target_box.y,
                       input_targets.targets.bayonet_vision_target->target_box.width, input_targets.targets.bayonet_vision_target->target_box.height,
                       ret_coor, vision_point.x, vision_point.y, vr_points.radar_point.x, vr_points.radar_point.y);

                float max_error_norm = 0;
                unsigned int best_radar_index = 0;
                for(size_t siv = 0; siv < splicing_input_target_vec.size(); siv++)
                {
                    //检查设备id是否相同,不相同 continue;
                    if(splicing_input_target_vec[siv].device_id != calib_cr->rv_calib_vec[radar_device_index].rc_calib_infos.rc_device.device_id)
                    {
                        continue;
                    }
                    //检查时间间隔是否大于设定的阈值,过大则删除
                    if(timestamp_ms - splicing_input_target_vec[siv].target_input.time_ms > rc_fusion_time_threshold)
                    {
                        continue;
                    }
                    //判断该目标是否在关联绑定区间
                    float radar_x_coor = splicing_input_target_vec[siv].target_input.target.x;
                    float radar_y_coor = splicing_input_target_vec[siv].target_input.target.y;
                    float error_x = abs(radar_x_coor - vr_points.radar_point.x);
                    float error_y = abs(radar_y_coor - vr_points.radar_point.y);
                    if(error_x < error_x_threshold
                    && error_y < error_y_threshold)
                    {
                        float error_norm = error_x_rate * (error_x_threshold - error_x) / error_x_threshold *
                                           (1.0f - error_x_rate) * (error_y_threshold - error_y) / error_y_threshold;
                        if(error_norm > max_error_norm)
                        {
                            max_error_norm = error_norm;
                            best_radar_index = siv;
                        }
                    }
                }
                if(max_error_norm > 0)
                {
                    ZDEBUG("vision_target_associated SUCCESS channel_id:%d cdv_id:%d plate_n:%s c_rx:%.2f c_ry:%.2f rdv_id:%d id:%d rx:%.2f ry:%.2f max_error_norm:%.2f \n",
                           rvfusion_channel_id, input_targets.device_id, input_targets.targets.bayonet_vision_target->plate_text,
                           vr_points.radar_point.x, vr_points.radar_point.y,
                           splicing_input_target_vec[best_radar_index].device_id, splicing_input_target_vec[best_radar_index].target_input.target.id,
                           splicing_input_target_vec[best_radar_index].target_input.target.x, splicing_input_target_vec[best_radar_index].target_input.target.y,
                           max_error_norm);
                    RadarVisionTarget rv_target_associated = {
                            splicing_input_target_vec[best_radar_index].device_id,
                            splicing_input_target_vec[best_radar_index].id,
                            input_targets.targets.bayonet_vision_target[it]
                    };
                    if(rv_target_vec.size() == 0)
                    {
                        rv_target_vec.push_back(rv_target_associated);
                    }
                    else
                    {
                        bool exist_rvtarget = false;
                        unsigned int exist_rvtarget_index = 0;
                        for(unsigned int rvv = 0; rvv < rv_target_vec.size(); rvv++)
                        {
                            if(rv_target_associated.id == rv_target_vec[rvv].id &&
                               rv_target_associated.device_id == rv_target_vec[rvv].device_id)
                            {
                                exist_rvtarget = true;
                                exist_rvtarget_index = rvv;
                                break;
                            }
                        }
                        if(exist_rvtarget == false)
                        {
                            rv_target_vec.push_back(rv_target_associated);
                        }
                        else
                        {
                            //更新
                            rv_target_vec[exist_rvtarget_index] = rv_target_associated;
                        }
                    }
                }
            }
        }
    }

    //根据缓存的目标是否存在对目标删除和车牌信息的委派
    std::vector<RadarVisionTarget>::iterator rv_target_vec_iter;
    for(rv_target_vec_iter = rv_target_vec.begin(); rv_target_vec_iter != rv_target_vec.end();)
    {
        bool exist_rvtarget = false;
        for(unsigned int sit = 0; sit < splicing_input_target_vec.size(); sit++)
        {
            if(rv_target_vec_iter->device_id == splicing_input_target_vec[sit].device_id
            && rv_target_vec_iter->id == splicing_input_target_vec[sit].target_input.target.id)
            {
                exist_rvtarget = true;
                //车牌号码的委派
                memcpy(splicing_input_target_vec[sit].target_input.target.number_plate,
                       rv_target_vec_iter->bvision_target.plate_text,
                       sizeof(rv_target_vec_iter->bvision_target.plate_text));
                break;
            }
        }
        if(exist_rvtarget == false)
        {
            ZDEBUG("vision_target_associated rv_target_vec DELETE deveie_id:%d id:%d \n",
                   rv_target_vec_iter->device_id,rv_target_vec_iter->id);
            rv_target_vec_iter = rv_target_vec.erase(rv_target_vec_iter);
        }
        else
        {
            rv_target_vec_iter++;
        }
    }

    //委派信息的打印
    ZDEBUG("vision_target_associated channel_id:%d calib_cr.size:%d ! \n",
           rvfusion_channel_id, calib_cr->rv_calib_vec.size());
    for(size_t rtv = 0; rtv < rv_target_vec.size(); rtv++)
    {
        ZDEBUG("vision_target_associated channel_id:%d device_id:%d id:%d plate_n:%s \n",
               rvfusion_channel_id,rv_target_vec[rtv].device_id,rv_target_vec[rtv].id,
               rv_target_vec[rtv].bvision_target.plate_text);
    }
}


