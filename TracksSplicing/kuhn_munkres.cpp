#include "kuhn_munkres.h"
#include "tecu1000_algorithm_header.h"
#include <algorithm>


using namespace tecu1000_alg;
KuhnMunkres::KuhnMunkres()
{
    // 参数初始化
    ex_a = nullptr;
    ex_b = nullptr;
    slack = nullptr;
    match = nullptr;
    vis_a = vis_b = nullptr;
    size = 0;
    diffSum = 0;
    ex_a = new double[MAX_OBJECT_NUM];
    ex_b = new double[MAX_OBJECT_NUM];
    vis_a = new bool[MAX_OBJECT_NUM];
    vis_b = new bool[MAX_OBJECT_NUM];
    slack = new double[MAX_OBJECT_NUM];
    match = new int[MAX_OBJECT_NUM];
}
 
KuhnMunkres::~KuhnMunkres()
{
    /* 释放之前分配了的内存 */
    if(ex_a)
    {
        delete []ex_a;
        ex_a = nullptr;
    }
    if(ex_b)
    {
        delete []ex_b;
        ex_b = nullptr;
    }
    if(vis_a)
    {
        delete []vis_a;
        vis_a = nullptr;
    }
    if(vis_b)
    {
        delete []vis_b;
        vis_b = nullptr;
    }
    if(slack)
    {
        delete []slack;
        slack = nullptr;
    }
 
    if(match)
    {
        delete []match;
        match = nullptr;
    }
}
 
void KuhnMunkres::init(int size, std::vector<std::vector<double>> diff)
{
    /* 获取差异度矩阵，根据矩阵的边长为其他变量分配空间 */
    this->diff = diff;
    this->size = size;
}
 
bool KuhnMunkres::dfs(int a)
{
    vis_a[a] = true;
    for(int b = 0; b < size; b++)
    {
        if(vis_b[b])
            continue;
        double c = diff[a][b];
        double gap = ex_a[a] + ex_b[b] - c;
        if(fabs(gap) < 1e-6)
        {
            vis_b[b] = true;
            if(match[b] == -1 || dfs(match[b]))
            {
                match[b] = a;
                return true;
            }
        }
        else
        {
            slack[b] = std::max(slack[b], gap);
        }
    }
    return false;
}
 
void KuhnMunkres::KM_Calc()
{
    for(int i = 0; i < size; i++)
    {
        ex_b[i] = 0;
        match[i] = -1;
        ex_a[i] = (diff)[i][0];
        for(int j = 1; j < size; j++)
        {
            ex_a[i] = std::min(ex_a[i], (diff)[i][j]);
        }
    }
 
    for(int i = 0; i < size; i++)
    {
        for(int m = 0; m < size; m++)
            slack[m] = -1000000.0;
        while(1)
        {
            for(int m = 0; m <size; m++)
            {
                vis_a[m] = false;
                vis_b[m] = false;
            }
            if(dfs(i))
                break;
         
            double d = -1000000.0;
            for(int j = 0; j < size; j++)
            {
                if(!vis_b[j])
                    d = std::max(d, slack[j]);
            }
            for(int j = 0; j < size; j++)
            {
                if(vis_a[j])
                    ex_a[j] -= d;
                if(vis_b[j])
                    ex_b[j] += d;
                else
                    slack[j] -= d;
            }
        }
    }
    diffSum = 0;
    for(int i = 0; i < size; i++)
    {
        diffSum += (diff)[match[i]][i];
    }
}
 
int* KuhnMunkres::getMatch()
{
    return match;
}
 
double KuhnMunkres::getdiffSum()
{
    return diffSum;
}