//
// Created by Administrator on 2024/7/16.
//

#include "calib_radar_camera.h"
#include "zlogger.h"
using namespace tecu_r1000_0_algorithm;

ZLOGGER_HEADER_DEFINE("ALG_CALIB_CAMERA_RADAR")


CalibCameraRadar::CalibCameraRadar(int channel_id, Config rc_calib_config)
{
    calib_cr_channel_id   = channel_id;

    focal_error           = rc_calib_config.focal_error;
    error_gap             = rc_calib_config.error_gap;
    calib_radar_threshold = rc_calib_config.calib_radar_thre;
    error_xrate           = rc_calib_config.error_xrate;

    ZINFO("CalibCameraRadar channel_id:%d create! focal_error:%.2f error_gap:%.2f calib_radar_threshold:%.2f error_xrate:%.2f \n",
          channel_id,focal_error,error_gap,calib_radar_threshold,error_xrate);

}
CalibCameraRadar::~CalibCameraRadar()
{
    std::vector<RvCalibMat>().swap(rv_calib_vec);
    ZINFO("CalibCameraRadar channel_id:%d rv_calib_vec release! rv_calib_vec size:%d \n",
          calib_cr_channel_id, rv_calib_vec.size());
}

/*
 * @Name:rc_devices_calib
 * @Description:相机与雷达(雷视)的标定
 *
 * @Input
 * rv_calib_mat: 相机与雷达标定的数据结构体
 *
 * @Output
 * 返回值 标定成功:0 标定失败:-1
 *
 * @Edit History
 * Date: 2024/7/15
 * Time: 16:49
 * Author: WangXing
 * Content:Create
*/
int CalibCameraRadar::rc_devices_calib(RvCalibMat &rv_calib_mat)
{
    std::vector<cv::Point3d> radar_calib_points;
    std::vector<cv::Point2d> image_calib_points;
    unsigned int calibdata_num = rv_calib_mat.rc_calib_infos.calib_match_cnt;
    for(unsigned int t = 0; t < calibdata_num; t++)
    {
        cv::Point3d r_calib_3d;
        cv::Point2d c_calib_2d;
        r_calib_3d.x = rv_calib_mat.rc_calib_infos.rc_calib_points[t].radar_point.x * 1000.0f;
        r_calib_3d.y = rv_calib_mat.rc_calib_infos.rc_calib_points[t].radar_point.y * 1000.0f;
        r_calib_3d.z = rv_calib_mat.rc_calib_infos.bc_device.device_height;
        c_calib_2d.x = rv_calib_mat.rc_calib_infos.rc_calib_points[t].image_point.x * (float)rv_calib_mat.rc_calib_infos.bc_device.image_width;
        c_calib_2d.y = rv_calib_mat.rc_calib_infos.rc_calib_points[t].image_point.y * (float)rv_calib_mat.rc_calib_infos.bc_device.image_height;
        radar_calib_points.push_back(r_calib_3d);
        image_calib_points.push_back(c_calib_2d);
    }

    cv::Mat camera_mat      = cv::Mat(3, 3, CV_64FC1, cv::Scalar::all(0));
    cv::Mat rotation_vec    = cv::Mat(3, 1, CV_64FC1, cv::Scalar::all(0));
    cv::Mat translation_mat = cv::Mat(3, 1, CV_64FC1, cv::Scalar::all(0));
    cv::Mat rotation_mat;
    cv::Mat camera_mat_invert;
    cv::Mat rotation_mat_invert;

    float best_focal = rv_calib_mat.rc_calib_infos.bc_device.focus;
    int   focal_num = float(rv_calib_mat.rc_calib_infos.bc_device.focus * focal_error * 2 * 10) / float(error_gap * 10);
    std::vector<float> focal_list;
    std::string focal_list_str = "";
    for (int f = 0; f < focal_num; f++)
    {
        float temp_focal = rv_calib_mat.rc_calib_infos.bc_device.focus * (1 - focal_error) + error_gap * f;
        focal_list_str += " " + std::to_string(temp_focal);
        focal_list.push_back(temp_focal);
    }
    ZINFO("focal_list size:%d focal_list_str:%s \n",focal_list.size(),focal_list_str.c_str());
    float min_calib_loss = calib_radar_threshold * 2.0f;
    float max_calib_loss = 300 * 1000;
    int best_focal_idx_success   = -1;
    int best_focal_idx_fail      = -1;
    std::vector<std::vector<float>> calib_errors_;
    for(size_t f = 0; f < focal_list.size(); f++)
    {
        double fx = focal_list[f] / (rv_calib_mat.rc_calib_infos.bc_device.cmos_width   / rv_calib_mat.rc_calib_infos.bc_device.image_width);
        double fy = focal_list[f] / (rv_calib_mat.rc_calib_infos.bc_device.cmos_height  / rv_calib_mat.rc_calib_infos.bc_device.image_height);
        camera_mat.ptr<double>(0)[0] = fx;
        camera_mat.ptr<double>(0)[2] = rv_calib_mat.rc_calib_infos.bc_device.image_width / 2;
        camera_mat.ptr<double>(1)[1] = fy;
        camera_mat.ptr<double>(1)[2] = rv_calib_mat.rc_calib_infos.bc_device.image_height / 2;
        camera_mat.ptr<double>(2)[2] = 1.0f;
        int flag = cv::SOLVEPNP_ITERATIVE;
        bool success = solvePnP(radar_calib_points, image_calib_points, camera_mat, cv::noArray(), rotation_vec, translation_mat, false, flag);
        Rodrigues(rotation_vec, rotation_mat);                                          //将旋转向量转换为罗德里格旋转矩阵
        int flag_camera = cv::invert(camera_mat, camera_mat_invert);
        int flag_rota   = cv::invert(rotation_mat, rotation_mat_invert);
        std::vector<float> calib_error;
        if(abs(flag_camera - 1) > 1e-4 || abs(flag_rota - 1) > 1e-4)
        {
            for(size_t i = 0; i < image_calib_points.size(); i++)
            {
                calib_error.push_back(calib_radar_threshold);
            }
            continue;
        }
        float x_error = 0;
        float y_error = 0;
        for(size_t i = 0; i < image_calib_points.size(); i++)
        {
            cv::Mat pp3d   = cv::Mat(3, 1, CV_64FC1, cv::Scalar::all(0));
            cv::Point2d pp(image_calib_points[i].x, image_calib_points[i].y);
            cv::Mat rotation_tran_mat;
            cv::Mat rotation_camera_mat;
            rotation_tran_mat     = rotation_mat_invert * translation_mat;
            rotation_camera_mat   = rotation_mat_invert * camera_mat_invert;
            double zc_denominator = radar_calib_points[i].z  + rotation_tran_mat.ptr<double>(2)[0];
            double zc_molecular   = rotation_camera_mat.ptr<double>(2)[0]*pp.x + rotation_camera_mat.ptr<double>(2)[1]*pp.y + rotation_camera_mat.ptr<double>(2)[2];
            double zc             = zc_denominator / zc_molecular;
            pp3d.ptr<double>(0)[0] = pp.x * zc;
            pp3d.ptr<double>(1)[0] = pp.y * zc;
            pp3d.ptr<double>(2)[0] = zc;
            cv::Mat pw             = rotation_camera_mat * pp3d - rotation_mat_invert * translation_mat;

            x_error               +=   abs(radar_calib_points[i].x - pw.ptr<double>(0)[0]);
            y_error               +=   abs(radar_calib_points[i].y - pw.ptr<double>(0)[1]);

            float total_error      = error_xrate * abs(radar_calib_points[i].x - pw.ptr<double>(0)[0]) +
                                     (1.0f - error_xrate) * abs(radar_calib_points[i].y - pw.ptr<double>(0)[1]);

            calib_error.push_back(total_error);
        }
        calib_errors_.push_back(calib_error);

        float xcalibthre = float(x_error / radar_calib_points.size());
        float ycalibthre = float(y_error / radar_calib_points.size());

        float cur_calib_loss = xcalibthre + ycalibthre;
        ZINFO("cur_focal:%.2f cur_calib_loss: %.2f \n",focal_list[f], cur_calib_loss);

        if(cur_calib_loss < calib_radar_threshold * 2)
        {
            if(abs(flag_camera - 1) <  1e-4 && abs(flag_rota - 1)< 1e-4
               && cur_calib_loss < min_calib_loss)
            {
                min_calib_loss = cur_calib_loss;
                best_focal_idx_success = f;
                best_focal     = focal_list[f];

                rv_calib_mat.camera_mat          = camera_mat.clone();
                rv_calib_mat.translation_mat     = translation_mat.clone();
                rv_calib_mat.rotation_mat        = rotation_mat.clone();
                rv_calib_mat.rotation_mat_invert = rotation_mat_invert.clone();
                rv_calib_mat.camera_mat_invert   = camera_mat_invert.clone();
            }
        }
        if(cur_calib_loss < max_calib_loss && cur_calib_loss >= calib_radar_threshold * 2.0f)
        {
            max_calib_loss = cur_calib_loss;
            best_focal_idx_fail = f;
        }
        std::vector<float>().swap(calib_error);
    }
    ZINFO("min_calib_loss:%.2f calib_radar_thre:%.2f \n",min_calib_loss, calib_radar_threshold);
    if(min_calib_loss < calib_radar_threshold * 2.0f)
    {
        ZINFO("calib success! \n");
        std::string calib_error_str = "";
        if(best_focal_idx_success > -1)
        {
            for(size_t i=0; i < calib_errors_[best_focal_idx_success].size(); i++)
            {
                rv_calib_mat.rc_calib_infos.calib_errors[i] = calib_errors_[best_focal_idx_success][i] / 1000;
                calib_error_str += " " + std::to_string(calib_errors_[best_focal_idx_success][i] / 1000);
            }
            ZINFO("calib_error_str %s \n", calib_error_str.c_str());
        }
        ZINFO("min_calib_loss: %f, best_focal: %f \n", min_calib_loss, best_focal);

        //
        //坐标转换
        //单位:pixel(未归一化)
        CalibPoint test_vision_point = {
                (float)image_calib_points[0].x,
                (float)image_calib_points[0].y
        };
        CalibPoint test_radar_point = {
                (float)radar_calib_points[0].x,
                (float)radar_calib_points[0].y
        };
        //单位:mm
        CalibPoint vision_radar_point = {
                0.0f,
                0.0f
        };
        CalibPoint radar_vision_point = {
                0.0f,
                0.0f
        };
        RcMatchPoints vr_points = {
                test_vision_point,
                vision_radar_point
        };
        RcMatchPoints rv_points = {
                radar_vision_point,
                test_radar_point
        };
        int ret_coor = rccoor_convert(&vr_points, rv_calib_mat, false);
        ret_coor = rccoor_convert(&rv_points, rv_calib_mat, true);

        ZDEBUG("calib test rccoor_convert Camera ->  Radar vision_point x:%.2f y:%.2f vision_radar_point x:%.2f y:%.2f radar_point x:%.2f y:%.2f \n",
               test_vision_point.x, test_vision_point.y, vr_points.radar_point.x, vr_points.radar_point.y, test_radar_point.x, test_radar_point.y);

        ZDEBUG("calib test rccoor_convert Radar -> Camera radar_point x:%.2f y:%.2f radar_vision_point x:%.2f y:%.2f vision_point x:%.2f y:%.2f \n",
               test_radar_point.x, test_radar_point.y, rv_points.image_point.x, rv_points.image_point.y, test_vision_point.x, test_vision_point.y);

        return 0;
    }
    else
    {
        ZINFO("calib faild! \n");
        std::string calib_error_str = "";
        if(best_focal_idx_fail > -1)
        {
            for(size_t i=0; i < calib_errors_[best_focal_idx_fail].size(); i++)
            {
                rv_calib_mat.rc_calib_infos.calib_errors[i] = calib_errors_[best_focal_idx_fail][i] / 1000;
                calib_error_str += " " + std::to_string(calib_errors_[best_focal_idx_fail][i] / 1000);
            }
            ZINFO("calib_error_str %s \n", calib_error_str.c_str());
        }
        return -1;
    }
}


/*
* @Name:rccalib_vec_update
* @Description:标定数据容器的更新
*
* @Input
* rv_calib_mat: 标定成功的数据
* channel_id: 通道号
*
* @Output
* 返回值 更新成功:0 更新失败:-1
*
* @Edit History
* Date: 2024/7/15
* Time: 19:05
* Author: WangXing
* Content:Create
*/
int CalibCameraRadar::rccalib_vec_update(RvCalibMat rv_calib_mat)
{
    if(rv_calib_vec.size() == 0)
    {
        rv_calib_vec.push_back(rv_calib_mat);
    }
    else
    {
        bool is_exist = false;
        unsigned int exist_index = 0;
        for(size_t rc = 0; rc < rv_calib_vec.size(); rc++)
        {
            if(rv_calib_vec[rc].rc_calib_infos.rc_device.device_id == rv_calib_mat.rc_calib_infos.rc_device.device_id
               && rv_calib_vec[rc].rc_calib_infos.bc_device.device_id == rv_calib_mat.rc_calib_infos.bc_device.device_id)
            {
                is_exist = true;
                exist_index = rc;
                break;
            }
        }
        if(is_exist == false)
        {
            rv_calib_vec.push_back(rv_calib_mat);
        }
        else
        {
            rv_calib_vec[exist_index] = rv_calib_mat;
        }
    }
    return 0;
}


/*
 * @Name:rvcoor_convert
 * @Description:目标的雷达坐标点和像素坐标点转换
 *
 * @Input
 * coor: 带转换的坐标点 雷达点单位:mm,像素点单位:pixel
 * rv_calib_mats: 坐标转换的矩阵
 * rc_flag：1-雷达坐标点向像素坐标系转换， 0-像素坐标系向雷达坐标点转换
 *
 * @Output
 * 返回 1:成功; 0:失败
 *
 * @Edit History
 * Date: 2024/7/16
 * Time: 17:46
 * Author: WangXing
 * Content:Create
*/
int CalibCameraRadar::rccoor_convert(RcMatchPoints *coor, RvCalibMat rv_calib_mats, bool rc_flag)
{
    cv::Mat pw = cv::Mat(3, 1, CV_64FC1, cv::Scalar::all(0));
    if(rc_flag == true)
    {
        //雷达坐标 -> 像素坐标
        pw.ptr<double>(0)[0] = coor->radar_point.x;
        pw.ptr<double>(0)[1] = coor->radar_point.y;
        pw.ptr<double>(0)[2] = rv_calib_mats.rc_calib_infos.bc_device.device_height;

        cv::Mat pixel_point = rv_calib_mats.camera_mat * (rv_calib_mats.rotation_mat * pw + rv_calib_mats.translation_mat);
        pixel_point         = pixel_point / pixel_point.ptr<double>(0)[2];
        coor->image_point.x = pixel_point.ptr<double>(0)[0];
        coor->image_point.y = pixel_point.ptr<double>(0)[1];
    }
    else
    {
        //像素坐标->雷达坐标
        cv::Mat rotation_tran_mat;
        cv::Mat rotation_camera_mat;
        rotation_tran_mat       = rv_calib_mats.rotation_mat_invert * rv_calib_mats.translation_mat;
        rotation_camera_mat     = rv_calib_mats.rotation_mat_invert * rv_calib_mats.camera_mat_invert;
        double zc_denominator   = rv_calib_mats.rc_calib_infos.bc_device.device_height  + rotation_tran_mat.ptr<double>(2)[0];
        double zc_molecular = rotation_camera_mat.ptr<double>(2)[0]* coor->image_point.x + rotation_camera_mat.ptr<double>(2)[1] * coor->image_point.y + rotation_camera_mat.ptr<double>(2)[2];
        double zc = zc_denominator / zc_molecular;
        pw.ptr<double>(0)[0] = coor->image_point.x * zc;
        pw.ptr<double>(1)[0] = coor->image_point.y * zc;
        pw.ptr<double>(2)[0] = zc;
        pw = rotation_camera_mat * pw - rv_calib_mats.rotation_mat_invert * rv_calib_mats.translation_mat;
        coor->radar_point.x = pw.ptr<double>(0)[0];
        coor->radar_point.y = pw.ptr<double>(0)[1];

    }
    return 0;
}















CalibCameraRadarByHomograph::CalibCameraRadarByHomograph(int channel_id, Config rc_calib_config)
{
    calib_cr_channel_id   = channel_id;

    focal_error           = rc_calib_config.focal_error;
    error_gap             = rc_calib_config.error_gap;
    calib_radar_threshold = rc_calib_config.calib_radar_thre;
    error_xrate           = rc_calib_config.error_xrate;

    ZINFO("CalibCameraRadarByHomograph channel_id:%d create! focal_error:%.2f error_gap:%.2f calib_radar_threshold:%.2f error_xrate:%.2f \n",
          channel_id,focal_error,error_gap,calib_radar_threshold,error_xrate);

}

CalibCameraRadarByHomograph::~CalibCameraRadarByHomograph()
{
    std::vector<RvCalibHomography>().swap(rv_calib_vec);
    ZINFO("CalibCameraRadarByHomograph channel_id:%d rv_calib_vec release! rv_calib_vec size:%d \n",
          calib_cr_channel_id, rv_calib_vec.size());
}





/*
 * @Name:rc_devices_calib
 * @Description:相机与雷达(雷视)的标定
 *
 * Mat cv::findHomography(
    InputArray srcPoints,   // 源图像中的点集，通常是二维点
    InputArray dstPoints,   // 目标图像中的对应点集，通常是二维点
    int method = 0,         // 计算单应性矩阵的方法
    double ransacReprojThreshold = 3, // RANSAC算法的投影误差阈值
    OutputArray mask = noArray(),     // 可选的输出掩码，指示哪些点对是内点
    const int maxIters = 2000,        // RANSAC算法的最大迭代次数
    const double confidence = 0.995   // RANSAC算法的置信度
)
 *
 * @Input
 * rv_calib_mat: 相机与雷达标定的数据结构体
 *
 * @Output
 * 返回值 标定成功:0 标定失败:-1
 *
 * @Edit History
 * Date: 2024/7/15
 * Time: 16:49
 * Author: WangXing
 * Content:Create
*/
int CalibCameraRadarByHomograph::rc_devices_calib(RvCalibHomography &rv_calib_mat)
{
    std::vector<cv::Point2d> radar_calib_points;
    std::vector<cv::Point2d> image_calib_points;
    unsigned int calibdata_num = rv_calib_mat.rc_calib_infos.calib_match_cnt;
    for(unsigned int t = 0; t < calibdata_num; t++)
    {
        cv::Point2d r_calib_2d;
        cv::Point2d c_calib_2d;
        r_calib_2d.x = rv_calib_mat.rc_calib_infos.rc_calib_points[t].radar_point.x * 1000.0f;
        r_calib_2d.y = rv_calib_mat.rc_calib_infos.rc_calib_points[t].radar_point.y * 1000.0f;
        c_calib_2d.x = rv_calib_mat.rc_calib_infos.rc_calib_points[t].image_point.x * (float)rv_calib_mat.rc_calib_infos.bc_device.image_width;
        c_calib_2d.y = rv_calib_mat.rc_calib_infos.rc_calib_points[t].image_point.y * (float)rv_calib_mat.rc_calib_infos.bc_device.image_height;
        radar_calib_points.push_back(r_calib_2d);
        image_calib_points.push_back(c_calib_2d);
    }


    std::vector<int> method_list =   {0,  cv::RANSAC,  cv::LMEDS}; // 注意：cv::RHO不是findHomography的有效方法
    float min_calib_loss = calib_radar_threshold * 2.0f;
    float max_calib_loss = 300 * 1000;
    int best_focal_idx_success   = -1;
    int best_focal_idx_fail      = -1;
    std::vector<std::vector<float>> calib_errors_;
    for (int i = 0; i < method_list.size(); ++i)
    {
        int caclMethod     = method_list[i];
        cv::Mat homography = cv::findHomography(image_calib_points,
                                                radar_calib_points,
                                                caclMethod);
        cv::Mat homography_invert;
        int flag = cv::invert(homography, homography_invert);
        std::vector<float> calib_error;
        if (flag != 1)
        {
            for(size_t i = 0; i < image_calib_points.size(); i++)
            {
                calib_error.push_back(calib_radar_threshold);
            }
            calib_errors_.push_back(calib_error);
            best_focal_idx_fail = i;
            continue;
        }


        float x_error = 0;
        float y_error = 0;
        for(size_t imp = 0; imp < image_calib_points.size(); imp++)
        {
            cv::Mat image_point = cv::Mat(3, 1, CV_64FC1, cv::Scalar::all(0));
            image_point.ptr<double>(0)[0] = image_calib_points[imp].x;
            image_point.ptr<double>(1)[0] = image_calib_points[imp].y;
            image_point.ptr<double>(2)[0] = 1;
            cv::Mat image_map_point = homography * image_point;

            x_error           +=   abs(radar_calib_points[imp].x - image_map_point.ptr<double>(0)[0] / image_map_point.ptr<double>(0)[2]);
            y_error           +=   abs(radar_calib_points[imp].y - image_map_point.ptr<double>(0)[1] / image_map_point.ptr<double>(0)[2]);

            float total_error = error_xrate * abs(x_error) + (1.0f - error_xrate) * abs(y_error);

            calib_error.push_back(total_error);
        }
        calib_errors_.push_back(calib_error);

        float xcalibthre = float(x_error / radar_calib_points.size());
        float ycalibthre = float(y_error / radar_calib_points.size());
        float cur_calib_loss = xcalibthre + ycalibthre;
        ZINFO("cur_method:%d cur_calib_loss:%.2f m \n",caclMethod, cur_calib_loss / 1000.0f);

        if(cur_calib_loss < calib_radar_threshold * 2)
        {
            if(cur_calib_loss < min_calib_loss)
            {
                min_calib_loss         = cur_calib_loss;
                best_focal_idx_success = i;
                rv_calib_mat.camera_radar_homography = homography.clone();
                rv_calib_mat.camera_radar_homography = homography_invert.clone();
            }
        }
        if(cur_calib_loss < max_calib_loss && cur_calib_loss >= calib_radar_threshold * 2.0f)
        {
            max_calib_loss      = cur_calib_loss;
            best_focal_idx_fail = i;
        }
    }
    ZINFO("min_calib_loss:%.2f calib_radar_thre:%.2f \n",min_calib_loss, calib_radar_threshold);
    if(min_calib_loss < calib_radar_threshold * 2.0f)
    {
        std::string calib_error_str = "";
        if(best_focal_idx_success > -1)
        {
            for(size_t i=0; i < calib_errors_[best_focal_idx_success].size(); i++)
            {
                rv_calib_mat.rc_calib_infos.calib_errors[i] = calib_errors_[best_focal_idx_success][i] / 1000;
                calib_error_str += " " + std::to_string(calib_errors_[best_focal_idx_success][i] / 1000);
            }
            ZINFO("calib_error_str %s best_focal_idx_success:%d \n", calib_error_str.c_str(), best_focal_idx_success);
        }
        ZINFO("calib success! min_calib_loss: %f best_focal_idx_success:%d\n", min_calib_loss / 1000.0f, best_focal_idx_success);
        return 0;
    }
    else
    {
        std::string calib_error_str = "";
        if(best_focal_idx_fail > -1)
        {
            for(size_t i = 0; i < calib_errors_[best_focal_idx_fail].size(); i++)
            {
                rv_calib_mat.rc_calib_infos.calib_errors[i] = calib_errors_[best_focal_idx_fail][i] / 1000;
                calib_error_str += " " + std::to_string(calib_errors_[best_focal_idx_fail][i] / 1000);
            }
            ZINFO("calib_error_str: %s best_focal_idx_fail:%d\n", calib_error_str.c_str(), best_focal_idx_fail);
        }
        ZINFO("calib faild! min_calib_loss: %f best_focal_idx_success:%d\n", min_calib_loss / 1000.0f, best_focal_idx_success);
        return -1;
    }
}





int CalibCameraRadarByHomograph::rccalib_vec_update(RvCalibHomography rv_calib_mat)
{
    if(rv_calib_vec.size() == 0)
    {
        rv_calib_vec.push_back(rv_calib_mat);
    }
    else
    {
        bool is_exist = false;
        unsigned int exist_index = 0;
        for(size_t rc = 0; rc < rv_calib_vec.size(); rc++)
        {
            if(rv_calib_vec[rc].rc_calib_infos.rc_device.device_id == rv_calib_mat.rc_calib_infos.rc_device.device_id
               && rv_calib_vec[rc].rc_calib_infos.bc_device.device_id == rv_calib_mat.rc_calib_infos.bc_device.device_id)
            {
                is_exist = true;
                exist_index = rc;
                break;
            }
        }
        if(is_exist == false)
        {
            rv_calib_vec.push_back(rv_calib_mat);
        }
        else
        {
            rv_calib_vec[exist_index] = rv_calib_mat;
        }
    }
}