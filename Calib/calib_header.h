#pragma once
#include <iostream>
#include <iomanip>
#include <vector>
#include <fstream>
#include <map>
#include "Dense"
#include <opencv2/opencv.hpp>            // C++
#include <opencv2/highgui/highgui_c.h>   // C
#include <opencv2/imgproc/imgproc_c.h>   // C


#include "tecu1000_algorithm_header.h"
using namespace tecu1000_alg;

namespace tecu_r1000_0_algorithm
{
	/********************************************************
	 * Description          			 �豸��Ϣ����
	 * device_cnt                        �豸��������
	 * device_list                       �豸��Ϣ�б�
	********************************************************/
	typedef struct
	{
		unsigned int	 device_cnt;                             //�豸������
		DeivceInfo       device_list[DEVICE_MAX_NUM];			 //�豸����б�
	}DeviceList;



	/********************************************************
	 * Description          			 �豸��ı궨��Ϣ
	 * device_match_cnt                  �豸��ı궨����
	 * device_matchs                     �豸�����Ϣ
	********************************************************/
	typedef struct
	{
		unsigned int     device_match_cnt;                       //�豸��궨ƥ������
		CalibMatch       device_matchs[DEVICE_MAX_NUM * 2];        //�豸��궨ƥ�����Ϣ
	}CalibMatchList;

	/********************************************************
	 * Description          			 �豸�����궨����Ľṹ��
	 * calib_mat_1_2                     �豸���device_id2��device_id1��ͶӰ����
	 * device_id1                        device_id1�豸���
	 * device_id2                        device_id2�豸���
	********************************************************/
	typedef struct
	{
		Eigen::Matrix3d calib_mat_2_1;
		unsigned int    device_id2;
		unsigned int    device_id1;
	}DeviceCalibMat;



	/********************************************************
	 * Description          			 ��׼�豸�������豸�ı궨����Ľṹ��
	 * calib_mat_ob                      �豸���device_id���׼�豸��ӳ�����
	 * device_id						 device_id���豸���
	********************************************************/
	typedef struct
	{
		Eigen::Matrix3d calib_mat_ob;
		unsigned int    device_id;
	}DevicesCalibMat;


	/********************************************************
	 * Description          			 �豸��궨����Ϣ
	 * base_device_id                    ��׼�豸���
	 * base_device_info                  ��׼�豸��Ϣ
	 * device_map_mats                   �豸���׼�豸ת����ӳ������ֵ�
	********************************************************/
	typedef struct
	{
		unsigned int                               base_device_id;
		DeivceInfo                                 base_device_info;
		std::map <unsigned int, DevicesCalibMat>   device_map_mats;
	}DevicesMap;

    /********************************************************
	 * Description          			 �״��뿨������ı궨�ṹ��
	 * rc_calib_infos                    �״��뿨������ı궨
	 * camera_mat                        ������ڲξ���
	 * rotation_mat                      ������״����ת����
     * translation_mat                   ������״��ƽ�ƾ���
     * rotation_mat_invert               ��ת�������
     * camera_mat_invert                 ����ڲξ������
	********************************************************/
    typedef struct
    {
        RcCalibMatch                                rc_calib_infos;
        cv::Mat                                     camera_mat;
        cv::Mat                                     rotation_mat;
        cv::Mat                                     translation_mat;
        cv::Mat                                     rotation_mat_invert;
        cv::Mat                                     camera_mat_invert;
    }RvCalibMat;


    /********************************************************
	 * Description          			 �״��뿨������ı궨�ṹ����ڵ�Ӧ�Ծ���
	 * rc_calib_infos                    �״��뿨������ı궨
	 * radar_camera_homography           �״�������ı궨����
	 * camera_radar_homography           ������״�ı궨����
	********************************************************/
    typedef struct
    {
        RcCalibMatch                                rc_calib_infos;
        cv::Mat                                     radar_camera_homography;
        cv::Mat                                     camera_radar_homography;
    }RvCalibHomography;




    /********************************************************
     * Description          			 �״����״�궨������ṹ��
     * device_id                         Ŀ�걻̽����豸ID
     * id                                Ŀ���ID
     * target                            Ŀ�����Ϣ
     * time_ms                           Ŀ���ʱ���
    ********************************************************/
    typedef struct
    {
        unsigned int				 device_id;
        unsigned int			     id;
        Target                       target;
        long long                    time_ms;
    }RrCtarget;


    /********************************************************
     * Description          			 �״����״�Ŀ��궨�ĵ��
     * device1_target                    �豸1��Ŀ��
     * device2_target                    �豸1��Ŀ��
    ********************************************************/
    typedef struct
    {
        RrCtarget device1_target;
        RrCtarget device2_target;
    }RrCalibPoint;


    /********************************************************
    * Description          			    �״����״�Ŀ��궨�ı궨�㼣��
    * device_id1                        �豸1�����
    * device_id2                        �豸2�����
    * devices_calib_paths               �����豸��ĵ㼣������
   ********************************************************/
    typedef struct
    {
        unsigned int device_id1;
        unsigned int device_id2;
        std::vector<std::vector<RrCalibPoint>> devices_calib_paths;
    }RrCalibPaths;

    /********************************************************
    * Description          			    �״����״�Ŀ��궨�ı궨״̬
    * CalibNo                           δ�궨�Ľ׶�
    * CalibStart                        �궨��ʼ�׶�
    * CalibIng                          �궨����ʱ�׶�
    * CalibEnd                          �궨����
   ********************************************************/
    typedef enum
    {
        CalibNo = 0x00,
        CalibStart,
        CalibIng,
        CalibEnd
    }RrCalibState;
}
