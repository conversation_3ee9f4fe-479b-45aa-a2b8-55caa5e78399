//
// Created by Administrator on 2024/8/24.
//
#include "zlogger.h"
#include "calib_radar_radar.h"
#include "pipeline_track_fusion.h"

using namespace tecu_r1000_0_algorithm;
ZLOGGER_HEADER_DEFINE("ALG_CALIB_RADAR_RADAR")


/*
 * @Name:CalibRadarRadar
 * @Description: 雷达与雷达间的标定方法类的创建
 *
 * @Input
 * channel_id: 通道号
 * rr_calib_config: 雷达与雷达间标定配置信息
 *
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/8/24
 * Time: 11:06
 * Author: WangXing
 * Content: Create
*/
CalibRadarRadar::CalibRadarRadar(int channel_id, Config rr_calib_config)
{
    rrcalib_channel_id = channel_id;
    rrc_target_save_time_threshold = 301;
    rrcalib_path_number = rr_calib_config.rrcalib_path_number;
    ZINFO("Create CalibRadarRadar, rrcalib_channel_id:%d rrc_target_save_time_threshold:%lld rrcalib_path_number:%d \n",
          rrcalib_channel_id,rrc_target_save_time_threshold, rrcalib_path_number);

    rrc_device_min_distance_threshold = rr_calib_config.min_detect_distance;
    rrc_device_max_distance_threshold = rr_calib_config.max_detect_distance;

    ZINFO("Create CalibRadarRadar, rrc_device_min_distance_threshold:%.2f rrc_device_max_distance_threshold:%.2f \n",
           rrc_device_min_distance_threshold, rrc_device_max_distance_threshold);

    min_path_gap = 12;
    speed_gap_threshold = 3.60f;
    ZINFO("Create CalibRadarRadar, min_path_gap:%d speed_gap_threshold:%.2f\n",
          min_path_gap, speed_gap_threshold);
}

/*
 * @Name: ~CalibRadarRadar
 * @Description: 雷达与雷达标定方法类的析构
 *
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/8/24
 * Time: 11:16
 * Author: WangXing
 * Content: Create
*/
CalibRadarRadar::~CalibRadarRadar()
{
    std::vector<RrCtarget>().swap(rrctarget_vec);
    rRcalibPathsUninit(this->rrcalib_paths_vec);
    ZINFO("Release CalibRadarRadar, rrcalib_channel_id：%d rrctarget_vec.size:%d \n",
          rrcalib_channel_id,rrctarget_vec.size());
}

/*
 * @Name: rRadarCalibConfig
 * @Description: 雷达与雷达标定间的参数配置
 *
 * @Input
 * base_device_id: 基准设备序号
 * device_list: 设备信息列表
 * devices_map: 基准设备向其它设备的映射矩阵字典
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/8/24
 * Time: 11:17
 * Author: WangXing
 * Content: Create
*/
void CalibRadarRadar::rRadarCalibConfig(unsigned int base_device_id, DeviceList device_list, DevicesMap devices_map)
{
    //传参
    rrcalib_base_device_id = base_device_id;
    rrcalib_device_list    = device_list;
    rrcalib_device_map     = devices_map;
    ZINFO("rRadarCalibConfig rrcalib_base_device_id:%d device_list cnt:%d \n",
          rrcalib_base_device_id, rrcalib_device_list.device_cnt);
    for(unsigned int dl = 0; dl < rrcalib_device_list.device_cnt; dl++)
    {
        ZINFO("rRadarCalibConfig device_list device_id:%d device_enable:%d device_longitude:%f device_latitude:%f device_direction:%f \n",
              rrcalib_device_list.device_list[dl].device_id,        rrcalib_device_list.device_list[dl].device_enable,
              rrcalib_device_list.device_list[dl].device_longitude, rrcalib_device_list.device_list[dl].device_latitude, rrcalib_device_list.device_list[dl].device_direction);
    }

    ZINFO("rRadarCalibConfig rrcalib_device_map device_map_mats size:%d \n",
          rrcalib_device_map.device_map_mats.size());
    for (auto it = rrcalib_device_map.device_map_mats.begin(); it != rrcalib_device_map.device_map_mats.end(); ++it)
    {
        Eigen::Matrix3d device_map_mat = it->second.calib_mat_ob;
        std::string device_map_str = "";
        trackFusionManger::print_matrix3d_info(device_map_mat, device_map_str);
        ZINFO("rRadarCalibConfig devices_map device_id_key:%d device_mat:%s, device_id:%d -> base_device_id:%d \n",
              it->first, device_map_str.c_str(), it->second.device_id, rrcalib_base_device_id);
    }

    //根据设备列表，对两两雷达之间的标定关系的点迹容器初始化
    rrcalib_state = RrCalibState::CalibStart;
    //清空之前的容器
    std::vector<RrCtarget>().swap(rrctarget_vec);
    rRcalibPathsUninit(this->rrcalib_paths_vec);
    //根据设备列表进行容器初始化
    rRcalibPathsInit(this->rrcalib_paths_vec, device_list);
}


/*
 * @Name: rRadarCalib
 * @Description: 雷达与雷达间的标定
 *
 * @Input
 * InputTargets: 输入数据的信息
 * timestamp_ms: 时间戳
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/8/24
 * Time: 11:25
 * Author: WangXing
 * Content: Create
*/
void CalibRadarRadar::rRadarCalib(InputTargets input_targets, long long timestamp_ms)
{
#ifdef DEBUG
    ZDEBUG("rrcalib_state:%d \n", rrcalib_state);
#endif
    //输入数据的缓存
    if(rrcalib_state == RrCalibState::CalibStart)
    {
        rRctargetUpdate(this->rrctarget_vec, input_targets, timestamp_ms);

#ifdef DEBUG
        ZDEBUG("rRadarCalib rrctarget_vec.size:%d timestamp_ms:%lld \n", rrctarget_vec.size(), timestamp_ms);
        for (unsigned int sot = 0; sot < rrctarget_vec.size(); sot++)
        {
            ZDEBUG("rrctarget_vec id:%d dv_id:%d x:%.2f y:%f n_plate:%s np_score:%.2f time:%lld\n",
                   rrctarget_vec[sot].id,                 rrctarget_vec[sot].device_id,
                   rrctarget_vec[sot].target.x,           rrctarget_vec[sot].target.y,
                   rrctarget_vec[sot].target.number_plate,rrctarget_vec[sot].target.np_score,
                   rrctarget_vec[sot].time_ms
            );
        }
#endif

        //标定点迹匹配对容器
        rRcalibPathsUpdate(this->rrcalib_paths_vec, this->rrctarget_vec, rrcalib_state);

#ifdef DEBUG
        ZDEBUG("rRcalibPathsUpdate,rrcalib_paths_vec size:%d\n",
               rrcalib_paths_vec.size());
        for(size_t rcp = 0; rcp < rrcalib_paths_vec.size(); rcp++)
        {
            ZDEBUG("device_id1:%d device_id2:%d calib_paths size:%d \n",
                   rrcalib_paths_vec[rcp].device_id1, rrcalib_paths_vec[rcp].device_id2,rrcalib_paths_vec[rcp].devices_calib_paths.size());
            for(size_t rccp = 0; rccp < rrcalib_paths_vec[rcp].devices_calib_paths.size(); rccp++)
            {
                ZDEBUG("calib path size:%d\n", rrcalib_paths_vec[rcp].devices_calib_paths[rccp].size());
            }
        }
#endif

    }
    else if(rrcalib_state == RrCalibState::CalibIng)
    {
        rRcalibPaths(this->rrcalib_paths_vec, rrcalib_state);

#ifdef DEBUG
//        ZDEBUG("rRcalibPathsUpdate,rrcalib_paths_vec size:%d\n",
//               rrcalib_paths_vec.size());
//        for(size_t rcp = 0; rcp < rrcalib_paths_vec.size(); rcp++)
//        {
//            ZDEBUG("device_id1:%d device_id2:%d calib_paths size:%d \n",
//                   rrcalib_paths_vec[rcp].device_id1, rrcalib_paths_vec[rcp].device_id2,rrcalib_paths_vec[rcp].devices_calib_paths.size());
//            for(size_t rccp = 0; rccp < rrcalib_paths_vec[rcp].devices_calib_paths.size(); rccp++)
//            {
//                ZDEBUG("calib path size:%d\n", rrcalib_paths_vec[rcp].devices_calib_paths[rccp].size());
//                for(size_t rp = 0; rp < rrcalib_paths_vec[rcp].devices_calib_paths[rccp].size(); rp++)
//                {
//                    ZDEBUG("number_plate1:%s id1:%d device_id1:%d id1_x:%.2f id1_y:%.2f speed1:%.2f number_plate2:%s id2:%d device_id2:%d id2_x:%.2f id2_y:%.2f speed2:%.2f \n",
//                           rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device1_target.target.number_plate,
//                           rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device1_target.id,
//                           rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device1_target.device_id,
//                           rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device1_target.target.x,
//                           rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device1_target.target.y,
//                           rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device1_target.target.speed,
//                           rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device2_target.target.number_plate,
//                           rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device2_target.id,
//                           rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device2_target.device_id,
//                           rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device2_target.target.x,
//                           rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device2_target.target.y,
//                           rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device2_target.target.speed
//                    );
//                }
//            }
//        }
#endif
    }
    else
    {
        if(rrcalib_state == RrCalibState::CalibNo)
        {
            if(this->rrcalib_paths_vec.size() != 0)
            {
                rRcalibPathsUninit(this->rrcalib_paths_vec);
                ZDEBUG("RrCalibState::CalibNo, rRcalibPathsUninit size:%d \n", this->rrcalib_paths_vec.size());
            }
            if(rrctarget_vec.size() != 0)
            {
                std::vector<RrCtarget>().swap(rrctarget_vec);
                ZDEBUG("RrCalibState::CalibNo, rrctarget_vec is size:%d \n", rrctarget_vec.size());
            }
        }
        //标定结束, 容器销毁，状态更新
    }
}




/*
 * @Name: rRcalibPaths
 * @Description: 基于相邻设备重叠区域的点迹标定
 *
 * @Input
 * rrcalib_paths_vec_: 相邻设备重叠区域的点迹容器的扩充
 * rrcalib_state_: 雷达与雷达的标定状态
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/8/26
 * Time: 9:58
 * Author: WangXing
 * Content: Create
*/
void CalibRadarRadar::rRcalibPaths(std::vector<RrCalibPaths> &rrcalib_paths_vec_, RrCalibState &rrcalib_state_)
{

    std::vector<DeviceCalibMat> devices_calib_vec;
    for(size_t rcp = 0; rcp < rrcalib_paths_vec.size(); rcp++)
    {
        std::vector<float> device_angle_vec;
        std::vector<float> device_tx_vec;
        std::vector<float> device_ty_vec;
        for(size_t rccp = 0; rccp < rrcalib_paths_vec[rcp].devices_calib_paths.size() - 1; rccp++)
        {
            //根据单条轨迹计算出两设备间的方位角
            ZDEBUG("calib path size:%d\n", rrcalib_paths_vec[rcp].devices_calib_paths[rccp].size());
            for(size_t rp = 0; rp < rrcalib_paths_vec[rcp].devices_calib_paths[rccp].size(); rp++)
            {
                ZDEBUG("number_plate1:%s id1:%d device_id1:%d id1_x:%.2f id1_y:%.2f number_plate2:%s id2:%d device_id2:%d id2_x:%.2f id2_y:%.2f \n",
                       rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device1_target.target.number_plate,
                       rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device1_target.id,
                       rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device1_target.device_id,
                       rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device1_target.target.x,
                       rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device1_target.target.y,
                       rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device2_target.target.number_plate,
                       rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device2_target.id,
                       rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device2_target.device_id,
                       rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device2_target.target.x,
                       rrcalib_paths_vec[rcp].devices_calib_paths[rccp][rp].device2_target.target.y
                );
            }
            float device_angle = 0;
            float device_tx    = 0;
            float device_ty    = 0;
            int calrr_ret = calRrcalcalib(rrcalib_paths_vec[rcp].devices_calib_paths[rccp], device_angle, device_tx, device_ty);
            if(calrr_ret == 0)
            {
                device_angle_vec.push_back(device_angle);
                device_tx_vec.push_back(device_tx);
                device_ty_vec.push_back(device_ty);
            }
        }
        std::string device_angle_vec_str = "";
        std::string device_tx_vec_str = "";
        std::string device_ty_vec_str = "";
        for(size_t dv = 0; dv < device_angle_vec.size(); dv++)
        {
            device_angle_vec_str += " " +std::to_string(device_angle_vec[dv]);
            device_tx_vec_str += " " +std::to_string(device_tx_vec[dv]);
            device_ty_vec_str += " " +std::to_string(device_ty_vec[dv]);
        }
        float angle_device = calMadmean(device_angle_vec, 2.5);
        float tx_device = calMadmean(device_tx_vec, 2.5);
        float ty_device = calMadmean(device_ty_vec, 2.5);
        ZDEBUG("rRcalibPaths device_id1:%d device_id2:%d calib_paths size:%d angle_device:%.2f tx:%.2f ty:%.2f\n",
               rrcalib_paths_vec[rcp].device_id1, rrcalib_paths_vec[rcp].device_id2,rrcalib_paths_vec[rcp].devices_calib_paths.size(),
               angle_device, tx_device, ty_device);
        ZDEBUG("device_angle_vec_str:%s device_tx_vec_str:%s device_ty_vec_str:%s \n",
               device_angle_vec_str.c_str(), device_tx_vec_str.c_str(), device_ty_vec_str.c_str());

        DeviceCalibMat device_calib_mat_21;
        Eigen::Matrix3d device_homo_mat_21;
        device_homo_mat_21 << cos(angle_device), sin(angle_device), tx_device,
                             -sin(angle_device), cos(angle_device), ty_device,
                              0, 0, 1;
        device_calib_mat_21.device_id2 = rrcalib_paths_vec[rcp].device_id2;
        device_calib_mat_21.device_id1 = rrcalib_paths_vec[rcp].device_id1;
        device_calib_mat_21.calib_mat_2_1 = device_homo_mat_21;
        devices_calib_vec.push_back(device_calib_mat_21);
        std::string device_homo_mat_21_str = "";
        trackFusionManger::print_matrix3d_info(device_homo_mat_21, device_homo_mat_21_str);
        ZDEBUG("device_id:%d -> device_id:%d device_homo_mat_21:%s \n",
               rrcalib_paths_vec[rcp].device_id2, rrcalib_paths_vec[rcp].device_id1,device_homo_mat_21_str.c_str());

        double det = device_homo_mat_21.determinant();
        if(det != 0)
        {
            DeviceCalibMat device_calib_mat_12;
            device_calib_mat_12.device_id2 = rrcalib_paths_vec[rcp].device_id1;
            device_calib_mat_12.device_id1 = rrcalib_paths_vec[rcp].device_id2;
            Eigen::Matrix3d device_homo_mat_12 = device_homo_mat_21.inverse();
            device_calib_mat_12.calib_mat_2_1  = device_homo_mat_12;
            devices_calib_vec.push_back(device_calib_mat_12);

            std::string device_homo_mat_12_str = "";
            trackFusionManger::print_matrix3d_info(device_homo_mat_12, device_homo_mat_12_str);
            ZDEBUG("device_id:%d -> device_id:%d device_homo_mat_12:%s \n",
                   rrcalib_paths_vec[rcp].device_id1, rrcalib_paths_vec[rcp].device_id2,device_homo_mat_21_str.c_str());
        }
    }


    //设备
    unsigned int base_device_id_index = 0;
    for (unsigned int dl = 0; dl < rrcalib_device_list.device_cnt; dl++)
    {
        if (rrcalib_device_list.device_list[dl].device_id == rrcalib_base_device_id)
        {
            base_device_id_index = dl;
            break;
        }
    }


    //映射矩阵容器
    std::vector<DevicesCalibMat> devices_map_vec;
    for (unsigned int dl = 0; dl < rrcalib_device_list.device_cnt; dl++)
    {
        if (dl > base_device_id_index)
        {
            bool device_map_success = false;
            DevicesCalibMat devices_map_mat;
            devices_map_mat.device_id = rrcalib_device_list.device_list[dl].device_id;
            if (dl - base_device_id_index == 1)
            {
                unsigned int device_id2_map = rrcalib_device_list.device_list[dl].device_id;
                unsigned int device_id1_map = rrcalib_base_device_id;
                for (const auto& calibs_mat : devices_calib_vec)
                {
                    if (calibs_mat.device_id1 == device_id1_map
                     && calibs_mat.device_id2 == device_id2_map)
                    {
                        devices_map_mat.calib_mat_ob = calibs_mat.calib_mat_2_1;
                        device_map_success = true;
                        break;
                    }
                }
                if (device_map_success == true)
                {
                    std::string map_mat_str = "";
                    trackFusionManger::print_matrix3d_info(devices_map_mat.calib_mat_ob, map_mat_str);
                    ZINFO("(dl - base_device_id_index) device_id:%d -> base_device_id:%d map mat:%s success! \n",
                           device_id2_map, device_id1_map, map_mat_str.c_str());
                }
                else
                {
                    ZINFO("(dl - base_device_id_index) device_id:%d -> base_device_id:%d map failure, due to devices is not calibs! \n",
                           device_id2_map, device_id1_map);
                }
            }
            else
            {
                Eigen::Matrix3d devices_init_mat = Eigen::Matrix3d::Identity();
                for (unsigned int bdi = dl; bdi > base_device_id_index; bdi--)
                {
                    unsigned int temp_device_id2_map = rrcalib_device_list.device_list[bdi].device_id;
                    unsigned int temp_device_id1_map = rrcalib_device_list.device_list[bdi - 1].device_id;


                    bool exist_near_calib = false;
                    for (const auto& calibs_mat : devices_calib_vec)
                    {
                        if (calibs_mat.device_id1 == temp_device_id1_map
                         && calibs_mat.device_id2 == temp_device_id2_map)
                        {
                            std::string calib_mat_2_1_str = "";
                            trackFusionManger::print_matrix3d_info(calibs_mat.calib_mat_2_1, calib_mat_2_1_str);
                            ZDEBUG("temp_device_id2_map:%d temp_device_id1_map:%d calib_mat_2_1:%s \n",temp_device_id2_map, temp_device_id1_map, calib_mat_2_1_str.c_str());
                            std::string devices_init_mat_str = "";
                            trackFusionManger::print_matrix3d_info(devices_init_mat, devices_init_mat_str);
                            ZDEBUG("temp_device_id2_map:%d temp_device_id1_map:%d devices_init_mat_str:%s \n",temp_device_id2_map, temp_device_id1_map, devices_init_mat_str.c_str());
                            devices_init_mat = devices_init_mat * calibs_mat.calib_mat_2_1;
                            exist_near_calib = true;
                            break;
                        }
                    }
                    if (exist_near_calib == true)
                    {
                        ZINFO("device_id:%d -> device_id:%d calib is exist!\n",
                              temp_device_id2_map, temp_device_id1_map);
                        device_map_success = true;
                    }
                    else
                    {
                        ZINFO("device_id:%d -> device_id:%d calib is not exist!\n",
                              temp_device_id2_map, temp_device_id1_map);
                        device_map_success = false;
                        break;
                    }
                }
                if (device_map_success == true)
                {
                    std::string map_mat_str = "";
                    trackFusionManger::print_matrix3d_info(devices_init_mat, map_mat_str);
                    ZINFO("device_id:%d -> base_device_id:%d map mat:%s success! \n",
                          rrcalib_device_list.device_list[dl].device_id, rrcalib_base_device_id, map_mat_str.c_str());
                    devices_map_mat.calib_mat_ob = devices_init_mat;
                }
                else
                {
                    ZINFO("device_id:%d -> base_device_id:%d map failure, due to device is not calibs! \n",
                          rrcalib_device_list.device_list[dl].device_id);
                }
            }
            if (device_map_success == true)
            {
                devices_map_vec.push_back(devices_map_mat);
            }
        }
        else if (dl < base_device_id_index)
        {
            bool device_map_success = false;
            DevicesCalibMat devices_map_mat;
            devices_map_mat.device_id = rrcalib_device_list.device_list[dl].device_id;
            if (base_device_id_index - dl == 1)
            {
                unsigned int device_id2_map = rrcalib_device_list.device_list[dl].device_id;
                unsigned int device_id1_map = rrcalib_base_device_id;
                for (const auto& calibs_mat : devices_calib_vec)
                {
                    if (calibs_mat.device_id1 == device_id1_map
                        && calibs_mat.device_id2 == device_id2_map)
                    {
                        devices_map_mat.calib_mat_ob = calibs_mat.calib_mat_2_1;
                        device_map_success = true;
                        break;
                    }
                }
                if (device_map_success == true)
                {
                    std::string map_mat_str = "";
                    trackFusionManger::print_matrix3d_info(devices_map_mat.calib_mat_ob, map_mat_str);
                    ZINFO("device_id:%d -> base_device_id:%d map mat:%s success! \n",
                          device_id2_map, device_id1_map, map_mat_str.c_str());
                }
                else
                {
                    ZINFO("device_id:%d -> base_device_id:%d map failure, due to device is not calibs! \n",
                          device_id2_map, device_id1_map);
                }
            }
            else
            {
                Eigen::Matrix3d devices_init_mat = Eigen::Matrix3d::Identity();
                for (unsigned int bdi = dl; bdi < base_device_id_index; bdi++)
                {
                    unsigned int temp_device_id2_map = rrcalib_device_list.device_list[bdi].device_id;
                    unsigned int temp_device_id1_map = rrcalib_device_list.device_list[bdi + 1].device_id;
                    bool exist_near_calib = false;
                    for (const auto& calibs_mat : devices_calib_vec)
                    {
                        if (calibs_mat.device_id1 == temp_device_id1_map
                            && calibs_mat.device_id2 == temp_device_id2_map)
                        {
                            devices_init_mat = devices_init_mat * calibs_mat.calib_mat_2_1;
                            exist_near_calib = true;
                            break;
                        }
                    }
                    if (exist_near_calib == true)
                    {
                        ZINFO("device_id:%d -> device_id:%d calib is exist!\n",
                               temp_device_id2_map, temp_device_id1_map);
                        device_map_success = true;
                    }
                    else
                    {
                        ZERROR("channel id:%d device_id:%d -> device_id:%d calib is not exist!\n",
                                temp_device_id2_map, temp_device_id1_map);
                        device_map_success = false;
                        break;
                    }
                }
                if (device_map_success == true)
                {
                    std::string map_mat_str = "";
                    trackFusionManger::print_matrix3d_info(devices_init_mat, map_mat_str);
                    ZINFO("device_id:%d -> base_device_id:%d map mat:%s success! \n",
                          rrcalib_device_list.device_list[dl].device_id, map_mat_str.c_str());
                    devices_map_mat.calib_mat_ob = devices_init_mat;
                }
                else
                {
                    ZERROR("device_id:%d -> base_device_id:%d map failure, due to device is not calibs! \n",
                           rrcalib_device_list.device_list[dl].device_id);
                }
            }
            if (device_map_success == true)
            {
                devices_map_vec.push_back(devices_map_mat);
            }
        }
        else
        {
            //dl == base_device_id_index
            ZINFO("base_device_id:%d -> base_device_id:%d map failure, due to device_id is base device! \n",
                  rrcalib_device_list.device_list[dl].device_id, rrcalib_device_list.device_list[base_device_id_index].device_id);

            continue;
        }
    }


    //数据的更新
    std::map<unsigned int, DevicesCalibMat>().swap(rrcalib_device_map.device_map_mats);
    std::map<unsigned int, DevicesCalibMat> temp_devices_mat_map;
    for (const auto& calibs_mat : devices_map_vec)
    {
        temp_devices_mat_map.insert(std::make_pair(calibs_mat.device_id, calibs_mat));
    }
    rrcalib_device_map.device_map_mats  = temp_devices_mat_map;
    rrcalib_state_ = RrCalibState ::CalibEnd;
}


/*
 * @Name: calVectormean
 * @Description: 计算容器的平均值
 *
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/8/27
 * Time: 12:05
 * Author: WangXing
 * Content: Create
*/
float CalibRadarRadar::calVectormean(const std::vector<float>& data)
{
    if(data.size() == 0)
    {
        return 0.0f;
    }
    else
    {
        float data_sum =0.0f;
        for(size_t dt = 0; dt < data.size(); dt++)
        {
            data_sum += data[dt];
        }
        return data_sum / (float)data.size();
    }
}


// 计算质心
Eigen::Vector2d CalibRadarRadar::calCentroid(const std::vector<Eigen::Vector2d>& points)
{
    Eigen::Vector2d centroid(0, 0);
    for (const auto& p : points)
    {
        centroid += p;
    }
    centroid /= points.size();
    return centroid;
}


int CalibRadarRadar::calRotationAndTranslation(const std::vector<Eigen::Vector2d> &src, const std::vector<Eigen::Vector2d> &dst, float &best_angle, float &best_tx, float &best_ty)
{
//    // 计算质心
    Eigen::Vector2d centroidSrc = calCentroid(src);
    Eigen::Vector2d centroidDst = calCentroid(dst);

    // 中心化点集
    float min_proj_error = 1000 * 1000;
    for(size_t sc = 0; sc < src.size(); sc++)
    {
        int next_point_index = (int)sc;
        int end_point_index  = (int)src.size() - sc - 1;
        if(end_point_index - next_point_index < 12)
        {
            break;
        }
        else
        {
            Eigen::Vector2d device1_vector = src[src.size() - sc - 1] - src[sc];
            Eigen::Vector2d device2_vector = dst[src.size() - sc - 1] - dst[sc];
            bool is_exist_mat = false;
            double angle  = trackFusionManger::compute_direct(device2_vector, device1_vector, is_exist_mat);
            Eigen::Matrix2d rotation_mat;
            rotation_mat << cos(angle ),  sin(angle ),
                            -sin(angle ),  cos(angle );
            // 计算平移向量
            Eigen::Vector2d translation_vec = centroidSrc - (rotation_mat * centroidDst);
            Eigen::Matrix3d homo_mat;
            homo_mat <<  cos( angle ),    sin(angle), translation_vec.x(),
                        -sin(angle ),    cos(angle), translation_vec.y(),
                         0, 0, 1;
            float proj_error = calProjError(src, dst, homo_mat);
            if(proj_error < min_proj_error)
            {
                min_proj_error = proj_error;
                best_angle = angle;
                best_tx    = translation_vec.x();
                best_ty    = translation_vec.y();
            }
            std::string homo_mat_str = "";
            trackFusionManger::print_matrix3d_info(homo_mat, homo_mat_str);

            float radians = 45.0f;
            double result = cos(radians); // 计算余弦值

            ZDEBUG("next_point_index:%d end_point_index:%d is_exist_mat:%d radians:%.2f angle:%.2f translation_vec x:%.2f y:%.2f proj_error:%.2f homo_mat_str:%s cos45:%.2f\n",
                   next_point_index, end_point_index, is_exist_mat, angle, angle * 180.0f / M_PI, translation_vec.x(),translation_vec.y(), proj_error,homo_mat_str.c_str(), result);
        }
    }
    if(min_proj_error < 1000 * 1000)
    {
        return 0;
    }
    else
    {
        return -1;
    }

    //计算质心
//    Eigen::Vector2d centroidSrc = calCentroid(src);
//    Eigen::Vector2d centroidDst = calCentroid(dst);
//
//    float min_proj_error = 1000 * 1000;
//    for(size_t alv = 0; alv < angle_list_vec.size(); alv++)
//    {
//        float angle = angle_list_vec[alv];
//        Eigen::Matrix2d rotation_mat;
//        rotation_mat << cos(angle), sin(angle),
//                        -sin(angle), cos(angle);
//        // 计算平移向量
//        Eigen::Vector2d translation_vec = centroidSrc - (rotation_mat * centroidDst);
//        Eigen::Matrix3d homo_mat;
//        homo_mat << cos(angle), sin(angle), translation_vec.x(),
//                    -sin(angle), cos(angle),translation_vec.y(),
//                    0, 0, 1;
//        float proj_error = calProjError(src, dst, homo_mat);
//        if(proj_error < min_proj_error)
//        {
//            min_proj_error = proj_error;
//            best_angle     = angle;
//            best_tx        = translation_vec.x();
//            best_ty        = translation_vec.y();
//        }
//        ZDEBUG("th:%d angle:%.2f translation_vec x:%.2f y:%.2f proj_error:%.2f\n",
//               alv, angle, translation_vec.x(),translation_vec.y(), proj_error);
//    }
}


// 计算平均绝对值偏差（MAD）
float CalibRadarRadar::calculateMad(const std::vector<float>& data)
{
    if (data.empty()) return 0.0;

    float sum = 0.0;
    for (float value : data) {
        sum += value;
    }
    float mean = sum / data.size();

    float madSum = 0.0;
    for (float value : data) {
        madSum += std::fabs(value - mean);
    }
    float mad = madSum / data.size();

    return mad;
}


float CalibRadarRadar::calMadmean(const std::vector<float>& data, float threshold_multiplier)
{
    float mad = calculateMad(data);
    float data_mean = calVectormean(data);

    std::vector<float> inliers_data;
    for(size_t i = 0; i < data.size(); i++)
    {
        if(std::fabs(data[i] - data_mean) < threshold_multiplier * mad)
        {
            inliers_data.push_back(data[i]);
        }
    }
    if(inliers_data.size() > 0)
    {
        float data_mad_mean = calVectormean(inliers_data);
        return data_mad_mean;
    }
    else
    {
        return data_mean;
    }
}

float CalibRadarRadar::calProjError(const std::vector<Eigen::Vector2d> &src, const std::vector<Eigen::Vector2d> &dst, Eigen::Matrix3d &homo_mat)
{
    float x_error_sum       = 0;
    float y_error_sum       = 0;
    std::vector<float>      cal_error_list;
    for (int dc = 0; dc < dst.size(); dc++)
    {
        Eigen::Vector3d other_p2_3d;
        other_p2_3d << dst[dc].x(),
                       dst[dc].y(),
                       1;
        Eigen::Vector3d other_p2_map;
        other_p2_map = homo_mat * other_p2_3d;
        float cal_error = 0.5f * fabs(src[dc].x() - other_p2_map.x()) + 0.5f * fabs(src[dc].y() - other_p2_map.y());
        cal_error_list.push_back(cal_error);

        x_error_sum += fabs(src[dc].x() - other_p2_map.x());
        y_error_sum += fabs(src[dc].y() - other_p2_map.y());
    }
    float x_error_mean = x_error_sum / (float)dst.size();
    float y_error_mean = y_error_sum / (float)dst.size();
    return x_error_mean * 0.5f + y_error_mean * 0.5f;
}




/*
 * @Name: calRrcalcalib
 * @Description: 通过重叠区域内点迹,计算两者的对应关系
 *
 * @Input
 * devices_path: 单条邻近设备中,重叠区域的点迹
 *
 * @Output
 * 0:  计算成功
 * -1: 计算失败
 *
 * @Edit History
 * Date: 2024/8/26
 * Time: 10:38
 * Author: WangXing
 * Content: Create
*/
int CalibRadarRadar::calRrcalcalib(std::vector<RrCalibPoint> devices_path, float &device_angle, float &device_tx, float &device_ty)
{
    if(devices_path.size() <= 12)
    {
        return -1;
    }
    else
    {
        std::vector<Eigen::Vector2d> device1_path;
        std::vector<Eigen::Vector2d> device2_path;
        for(size_t dp = 0; dp < devices_path.size(); dp++)
        {
            Eigen::Vector2d device1_point;
            device1_point << devices_path[dp].device1_target.target.x,devices_path[dp].device1_target.target.y;
            Eigen::Vector2d device2_point;
            device2_point << devices_path[dp].device2_target.target.x,devices_path[dp].device2_target.target.y;
            device1_path.push_back(device1_point);
            device2_path.push_back(device2_point);
        }
        int cal_ret = calRotationAndTranslation(device1_path, device2_path, device_angle, device_tx, device_ty);
        ZDEBUG("device_id1:%d id1:%d np:%s device_id2:%d id2:%d np2:%s radians:%.2f angle:%.2f tx:%.2f ty:%.2f \n",
               devices_path[0].device1_target.device_id, devices_path[0].device1_target.id, devices_path[0].device1_target.target.number_plate,
               devices_path[0].device2_target.device_id, devices_path[0].device2_target.id, devices_path[0].device2_target.target.number_plate,
               device_angle,   device_angle * 180 / M_PI, device_tx, device_ty);

        return cal_ret;
    }
}



/*
 * @Name: rRcalibPathsUpdate
 * @Description: 雷达与雷达间点迹对的更新
 *
 * @Input
 * rrcalib_paths_vec_:雷达设备间的点迹对扩充
 * rrctarget_vec_: 缓存的输入数据容器
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/8/24
 * Time: 14:57
 * Author: WangXing
 * Content: Create
*/
void CalibRadarRadar::rRcalibPathsUpdate(std::vector<RrCalibPaths> &rrcalib_paths_vec_, std::vector<RrCtarget> &rrctarget_vec_, RrCalibState &rrcalib_state_)
{
    //在rrctarget_vec_容器中查询是否存在车牌号相同的两个目标
    std::vector<RrCalibPoint> temp_rrcalib_pionts;
    for(size_t rrt = 0; rrt < rrctarget_vec_.size(); rrt++)
    {
        int device_id = (int)rrctarget_vec_[rrt].device_id;
        unsigned int id        = rrctarget_vec_[rrt].id;
        if(rrctarget_vec_[rrt].target.number_plate[0] == '\0')
        {
            continue;
        }
        unsigned int match_target_index = 0;
        bool is_exist_match = false;
        for(size_t rct = 0; rct < rrctarget_vec_.size(); rct++)
        {
            if(rrctarget_vec_[rct].target.number_plate[0] == '\0')
            {
                continue;
            }
            if(id == rrctarget_vec_[rct].id
            && device_id == rrctarget_vec_[rct].device_id)
            {
                continue;
            }
            //相邻两个设备的车牌号码相同, 速度差距小于speed_gap_threshold
            if(abs(device_id - (int)rrctarget_vec_[rct].device_id) == 1
            && strcmp(rrctarget_vec_[rrt].target.number_plate,  rrctarget_vec_[rct].target.number_plate) == 0
            && fabs(rrctarget_vec_[rrt].target.speed - rrctarget_vec_[rct].target.speed) < speed_gap_threshold)
            {
                match_target_index = rct;
                is_exist_match = true;
                break;
            }
        }
        if(is_exist_match == true)
        {
            //根据设备的编号判断两者设备的前后顺序 编号小的在前面
            RrCalibPoint rrcalib_point;
            if(device_id - rrctarget_vec_[match_target_index].device_id > 0)
            {
                rrcalib_point.device1_target = rrctarget_vec_[match_target_index];
                rrcalib_point.device2_target = rrctarget_vec_[rrt];
            }
            else
            {
                rrcalib_point.device1_target = rrctarget_vec_[rrt];
                rrcalib_point.device2_target = rrctarget_vec_[match_target_index];
            }
            temp_rrcalib_pionts.push_back(rrcalib_point);
        }
    }


    //将当前的目标位置添加到对应的容器中
    for(size_t trcc = 0; trcc < temp_rrcalib_pionts.size(); trcc++)
    {
        for(size_t rrp = 0; rrp < rrcalib_paths_vec_.size(); rrp++)
        {
            unsigned int device_id1 = rrcalib_paths_vec_[rrp].device_id1;
            unsigned int device_id2 = rrcalib_paths_vec_[rrp].device_id2;
            //当轨迹的数量大于设定的值则不进行扩充
            if(rrcalib_paths_vec_[rrp].devices_calib_paths.size() > rrcalib_path_number + 1)
            {
                continue;
            }
            if(device_id1 != temp_rrcalib_pionts[trcc].device1_target.device_id
            || device_id2 != temp_rrcalib_pionts[trcc].device2_target.device_id)
            {
                continue;
            }
            //判断此两个目标是否在标定的点迹对容器中
            bool is_exist_match = false;
            unsigned int is_match_index = 0;
            for(size_t ttc = 0; ttc < rrcalib_paths_vec_[rrp].devices_calib_paths.size(); ttc++)
            {
                //不存在
                if(rrcalib_paths_vec_[rrp].devices_calib_paths[ttc].size() == 0)
                {
                    break;
                }
                //存在车牌号码相同
                if(strcmp(rrcalib_paths_vec_[rrp].devices_calib_paths[ttc][0].device1_target.target.number_plate,
                          temp_rrcalib_pionts[trcc].device1_target.target.number_plate) == 0)
                {
                    is_exist_match = true;
                    is_match_index = ttc;
                    break;
                }
            }
            if(is_exist_match == false)
            {
                std::vector<RrCalibPoint> rrcalib_path;
                rrcalib_path.push_back(temp_rrcalib_pionts[trcc]);
                rrcalib_paths_vec_[rrp].devices_calib_paths.push_back(rrcalib_path);
            }
            else
            {
                rrcalib_paths_vec_[rrp].devices_calib_paths[is_match_index].push_back(temp_rrcalib_pionts[trcc]);
            }
        }
    }


    //检查数据获取是否成功
    bool is_match_finish = false;
    for (size_t rrp = 0; rrp < rrcalib_paths_vec_.size(); rrp++)
    {
        if (rrcalib_paths_vec_[rrp].devices_calib_paths.size() >= rrcalib_path_number + 1)
        {
            is_match_finish = true;
        }
        else
        {
            is_match_finish = false;
            break;
        }
    }
    if(is_match_finish == true)
    {
        rrcalib_state_ = RrCalibState ::CalibIng;
    }
}



/*
 * @Name: rRctargetUpdate
 * @Description:
 *
 * @Input
 * rrctarget_vec_: 输入数据的缓存
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/8/24
 * Time: 11:48
 * Author: WangXing
 * Content: Create
*/
void CalibRadarRadar::rRctargetUpdate(std::vector<RrCtarget> &rrctarget_vec_, InputTargets input_targets, long long timestamp_ms)
{
    for (unsigned int itc = 0; itc < input_targets.target_cnt; itc++)
    {
        if(input_targets.device_type == DeviceType::Bayonet_Camera_Device)
        {
            continue;
        }
        //当目标大于rrc_device_max_distance_threshold米时,则不计入重叠区域的计算
        if(input_targets.targets.target_devices[itc].y > rrc_device_max_distance_threshold)
        {
            continue;
        }

        unsigned int device_id = input_targets.device_id;
        unsigned int target_id = input_targets.targets.target_devices[itc].id;

        bool exist_splicing_input = false;
        unsigned int exist_splicing_index = 0;
        for (unsigned int sic = 0; sic < rrctarget_vec_.size(); sic++)
        {
            if (rrctarget_vec_[sic].device_id == device_id
             && rrctarget_vec_[sic].id == target_id)
            {
                exist_splicing_input = true;
                exist_splicing_index = sic;
            }
        }

        if (exist_splicing_input == true)
        {
            rrctarget_vec_[exist_splicing_index].id        = input_targets.targets.target_devices[itc].id;
            rrctarget_vec_[exist_splicing_index].device_id = input_targets.device_id;
            rrctarget_vec_[exist_splicing_index].target    = input_targets.targets.target_devices[itc];
            rrctarget_vec_[exist_splicing_index].time_ms   = timestamp_ms;

        }
        else
        {
            RrCtarget rr_calib_target;
            rr_calib_target.id        = input_targets.targets.target_devices[itc].id;
            rr_calib_target.device_id = input_targets.device_id;
            rr_calib_target.target    = input_targets.targets.target_devices[itc];
            rr_calib_target.time_ms   = timestamp_ms;
            rrctarget_vec_.push_back(rr_calib_target);
        }
    }

    //输入目标长时间未更新，则删除
    std::vector<RrCtarget>::iterator rrctarget_vec_iter;
    for (rrctarget_vec_iter = rrctarget_vec_.begin(); rrctarget_vec_iter != rrctarget_vec_.end();)
    {
        if (abs(timestamp_ms - rrctarget_vec_iter->time_ms) > rrc_target_save_time_threshold)
        {
            ZDEBUG("rRctargetUpdate device_id:%d id:%d is exceed DELETE, timestamp_ms:%lld target_time_ms:%lld rrc_target_save_time_threshold:%lld\n",
                   rrctarget_vec_iter->device_id, rrctarget_vec_iter->id,
                   timestamp_ms, rrctarget_vec_iter->time_ms, rrc_target_save_time_threshold);
            rrctarget_vec_iter = rrctarget_vec_.erase(rrctarget_vec_iter);
        }
        else
        {
            rrctarget_vec_iter++;
        }
    }
}


/*
 * @Name: rRcalibPathsUninit
 * @Description: 对rrcalib_paths_vec_这个容器进行释放
 *
 * @Input
 * rrcalib_paths_vec_: 释放的容器
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/8/24
 * Time: 14:28
 * Author: WangXing
 * Content: Create
*/
void CalibRadarRadar::rRcalibPathsUninit(std::vector<RrCalibPaths> &rrcalib_paths_vec_)
{
    for(size_t rrp = 0; rrp < rrcalib_paths_vec_.size(); rrp++)
    {
        for(size_t dcp = 0; dcp < rrcalib_paths_vec_[rrp].devices_calib_paths.size(); dcp++)
        {
            for(size_t dcpp = 0; dcpp < rrcalib_paths_vec_[rrp].devices_calib_paths[dcp].size(); dcpp++)
            {
                std::vector<RrCalibPoint>().swap(rrcalib_paths_vec_[rrp].devices_calib_paths[dcp]);
            }
        }
        std::vector<std::vector<RrCalibPoint>>().swap(rrcalib_paths_vec_[rrp].devices_calib_paths);
    }
    std::vector<RrCalibPaths>().swap(rrcalib_paths_vec_);
    ZINFO("Release rRcalibPathsUninit, rrcalib_paths_vec_ size:%d rrcalib_paths_vec_.capacity:%d\n",
          rrcalib_paths_vec_.size(), rrcalib_paths_vec_.capacity());
}

/*
 * @Name: rRcalibPathsInit
 * @Description: 两两雷视设备间的创建
 *
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/8/24
 * Time: 14:42
 * Author: WangXing
 * Content: Create
*/
void CalibRadarRadar::rRcalibPathsInit(std::vector<RrCalibPaths> &rrcalib_paths_vec_, DeviceList device_list)
{
    if(device_list.device_cnt < 2)
    {

    }
    else
    {
        for(size_t dl = 1; dl < device_list.device_cnt; dl++)
        {
            RrCalibPaths rc_calib_paths;
            rc_calib_paths.device_id1 = device_list.device_list[dl - 1].device_id;
            rc_calib_paths.device_id2 = device_list.device_list[dl].device_id;
            //rc_calib_paths.devices_calib_paths = empty();
            rrcalib_paths_vec_.push_back(rc_calib_paths);
        }
    }
    ZINFO("rRcalibPathsInit device_cnt:%d\n",device_list.device_cnt);
    for(size_t rrc = 0; rrc < rrcalib_paths_vec_.size(); rrc++)
    {
        ZINFO("rRcalibPathsInit device_id1:%d device_id2:%d devices_calib_paths size:%d\n",
              rrcalib_paths_vec_[rrc].device_id1, rrcalib_paths_vec_[rrc].device_id2, rrcalib_paths_vec_[rrc].devices_calib_paths.size());
    }
}