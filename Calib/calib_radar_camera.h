//
// Created by Administrator on 2024/7/16.
//
#pragma once
#include "calib_header.h"
#include "config.h"

namespace tecu_r1000_0_algorithm
{
    class CalibCameraRadar
    {
    public:
        CalibCameraRadar(int channel_id, Config rc_calib_config);
        ~CalibCameraRadar();

        //
        int calib_cr_channel_id;

        //通道内卡口相机与雷视一体机的标定容器
        std::vector<RvCalibMat> rv_calib_vec;

        //相机与雷达的标定
        int rc_devices_calib(RvCalibMat &rv_calib_mat);

        //rvcalib_vec的更新
        int rccalib_vec_update(RvCalibMat rv_calib_mat);

        //坐标转换
        int rccoor_convert(RcMatchPoints *coor, RvCalibMat rv_calib_mats, bool rc_flag);
    private:
        //相机焦距的误差
        float focal_error;
        //相机焦距误差的间隔
        float error_gap;
        //标定的平均误差阈值（雷达坐标系）
        float calib_radar_threshold;
        //标定的x轴所占误差的比例
        float error_xrate;
    };



    class CalibCameraRadarByHomograph
    {
    public:
        CalibCameraRadarByHomograph(int channel_id, Config rc_calib_config);
        ~CalibCameraRadarByHomograph();

        //
        int calib_cr_channel_id;

        //通道内卡口相机与雷视一体机的标定容器
        std::vector<RvCalibHomography> rv_calib_vec;

        //相机与雷达的标定
        int rc_devices_calib(RvCalibHomography &rv_calib_mat);

        //rvcalib_vec的更新
        int rccalib_vec_update(RvCalibHomography rv_calib_mat);

        //坐标转换
        int rccoor_convert(RcMatchPoints *coor, RvCalibHomography rv_calib_mats, bool rc_flag);
    private:
        //相机焦距的误差
        float focal_error;
        //相机焦距误差的间隔
        float error_gap;
        //标定的平均误差阈值（雷达坐标系）
        float calib_radar_threshold;
        //标定的x轴所占误差的比例
        float error_xrate;

        double calc_reproj_err(cv::Mat Homography, std::vector<cv::Point2d> radar_calib_points, std::vector<cv::Point2d> image_calib_points);


    };


}



