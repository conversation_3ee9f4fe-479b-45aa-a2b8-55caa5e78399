#pragma once
#include "config.h"
#include "calib_header.h"
#include "tecu1000_algorithm_header.h"

using namespace tecu1000_alg;

namespace tecu_r1000_0_algorithm
{
    class CalibRadarRadar
    {
    public:
        CalibRadarRadar(int channel_id, Config rr_calib_config);
        ~CalibRadarRadar();

        void rRadarCalibConfig(unsigned int base_device_id, DeviceList device_list, DevicesMap devices_map);

        void rRadarCalib(InputTargets input_targets, long long timestamp_ms);

        RrCalibState rrcalib_state;

        DevicesMap rrcalib_device_map;
    private:

        int        rrcalib_channel_id;
        int        rrcalib_base_device_id;
        DeviceList rrcalib_device_list;


        std::vector<RrCtarget> rrctarget_vec;
        long long rrc_target_save_time_threshold;
        float rrc_device_min_distance_threshold;
        float rrc_device_max_distance_threshold;
        void rRctargetUpdate(std::vector<RrCtarget> &rrctarget_vec_, InputTargets input_targets, long long timestamp_ms);

        //用于雷达与雷达两两标定的容器
        unsigned int  rrcalib_path_number;
        std::vector<RrCalibPaths> rrcalib_paths_vec;
        float speed_gap_threshold;
        void rRcalibPathsInit(std::vector<RrCalibPaths> &rrcalib_paths_vec_, DeviceList device_list);
        void rRcalibPathsUninit(std::vector<RrCalibPaths> &rrcalib_paths_vec_);
        void rRcalibPathsUpdate(std::vector<RrCalibPaths> &rrcalib_paths_vec_, std::vector<RrCtarget> &rrctarget_vec_,RrCalibState &rrcalib_state_);


        //雷达点迹的标定
        int min_path_gap;
        void rRcalibPaths(std::vector<RrCalibPaths> &rrcalib_paths_vec_, RrCalibState &rrcalib_state_);
        int calRrcalcalib(std::vector<RrCalibPoint> devices_path, float &device_angle, float &device_tx, float &device_ty);

        Eigen::Vector2d calCentroid(const std::vector<Eigen::Vector2d>& points);
        int calRotationAndTranslation(const std::vector<Eigen::Vector2d> &src, const std::vector<Eigen::Vector2d> &dst,  float &best_angle, float &best_tx, float &best_ty);
        float calProjError(const std::vector<Eigen::Vector2d> &src, const std::vector<Eigen::Vector2d> &dst, Eigen::Matrix3d &homo_mat);
        float calVectormean(const std::vector<float>& data);
        float calculateMad(const std::vector<float>& data);
        float calMadmean(const std::vector<float>& data, float threshold_multiplier);

    };
}





