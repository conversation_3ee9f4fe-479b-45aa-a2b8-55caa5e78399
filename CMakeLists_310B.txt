# Copyright (c) Huawei Technologies Co., Ltd. 2019. All rights reserved.

# CMake lowest version requirement
cmake_minimum_required(VERSION 3.5.1)

# project information
project(TECU1000)

add_compile_options(-std=c++17)

add_definitions(-DENABLE_DVPP_INTERFACE)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY  "../../out")
set(CMAKE_CXX_FLAGS_DEBUG "-fPIC -O0 -g -Wall")
set(CMAKE_CXX_FLAGS_RELEASE "-fPIC -O2 -Wall")

# 设置FFMPEG库路径
set(FFMPEG_DIR "/home/<USER>/arm_ffmpeg/install")

# 如果定义了CROSS_COMPILE，使用工具链文件中的路径设置
if(DEFINED CMAKE_TOOLCHAIN_FILE)
    # 使用工具链文件中已定义的路径
    # 这些变量在toolchain-aarch64.cmake中设置
    message(STATUS "Using ARM toolchain settings")
    # 使用工具链文件中定义的ACL和OpenCV路径
    message(STATUS "ACL_INC_DIR: ${ACL_INC_DIR}")
    message(STATUS "ACL_LIB_DIR: ${ACL_LIB_DIR}")
    
    # 设置带有FFMPEG支持的OpenCV变量
    set(OpenCV_DIR "/home/<USER>/libs/opencv-4.5.5/build_aarch_ffmpeg/install")
    set(OpenCV_INCLUDE_DIRS "${OpenCV_DIR}/include/opencv4")
    # 使用OpenCV世界库
    set(OpenCV_LIBS "${OpenCV_DIR}/lib/libopencv_world.so")
    
    # 设置FFMPEG库
    set(FFMPEG_LIBS
        "${FFMPEG_DIR}/lib/libavformat.so.58"
        "${FFMPEG_DIR}/lib/libavcodec.so.58"
        "${FFMPEG_DIR}/lib/libavutil.so.56"
        "${FFMPEG_DIR}/lib/libswscale.so.5"
    )
    
    message(STATUS "Manually set OpenCV paths for ARM with FFMPEG support:")
    message(STATUS "    include path: ${OpenCV_INCLUDE_DIRS}")
    message(STATUS "    libraries: ${OpenCV_LIBS}")
    message(STATUS "    FFMPEG libraries: ${FFMPEG_LIBS}")
else()
    # 普通x86编译时的路径设置
    set(INC_PATH $ENV{DDK_PATH})
    if (NOT DEFINED ENV{DDK_PATH})
        set(INC_PATH "/usr/local/Ascend/ascend-toolkit/latest")
        message(STATUS "set default INC_PATH: ${INC_PATH}")
    else()
        message(STATUS "set INC_PATH: ${INC_PATH}")
    endif ()

    set(LIB_PATH $ENV{NPU_HOST_LIB})
    if (NOT DEFINED ENV{NPU_HOST_LIB})
        set(LIB_PATH "/usr/local/Ascend/ascend-toolkit/latest/runtime/lib64/stub")
        message(STATUS "set default LIB_PATH: ${LIB_PATH}")
    else()
        message(STATUS "set LIB_PATH: ${LIB_PATH}")
    endif ()

    # 设置OpenCV路径（用于x86编译）
    set(OpenCV_DIR "/home/<USER>/libs/opencv-4.5.5/build_aarch/install")
endif()

# 禁用系统路径OpenCV查找
set(CMAKE_PREFIX_PATH)
message(STATUS "CMAKE_PREFIX_PATH has been cleared")

# 仅在x86编译时查找OpenCV
if(NOT DEFINED CMAKE_TOOLCHAIN_FILE)
  message(STATUS "Looking for OpenCV for x86")
  find_package(OpenCV REQUIRED)
  if(OpenCV_FOUND)
      message(STATUS "OpenCV library status:")
      message(STATUS "    version: ${OpenCV_VERSION}")
      message(STATUS "    include path: ${OpenCV_INCLUDE_DIRS}")
      message(STATUS "    libraries: ${OpenCV_LIBS}")
  else()
      message(FATAL_ERROR "OpenCV not found!")
  endif()
endif()

# 如果定义了CROSS_COMPILE，使用ACL_INC_DIR和ACL_LIB_DIR
if(DEFINED CMAKE_TOOLCHAIN_FILE)
    include_directories(
        ${ACL_INC_DIR}
        ${OpenCV_INCLUDE_DIRS}
        ${FFMPEG_DIR}/include  # 添加FFMPEG头文件路径
    )
    link_directories(
        ${ACL_LIB_DIR}
        /home/<USER>/project/share_libs/trvf_b500_8m_deps_svp/lib/
        ${OpenCV_DIR}/lib  # 添加OpenCV库目录
        ${FFMPEG_DIR}/lib  # 添加FFMPEG库目录
    )
    
    # 设置rpath包含FFMPEG库目录
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,-rpath-link,${FFMPEG_DIR}/lib")
    # 设置运行时库查找路径
    set(CMAKE_INSTALL_RPATH "${CMAKE_INSTALL_RPATH}:${FFMPEG_DIR}/lib")
    set(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE)
else()
    include_directories(
        ${INC_PATH}/runtime/include/
        ${OpenCV_INCLUDE_DIRS}
    )
    link_directories(
        ${LIB_PATH}
    )
endif()

file(GLOB Self_Header_Files ${PROJECT_SOURCE_DIR}/*.h)

# Header path
include_directories(
    ${INC_PATH}/runtime/include/
    ../inc/
    /home/<USER>/project/Rv_split/lib/opencv_lib/include/opencv4/
    /home/<USER>/project/Rv_split/lib/arm_eigen/install/include/eigen3/
    /home/<USER>/project/Rv_split/lib/arm_jsoncpp/install/include/
    ${INC_PATH}/${RUNTIME_NAME}/include/
    ${Self_Header_Files}
    ${PROJECT_SOURCE_DIR}/Eigen/
    ${PROJECT_SOURCE_DIR}/Calib/
    ${PROJECT_SOURCE_DIR}/Config/
    ${PROJECT_SOURCE_DIR}/DataSave/
    ${PROJECT_SOURCE_DIR}/KalmanFilter/
    ${PROJECT_SOURCE_DIR}/Src/
    ${PROJECT_SOURCE_DIR}/TracksSplicing/
    ${PROJECT_SOURCE_DIR}/LaneSmoothing/
    ${PROJECT_SOURCE_DIR}/CollisionDetection/
    ${PROJECT_SOURCE_DIR}/../tecu1000_0_libs/
    ${PROJECT_SOURCE_DIR}/jsoncpp/
)



if(target STREQUAL "Simulator_Function")
    add_compile_options(-DFUNC_SIM)
endif()

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -pthread -Wall -Ofast -Wfatal-errors -D_MWAITXINTRIN_H_INCLUDED")

# 检测是否是ARM交叉编译
if(CMAKE_CROSSCOMPILING AND CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64")
    set(IS_ARM_CROSS_COMPILE TRUE)
    message(STATUS "Detected ARM cross-compilation environment")
else()
    set(IS_ARM_CROSS_COMPILE FALSE)
endif()

# add host lib path
link_directories(
    ${LIB_PATH}
    /home/<USER>/project/share_libs/opencv
    /home/<USER>/project/Rv_split/lib/arm_jsoncpp/install/lib
)

# 为ARM交叉编译添加额外库路径
if(IS_ARM_CROSS_COMPILE)
    link_directories(
        # ${PROJECT_SOURCE_DIR}/aarch64-libs
        /usr/local/Ascend/ascend-toolkit/latest/aarch64-linux/lib64
        /usr/local/Ascend/ascend-toolkit/latest/aarch64-linux/lib
        /usr/local/Ascend/ascend-toolkit/latest/aarch64-linux/runtime/lib64
        /usr/local/Ascend/ascend-toolkit/latest/aarch64-linux/fwkacllib/lib64
        /usr/aarch64-linux-gnu/lib
    )
    message(STATUS "Added ARM cross-compilation lib paths")
endif()

#查找库文件
find_library(ZLOG_LIBRARY NAMES libzlog.so.1.3 PATHS  ${PROJECT_SOURCE_DIR}/../tecu1000_0_libs/)

file(GLOB Source_Files ${PROJECT_SOURCE_DIR}/Src/*.cpp)
file(GLOB Calib_SRC ${PROJECT_SOURCE_DIR}/Calib/*.cpp)
file(GLOB Config_SRC ${PROJECT_SOURCE_DIR}/Config/*.cpp)
file(GLOB DataSave_SRC ${PROJECT_SOURCE_DIR}/DataSave/*.cpp)
file(GLOB KalmanFilter_SRC ${PROJECT_SOURCE_DIR}/KalmanFilter/*.cpp)
file(GLOB TracksSplicing_SRC ${PROJECT_SOURCE_DIR}/TracksSplicing/*.cpp)
file(GLOB LaneSmoothing_SRC ${PROJECT_SOURCE_DIR}/LaneSmoothing/*.cpp)
file(GLOB CollisionDetection_SRC ${PROJECT_SOURCE_DIR}/CollisionDetection/*.cpp)
file(GLOB JSON_SRC ${PROJECT_SOURCE_DIR}/jsoncpp/*.cpp)

add_library(tecu_1000_0 SHARED ${Source_Files} ${Calib_SRC} ${Config_SRC}
        ${DataSave_SRC} ${KalmanFilter_SRC} ${TracksSplicing_SRC} ${LaneSmoothing_SRC} ${CollisionDetection_SRC} ${JSON_SRC})

#链接库到文件
target_link_libraries(tecu_1000_0 ${ZLOG_LIBRARY})

if (target STREQUAL "Simulator_Function")
    target_link_libraries(tecu_1000_0 funcsim
    )
else ()
    if (${CMAKE_HOST_SYSTEM_NAME} MATCHES "Windows")
        target_link_libraries(tecu_1000_0
            libascendcl
                    )
    else ()
        target_link_libraries(tecu_1000_0 stdc++ opencv_world
            )
    endif ()
endif ()
