#pragma once
#include <stddef.h>

#if 1

#define ZLOGGER_HEADER_DEFINE(A) \
    static const char *ZLOGGER_HEADER = A;\
    static void *ZLOGGER_CAT = 0;


#define ZERROR(...) \
    novasky_zlog(ZLOGGER_HEADER, &ZLOGGER_CAT,\
    __FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, \
    __LINE__, 100, __VA_ARGS__)


#define ZWARN(...) \
    novasky_zlog(ZLOGGER_HEADER, &ZLOGGER_CAT,\
    __FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, \
    __LINE__, 80, __VA_ARGS__)


#define ZINFO(...) \
    novasky_zlog(ZLOGGER_HEADER, &ZLOGGER_CAT,\
    __FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, \
    __LINE__, 40, __VA_ARGS__)


#define ZDEBUG(...) \
    novasky_zlog(ZLOGGER_HEADER, &ZLOGGER_CAT,\
    __FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, \
    __LINE__, 20, __VA_ARGS__)




#define HZERROR(buf, buf_len) \
    novasky_hzlog(ZLOGGER_HEADER, &ZLOGGER_CAT,\
    __FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, \
    __LINE__, 100, buf, buf_len)


#define HZWARN(buf, buf_len) \
    novasky_hzlog(ZLOGGER_HEADER, &ZLOGGER_CAT,\
    __FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, \
    __LINE__, 80, buf, buf_len)


#define HZINFO(buf, buf_len) \
    novasky_hzlog(ZLOGGER_HEADER, &ZLOGGER_CAT,\
    __FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, \
    __LINE__, 40, buf, buf_len)


#define HZDEBUG(buf, buf_len) \
    novasky_hzlog(ZLOGGER_HEADER, &ZLOGGER_CAT,\
    __FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, \
    __LINE__, 20, buf, buf_len)

#else
#define ZLOGGER_HEADER_DEFINE(A)
#define ZDEBUG printf
#define ZINFO printf
#define ZWARN printf
#define ZERROR printf
#endif


#ifdef __cplusplus
extern "C" {
#endif

void novasky_zlog(const char *log_header, void **p_cate, 
                  const char *file, size_t filelen, const char *func, size_t funclen,
                  long line, int level, const char *format, ...);

void novasky_hzlog(const char *log_header, void **p_cate, 
                   const char *file, size_t filelen, const char *func, size_t funclen,
                   long line, int level, const void *buf, size_t buflen);

int novasky_zlog_init(const char *conf_path);
void novasky_zlog_pure(void *p_category,
        const void *file, size_t filelen, const void *func, size_t funclen,
        long line, const int level,
        const void *str_pure, size_t str_pure_len);
void *novasky_zlog_get_category(const char *log_header);
int novasky_zlog_check(void *p_category, int level);

#ifdef __cplusplus
}
#endif









