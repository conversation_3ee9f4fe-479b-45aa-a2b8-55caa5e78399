/**
  ******************************************************************************
  * @file           : utils.h
  * <AUTHOR> Administrator
  * @brief          : None
  * @attention      : None
  * @date           : 2024/12/13
  ******************************************************************************
  */
#include "algorithm_header.h"
#include "json.h"
#include <vector>
#include <cstdint>  // 添加这个头文件以支持int32_t类型
#include <string>


/********************************************************
	 * Description          			 设备信息集合
	 * device_cnt                        设备数量集合
	 * device_list                       设备信息列表
	********************************************************/
typedef struct
{
    unsigned int	 device_cnt;                             //设备的数量
    GroupInfo       device_list[12];			 //设备编号列表
}DeviceList;

//读取道路配置信息
int read_road_lanes_config(std::string lane_config_path,
                           std::vector<RoadConfigAlg> &road_config_vec);

//打印道路配置信息
void print_road_config(const std::vector<RoadConfigAlg> road_config);



int read_devices_config(std::string device_file_path,
                        std::vector<RoadConfigAlg> &road_config_vec);


int read_device_to_list(std::string device_file_path,
                        DeviceList &device_list);


//读取仿真的输入数据时间戳到容器
int read_input_timems_vec(std::string input_data_timems_path,
                          std::vector<long long> &input_data_timems_vec,
                          int max_number);



int read_devices_config( DeviceList device_list,
                        std::vector<RoadConfigAlg> &road_config_vec);