//
// Created by Administrator on 2024/12/13.
//
#include <vector>
#include <cstdint>
#include <string>
#include "utils_debug.h"
#include "zlogger.h"
#include <fstream>
#include <iostream>

ZLOGGER_HEADER_DEFINE("ALG_ROAD_CONFIG_TEST")


/*
 * @Name: load_road_lanes_config
 * @Description: 道路配置信息加载
 *
 * @Input
 * lane_config_path: 道路配置文件路径
 * road_config_alg: 道路配置信息容器
 *
 * @Output
 * 0: 加载成功
 * -1: 加载失败
 *
 * @Edit History
 * Date: 2024/12/13
 * Time: 13:44
 * Author: WangXing
 * Content: Create
*/
int read_road_lanes_config(std::string lane_config_path,
                           std::vector<RoadConfigAlg> &road_config_vec)
{
    std::ifstream ifs(lane_config_path, std::ios::binary);
    // 检查文件是否成功打开
    if (!ifs.is_open())
    {
        ZERROR("lane_config_file Failed to open, lane_config_path:%s \n", lane_config_path.c_str());
        return -1; // 返回错误代码
    }else{
        ZINFO("lane_config_file SUCCESS to open, lane_config_path:%s \n", lane_config_path.c_str());
    }
    Json::Reader reader;
    Json::Value root;
    Json::Value road_config_arr;
    Json::Value lane_array;
    Json::Value coordinate_array;

    LaneAlg lane;
    CoordinateAlg coordinate;
    RoadConfigAlg road_config;

    ZINFO("lane_config_file SUCCESS to open, lane_config_path:%s \n", lane_config_path.c_str());

    if (reader.parse(ifs, root, false)) {

        if (root["road_config"].isNull()) {
            ZERROR("FAIL: camera calibration json is null\n");
            ifs.close();
            return -1;
        }

        if (root["road_config"].isArray()) {
            road_config_arr = root["road_config"];

            for (auto & i : road_config_arr)
            {
                if (i["road_id"].isString())
                {
                    road_config.road_id = i["road_id"].asString();
                    ZINFO("road_id:%s \n", road_config.road_id.c_str());
                }
                else
                {
                    ZERROR("FAIL: road config road_id json type error\n");
                    ifs.close();
                    return -1;
                }

                if (i["road_name"].isString())
                {
                    road_config.road_name = i["road_name"].asString();
                }
                else
                {
                    ZERROR("FAIL: road config road_name json type error\n");
                    ifs.close();
                    return -1;
                }

                if (i["road_width"].isDouble())
                {
                    road_config.road_width = i["road_width"].asDouble();
                }
                else
                {
                    ZERROR("FAIL: road config road_width json type error\n");
                    ifs.close();
                    return -1;
                }

                if (i["road_lane_width"].isDouble())
                {
                    road_config.road_lane_width = i["road_lane_width"].asDouble();
                }
                else
                {
                    ZERROR("FAIL: road config road_lane_width json type error\n");
                    ifs.close();
                    return -1;
                }

                coordinate_array.resize(0);
                road_config.coordinate_vec.resize(0);
                if (i["coordinate"].isArray())
                {
                    coordinate_array = i["coordinate"];

                    for (auto & k : coordinate_array)
                    {
                        if (k["longitude"].isDouble())
                        {
                            coordinate.longitude = k["longitude"].asDouble();
                        }
                        else
                        {
                            ZERROR("FAIL: road config coordinate longitude json type error\n");
                            ifs.close();
                            return -1;
                        }

                        if (k["latitude"].isDouble())
                        {
                            coordinate.latitude = k["latitude"].asDouble();
                        }
                        else
                        {
                            ZERROR("FAIL: road config coordinate coordinate latitude json type error\n");
                            ifs.close();
                            return -1;
                        }

                        road_config.coordinate_vec.push_back(coordinate);
                    }
                }
                lane_array.resize(0);
                road_config.lane_vec.resize(0);
                if (i["lane"].isArray())
                {
                    lane_array = i["lane"];
                    for (auto & j : lane_array)
                    {
                        if (j["lane_id"].isString())
                        {
                            lane.lane_id = j["lane_id"].asString();
                        }
                        else
                        {
                            ZERROR("FAIL: road config lane  lane_id json type error\n");
                            ifs.close();
                            return -1;
                        }

                        if (j["lane_type"].isUInt())
                        {
                            lane.lane_type = j["lane_type"].asUInt();
                        }
                        else
                        {
                            ZERROR("FAIL: road config lane lane_type json type error\n");
                            ifs.close();
                            return -1;
                        }
                        if (j["lane_index"].isUInt())
                        {
                            lane.lane_index = j["lane_index"].asUInt();
                        }
                        else
                        {
                            ZERROR("FAIL: road config lane lane_index json type error\n");
                            ifs.close();
                            return -1;
                        }

                        if (j["direction"].isUInt())
                        {
                            lane.direction = j["direction"].asUInt();
                        }
                        else
                        {
                            ZERROR("FAIL: road config lane direction json type error\n");
                            ifs.close();
                            return -1;
                        }

                        if (j["trun_type"].isUInt())
                        {
                            lane.trun_type = j["trun_type"].asUInt();
                        }
                        else
                        {
                            ZERROR("FAIL: road config lane  trun_type json type error\n");
                            ifs.close();
                            return -1;
                        }

                        coordinate_array.resize(0);
                        lane.coordinate.resize(0);
                        if (j["coordinate"].isArray())
                        {
                            coordinate_array = j["coordinate"];
                            for (auto & k : coordinate_array)
                            {
                                if (k["longitude"].isDouble())
                                {
                                    coordinate.longitude = k["longitude"].asDouble();
                                }
                                else
                                {
                                    ZERROR("FAIL: road config lane coordinate longitude json type error\n");
                                    ifs.close();
                                    return -1;
                                }

                                if (k["latitude"].isDouble())
                                {
                                    coordinate.latitude = k["latitude"].asDouble();
                                }
                                else
                                {
                                    ZERROR("FAIL: road config lane coordinate latitude json type error\n");
                                    ifs.close();
                                    return -1;
                                }

                                lane.coordinate.push_back(coordinate);
                            }
                        }

                        road_config.lane_vec.push_back(lane);
                    }

                }
                else
                {
                    ZERROR("FAIL: road config lane json type error\n");
                    ifs.close();
                    return -1;
                }

                road_config_vec.push_back(road_config);
            }
        }
        else {
            ZERROR("FAIL: road_config json is error\n");
            ifs.close();
            return -1;
        }
    }
    else {
        ZERROR("FAIL: parse json is error!\n");
        ifs.close();
        return -1;
    }
    ifs.close();
    ZINFO("lane_config_file SUCCESS to close, lane_config_path:%s \n", lane_config_path.c_str());
    return 0;
}



int read_devices_config(std::string device_file_path,
                        std::vector<RoadConfigAlg> &road_config_vec)
{
    DeviceList device_list;
    FILE* devicelist_file = fopen(device_file_path.c_str(), "rb");
    if(devicelist_file == nullptr){
        ZERROR("device_file_path:%s open failed! \n", device_file_path.c_str());
        return -1;
    }else{
        ZINFO("device_file_path:%s open success! \n", device_file_path.c_str());
    }
    size_t device_list_size = fread(&device_list, sizeof(DeviceList), 1, devicelist_file);
    ZDEBUG("device_list cnt:%d \n", device_list.device_cnt);
    fclose(devicelist_file);

    std::vector<GroupInfo>    group_vec;
    for(size_t dl = 0; dl < device_list.device_cnt; dl++)
    {
        group_vec.push_back(device_list.device_list[dl]);
    }

    for(size_t rcv = 0; rcv < road_config_vec.size(); rcv++)
    {
        road_config_vec[rcv].grp_info_vec = group_vec;
    }
    return 0;
}


int read_devices_config( DeviceList device_list,
                        std::vector<RoadConfigAlg> &road_config_vec)
{
   
    ZDEBUG("device_list cnt:%d \n", device_list.device_cnt);

    std::vector<GroupInfo>    group_vec;
    for(size_t dl = 0; dl < device_list.device_cnt; dl++)
    {
        group_vec.push_back(device_list.device_list[dl]);
    }

    for(size_t rcv = 0; rcv < road_config_vec.size(); rcv++)
    {
        road_config_vec[rcv].grp_info_vec = group_vec;
    }
    return 0;
}


/*
 * @Name: road_config
 * @Description: 打印车道配置信息
 *
 * @Input
 * road_config: 车道配置信息
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/12/5
 * Time: 14:59
 * Author: WangXing
 * Content: Create
*/
void print_road_config(const std::vector<RoadConfigAlg> road_config_vec)
{
    ZINFO("road_config_vec size:%d \n", road_config_vec.size());
    for(size_t rcv = 0; rcv < road_config_vec.size(); rcv++)
    {
        RoadConfigAlg road_config = road_config_vec[rcv];
        ZINFO("road_config th:%d/%d road_id:%s road_name:%s road_lane_width:%.2f road_width:%.2f \n",
              rcv, road_config_vec.size(),
              road_config.road_id.c_str(),
              road_config.road_name.c_str(),
              road_config.road_lane_width,
              road_config.road_width);
        ZINFO("road_config coordinate_vec size:%d \n",
              road_config.coordinate_vec.size());
        for(size_t cv = 0; cv < road_config.coordinate_vec.size(); cv++)
        {
            ZINFO("th:%d/%d latitude:%.6f longitude:%.6f \n",
                  cv, road_config.coordinate_vec.size(),
                  road_config.coordinate_vec[cv].latitude,
                  road_config.coordinate_vec[cv].longitude);
        }
        ZINFO("road_config lane_vec:%d\n", road_config.lane_vec.size());
        for(size_t lv = 0; lv < road_config.lane_vec.size(); lv++)
        {
            ZINFO("th:%d/%d lane_line coordinate_vec size:%d \n",
                  lv,
                  road_config.lane_vec.size(),
                  road_config.lane_vec[lv].coordinate.size());
            for(size_t lvc = 0; lvc < road_config.lane_vec[lv].coordinate.size(); lvc++)
            {
                ZINFO("th:%d/%d lane_index:%d latitude:%.6f longitude:%.6f \n",
                      lvc, road_config.lane_vec[lv].coordinate.size(),
                      road_config.lane_vec[lv].lane_index,
                      road_config.lane_vec[lv].coordinate[lvc].latitude,
                      road_config.lane_vec[lv].coordinate[lvc].longitude);
            }
        }
        ZINFO("road_config grp_info_vec:%d \n", road_config.grp_info_vec.size());
        for(size_t dv = 0; dv < road_config.grp_info_vec.size(); dv++)
        {
            ZINFO("th:%d/%d grp_id:%d grp_enable:%d grp_direction:%.6f grp_latitude:%.6f grp_longitude:%6f x:%.2f y:%.2f\n",
                  dv, road_config.grp_info_vec.size(),
                  road_config.grp_info_vec[dv].grp_id,
                  road_config.grp_info_vec[dv].grp_enable,
                  road_config.grp_info_vec[dv].grp_direction,
                  road_config.grp_info_vec[dv].grp_latitude,
                  road_config.grp_info_vec[dv].grp_longitude,
                  road_config.grp_info_vec[dv].x,
                  road_config.grp_info_vec[dv].y);
        }
        ZINFO("load_line_size_end\n");
        ZINFO("\n \n");
    }
}


/*
 * @Name: read_device_to_list
 * @Description: 读取device到数组中
 *
 * @Input
 * device_file_path: device_list 的路径
 * device_list: 设备的信息数组
 *
 * @Output
 * 0: 成功； -1:失败
 *
 * @Edit History
 * Date: 2024/12/15
 * Time: 16:50
 * Author: WangXing
 * Content: Create
*/
int read_device_to_list(std::string device_file_path,
                        DeviceList &device_list)
{
    FILE* devicelist_file = fopen(device_file_path.c_str(), "rb");
    size_t device_list_size = fread(&device_list, sizeof(DeviceList), 1, devicelist_file);
    ZDEBUG("device_list cnt:%d \n", device_list.device_cnt);
    fclose(devicelist_file);

    return 0;
}



/*
 * @Name:
 * @Description:
 *
 * @Input
 * input_data_timems_path: 输入数据时间戳的文件路径
 * input_data_timems_vec: 输入数据时间戳的容器
 * max_number: 最大的容器数量
 *
 * @Output
 * 0: 成功； -1:失败
 *
 * @Edit History
 * Date: 2024/12/15
 * Time: 16:56
 * Author: WangXing
 * Content: Create
*/
int read_input_timems_vec(std::string input_data_timems_path,
                          std::vector<long long> &input_data_timems_vec,
                          int max_number)
{
    // 打开文件用于读取
    FILE* input_data_timems_file = fopen(input_data_timems_path.c_str(), "r");
    if (nullptr == input_data_timems_file)
    {
        return -1;
    }
    while (1)
    {
        long long time_ms;
        int ret_timems = fscanf(input_data_timems_file, "%lld", &time_ms);
        if (ret_timems < 0)
        {
            break;
        }
        input_data_timems_vec.push_back(time_ms);
        static int count = 0;
        count++;
        if (count > max_number)
        {
            break;
        }
    }
    fclose(input_data_timems_file);
}