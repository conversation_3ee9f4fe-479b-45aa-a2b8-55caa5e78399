#include "zlogger.h"
#include <stdio.h>
#include <stdarg.h>
#include <string.h>
#include <time.h>
#include <sys/time.h>

// 全局日志文件指针
static FILE *log_file = NULL;
// 日志系统初始化函数
int novasky_zlog_init(const char *conf_path) {
    // 这里可以实现日志系统的初始化逻辑
    // 例如读取配置文件、初始化日志缓冲区等
    if (!conf_path) {
        return -1;
    }

    // 打开日志文件 覆盖
    log_file = fopen("algorithm/log/tecu_1000_0.log", "a");
    if (!log_file) {
        fprintf(stderr, "无法打开日志文件 log.txt\n");
        return -1;
    }

    // 返回0表示初始化成功
    return 0;
}

// 获取日志类别
void *novasky_zlog_get_category(const char *log_header) {
    // 根据日志头获取对应的日志类别
    // 实际实现可能涉及到哈希表查找等
    if (!log_header) {
        return NULL;
    }

    // 这里简单返回非NULL值表示成功
    return (void*)log_header;
}

// 检查日志级别是否需要记录
int novasky_zlog_check(void *p_category, int level) {
    // 检查给定的日志类别和级别是否需要记录
    // 例如，可以根据配置决定是否记录DEBUG级别的日志
    if (!p_category) {
        return 0;
    }

    // 这里简单实现：只记录级别大于等于20的日志
    return level >= 20;
}

// 格式化日志记录函数
void novasky_zlog(const char *log_header, void **p_cate,
                  const char *file, size_t filelen, const char *func, size_t funclen,
                  long line, int level, const char *format, ...) {
    // 检查日志类别是否已初始化
    if (*p_cate == 0) {
        *p_cate = novasky_zlog_get_category(log_header);
    }

    // 检查是否需要记录此级别的日志
    if (!novasky_zlog_check(*p_cate, level)) {
        return;
    }

    // 获取日志级别字符串
    const char* level_str = "UNKNOWN";
    if (level == 100) {
        level_str = "ERROR";
    } else if (level == 80) {
        level_str = "WARN";
    } else if (level == 40) {
        level_str = "INFO";
    } else if (level == 20) {
        level_str = "DEBUG";
    }

    // 获取当前时间
    struct timeval tv;
    gettimeofday(&tv, NULL);

    // 转换为本地时间
    struct tm *tm_info = localtime(&tv.tv_sec);

    // 格式化时间戳
    char timestamp[32];
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", tm_info);

    // 格式化日志消息
    va_list args, args_copy;
    va_start(args, format);
    va_copy(args_copy, args);

    // 构建日志前缀，按照要求的格式：时间戳 模块名 日志级别
    char prefix[256];
    snprintf(prefix, sizeof(prefix), "%s.%03ld %s %s    ",
             timestamp, tv.tv_usec / 1000, log_header, level_str);

    // 输出日志前缀到stderr
    fprintf(stderr, "%s", prefix);

    // 输出格式化的日志消息到stderr
    vfprintf(stderr, format, args);

    // 如果日志文件已打开，也输出到文件
    if (log_file) {
        fprintf(log_file, "%s", prefix);
        vfprintf(log_file, format, args_copy);
        fflush(log_file);  // 确保立即写入文件
    }

    va_end(args_copy);
    va_end(args);
}

// 二进制数据日志记录函数
void novasky_hzlog(const char *log_header, void **p_cate,
                   const char *file, size_t filelen, const char *func, size_t funclen,
                   long line, int level, const void *buf, size_t buflen) {
    // 检查日志类别是否已初始化
    if (*p_cate == 0) {
        *p_cate = novasky_zlog_get_category(log_header);
    }

    // 检查是否需要记录此级别的日志
    if (!novasky_zlog_check(*p_cate, level)) {
        return;
    }

    // 获取日志级别字符串
    const char* level_str = "UNKNOWN";
    if (level == 100) {
        level_str = "ERROR";
    } else if (level == 80) {
        level_str = "WARN";
    } else if (level == 40) {
        level_str = "INFO";
    } else if (level == 20) {
        level_str = "DEBUG";
    }

    // 获取当前时间
    struct timeval tv;
    gettimeofday(&tv, NULL);

    // 转换为本地时间
    struct tm *tm_info = localtime(&tv.tv_sec);

    // 格式化时间戳
    char timestamp[32];
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", tm_info);

    // 构建日志前缀，按照要求的格式：时间戳 模块名 日志级别
    char prefix[256];
    snprintf(prefix, sizeof(prefix), "%s.%03ld %s %s    Binary data (%zu bytes): ",
             timestamp, tv.tv_usec / 1000, log_header, level_str, buflen);

    // 输出日志前缀到stderr
    fprintf(stderr, "%s", prefix);

    // 输出二进制数据的十六进制表示到stderr
    const unsigned char *data = (const unsigned char *)buf;
    for (size_t i = 0; i < buflen && i < 32; i++) {
        fprintf(stderr, "%02x ", data[i]);
        if ((i + 1) % 16 == 0 && i < buflen - 1) {
            fprintf(stderr, " ");
        }
    }

    if (buflen > 32) {
        fprintf(stderr, "... (more data)");
    }

    // 如果日志文件已打开，也输出到文件
    if (log_file) {
        fprintf(log_file, "%s", prefix);

        for (size_t i = 0; i < buflen && i < 32; i++) {
            fprintf(log_file, "%02x ", data[i]);
            if ((i + 1) % 16 == 0 && i < buflen - 1) {
                fprintf(log_file, " ");
            }
        }

        if (buflen > 32) {
            fprintf(log_file, "... (more data)");
        }

        fflush(log_file);  // 确保立即写入文件
    }
}

// 纯文本日志记录函数
void novasky_zlog_pure(void *p_category,
        const void *file, size_t filelen, const void *func, size_t funclen,
        long line, const int level,
        const void *str_pure, size_t str_pure_len) {
    // 检查是否需要记录此级别的日志
    if (!novasky_zlog_check(p_category, level)) {
        return;
    }

    // 获取日志级别字符串
    const char* level_str = "UNKNOWN";
    if (level == 100) {
        level_str = "ERROR";
    } else if (level == 80) {
        level_str = "WARN";
    } else if (level == 40) {
        level_str = "INFO";
    } else if (level == 20) {
        level_str = "DEBUG";
    }

    // 获取当前时间
    struct timeval tv;
    gettimeofday(&tv, NULL);

    // 转换为本地时间
    struct tm *tm_info = localtime(&tv.tv_sec);

    // 格式化时间戳
    char timestamp[32];
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", tm_info);

    // 构建日志前缀，按照要求的格式：时间戳 模块名 日志级别
    // 注意：纯文本日志没有模块名，使用 "PURE_TEXT" 作为替代
    char prefix[256];
    snprintf(prefix, sizeof(prefix), "%s.%03ld PURE_TEXT %s    ",
             timestamp, tv.tv_usec / 1000, level_str);

    // 输出日志前缀到stderr
    fprintf(stderr, "%s", prefix);

    // 输出纯文本日志到stderr
    fprintf(stderr, "%.*s", (int)str_pure_len, (const char *)str_pure);

    // 如果日志文件已打开，也输出到文件
    if (log_file) {
        fprintf(log_file, "%s", prefix);
        fprintf(log_file, "%.*s", (int)str_pure_len, (const char *)str_pure);
        fflush(log_file);  // 确保立即写入文件
    }
}
