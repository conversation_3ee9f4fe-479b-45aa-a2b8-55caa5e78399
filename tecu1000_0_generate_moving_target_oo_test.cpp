//
// Created by Administrator on 2024/7/15.
//
#include <iostream>
#include <string>
#include <thread>
#include "zlogger.h"
#include "algorithm_header.h"
#include "utils_debug.h"
#include <cstdio>
#include <fstream>
#include <vector>
#include <cmath>
#include <algorithm>

ZLOGGER_HEADER_DEFINE("ALG_LANES_TEST")

/* 事件类型 */
typedef struct
{
    unsigned int trigger_count;         //当前目标触发的事件计数
    bool b_illegal_change;              //非法变道
    bool b_over_speed;                  //超速
    bool b_below_speed;                 //低速
    bool b_retrograde;                  //逆行
    bool b_illegal_parking;             //非法停车
    bool b_intrusion;                   //入侵
    bool b_accident;                    //事故
    bool b_drag_racing;                 //飙车
    bool b_dist_no_maintain;            //未保持车距
    bool b_abnormal_parking;            //异常停车
    bool b_occ_emer;                    //占用应急车道
    bool b_snake_change;                //蛇形变道
} EventState;

typedef struct
{
    char device_ip[16];                          //设备IP
	unsigned int id;  		                                //目标id     (1 ~ MAX_OBJECT_NUM)
	unsigned int source_type;                               //目标的融合类型
	TargetType target_type;		                            //目标类型
    float      tt_score;                                    //目标分数

	float x; 			                                    //x轴坐标
	float y; 			                                    //y轴坐标
	float angle;                                            //目标图标角度 0-360°
	float speed;			                                //速度 km/h
	double longitude;                                       //目标经度 小数点精确到8位
	double latitude;                                        //目标维度 小数点精确到8位

    char number_plate[64];                      //车牌文字
    float np_score;                                         //车牌文字分数
    PlateColor plate_color;                                 //车牌颜色
    float      pc_score;                                    //车牌颜色分数
    VehicleType vehicle_type;                               //车辆类型
    float       vt_score;                                   //车辆类型分数
	VehicleColor vehicle_color;                             //车身颜色
    float       vc_score;                                   //车身颜色分数
    EventState event_state;                                 //目标的事件触发状态

	double direction;                                       //方向角 0-360° pengge edit
	double distance;                                        //距离  pengge edit

    float radar_coord_dist;                                 //目标到雷达坐标系原点的距离
    float radar_coord_angle;                                //目标和雷达坐标系原点的连线与雷达法线的夹角
    long long timestamp_ms;                                 //目标的时间戳
}   Target;

/* 拼接目标数据 */
typedef struct
{
    unsigned int splice_id;  		                        //拼接id的范围
    unsigned int device_id;                                 //设备的id
    long long timestamp_ms;                                 //设备的时间戳 主要用于传递给下一个盒子的数据
    float        splicing_x;                                //基准设备坐标系下的x轴坐标
    float        splicing_y;                                //基准设备坐标系下的y轴坐标
    Target       splice_target;                             //拼接后的目标信息
}OutputTargetOld;

int algorithm_fusion_output_test(AlgCallbackType callbackType, int channel_id, OutputTargets *output_targets,OutputTargets *output_targets_next,void *user_ptr)
{
    // if (callbackType == AlgCallbackType::CALLBACK_TYPE_TARGETS)
    // {
    //     static int callback_count = 0;
    //     callback_count++;

    //     ZINFO("回调函数被调用 - 次数: %d, 通道ID: %d, 目标数量: %u",
    //           callback_count, channel_id, output_targets->target_cnt);

    //     // 打印部分目标信息（最多5个）
    //     unsigned int print_count = std::min(output_targets->target_cnt, 5u);
    //     for (unsigned int i = 0; i < print_count; i++) {
    //         const OutputTarget& ot = output_targets->fusion_targets[i];
    //         const TecuTarget& target = ot.splice_target;
    //         ZINFO("目标[%d]: 拼接ID=%u, 设备ID=%u, 目标ID=%u, 类型=%d, 坐标=(%.2f,%.2f), 速度=%.2f",
    //               i, ot.splice_id, ot.grp_id, target.id,
    //               (int)target.target_type, target.x, target.y, target.speed);
    //     }
    // }
    return 0;
}

// 打印 MultiBoxTargets 结构体的所有属性(一行格式)
void print_box_targets(const MultiBoxTargets& box_targets) {
    printf("============ MultiBoxTargets 简洁信息 ============\n");

    // 打印 multi_device_targets 信息
    const MultiDeviceTargets& mdt = box_targets.multi_device_targets;
    printf("-- 设备数量: %u --\n", mdt.device_cnt);

    // 设备和目标信息表头
    printf("设备ID 设备类型 目标序号 目标ID 目标类型 坐标(x,y) 速度 角度 经纬度 车牌 车辆类型 车身颜色\n");

    // 打印每个设备的信息和目标
    for (unsigned int i = 0; i < mdt.device_cnt; i++) {
        const InputTargets& device = mdt.device_input[i];

        // 打印每个目标的信息(一行)
        for (unsigned int j = 0; j < device.target_cnt; j++) {
            const TecuTarget& target = device.targets.target_devices[j];
            printf("%u %d %u %u %d (%.2f,%.2f) %.2f %.2f (%.6f,%.6f) %s %d %d\n",
                device.grp_id,
                (int)device.grp_type,
                j,
                target.id,
                (int)target.target_type,
                target.x, target.y,
                target.speed,
                target.angle,
                target.longitude, target.latitude,
                target.number_plate,
                (int)target.vehicle_type,
                (int)target.vehicle_color
            );
        }
    }

    // 打印 last_output_target 信息
    const OutputTargets& lot = box_targets.last_output_target;
    printf("\n-- 融合目标数量: %u --\n", lot.target_cnt);

    // 融合目标表头
    printf("拼接ID 设备ID 拼接坐标(x,y) 目标ID 目标类型 原始坐标(x,y) 速度 角度 经纬度 车牌\n");

    // 打印每个融合目标的信息(一行)
    for (unsigned int i = 0; i < lot.target_cnt; i++) {
        const OutputTarget& ot = lot.fusion_targets[i];
        const TecuTarget& target = ot.splice_target;
        printf("%u %u (%.2f,%.2f) %u %d (%.2f,%.2f) %.2f %.2f (%.6f,%.6f) %s\n",
            ot.splice_id,
            ot.grp_id,
            ot.splicing_x, ot.splicing_y,
            target.id,
            (int)target.target_type,
            target.x, target.y,
            target.speed,
            target.angle,
            target.longitude, target.latitude,
            target.number_plate
        );
    }

    printf("===============================================\n");
}

// 旧版本target转换
void change_target2tecu_target(Target *target_tmp, TecuTarget *tecu_target, int target_cnt, unsigned int grp_id) {
    for(int i = 0; i < target_cnt; i++) {
        // 设置TecuTarget特有字段的默认值
        tecu_target[i].grp_id = grp_id;   // 使用传入的设备组ID
        tecu_target[i].stream_id = 0;     // 默认流ID

        // 复制共有字段
        tecu_target[i].id = target_tmp[i].id;
        tecu_target[i].source_type = target_tmp[i].source_type;
        tecu_target[i].target_type = target_tmp[i].target_type;
        tecu_target[i].tt_score = target_tmp[i].tt_score;

        tecu_target[i].x = target_tmp[i].x;
        tecu_target[i].y = target_tmp[i].y;
        tecu_target[i].angle = target_tmp[i].angle;
        tecu_target[i].speed = target_tmp[i].speed;
        tecu_target[i].longitude = target_tmp[i].longitude;
        tecu_target[i].latitude = target_tmp[i].latitude;

        strcpy(tecu_target[i].number_plate, target_tmp[i].number_plate);
        tecu_target[i].np_score = target_tmp[i].np_score;
        tecu_target[i].plate_color = target_tmp[i].plate_color;
        tecu_target[i].pc_score = target_tmp[i].pc_score;
        tecu_target[i].vehicle_type = target_tmp[i].vehicle_type;
        tecu_target[i].vt_score = target_tmp[i].vt_score;
        tecu_target[i].vehicle_color = target_tmp[i].vehicle_color;
        tecu_target[i].vc_score = target_tmp[i].vc_score;

        tecu_target[i].direction = target_tmp[i].direction;
        tecu_target[i].distance = target_tmp[i].distance;

        tecu_target[i].radar_coord_dist = target_tmp[i].radar_coord_dist;
        tecu_target[i].radar_coord_angle = target_tmp[i].radar_coord_angle;
        tecu_target[i].timestamp_ms = target_tmp[i].timestamp_ms;

        // 注意：Target中的event_state字段在TecuTarget中不存在，因此不复制
    }
}

// 打印Target结构体数组
void print_targets(Target* targets, int target_cnt) {
    printf("======== Target数组信息 (共%d个目标) ========\n", target_cnt);
    for (int i = 0; i < target_cnt; i++) {
        printf("目标[%d]: ID=%u, 类型=%d, 坐标=(%.2f,%.2f), 速度=%.2f, 角度=%.2f\n",
               i, targets[i].id, (int)targets[i].target_type,
               targets[i].x, targets[i].y,
               targets[i].speed, targets[i].angle);
    }
    printf("=======================================\n");
}

int main(int argc, char *argv[])
{
    novasky_zlog_init("");
    ZINFO("分体式雷视一体机测试  采用适配层");
    ZINFO("<<<<<<<<<<<<<<<<<<<<<-------------------------ROAD CONFIG TEST----------------------->>>>>>>>>>>>>>>>>>>>> \n");
    int ret = 0;
    char algor_version[128];
    ret = tecu_algorithm_init(algor_version);
    int create_channel_id = tecu_algorithm_open_channel(algorithm_fusion_output_test, nullptr);
    if (create_channel_id < 0)
    {
        ZERROR("algorithm_open_channel failure!\n");
    }
    else
    {
        ZINFO("algorithm_open_channel success, algor version:%s! \n", algor_version);
    }

    std::string input_data_file_path = "";
    std::string input_data_timems_path = "";
    std::string base_device_id_file_path = "";
    std::string devicelist_file_path = "";
    std::string devicemap_file_path = "";
    std::string data_dir_path = "";
    std::string input_data_timems = "";
    std::string lane_config_path = "tecu_r1000_config.json";
    if (argc != 8)
    {
        ZERROR("argv[0]:%s argv[1]:%s argv[2]:%s argc:%d \n", argv[0], argv[1], argv[2], argc);
        ZERROR("Usange: algorithm_testso.exe data_dir_path input_data_timems \n");
        ZERROR("Sample: algorithm_testso.exe E:\05_新疆比测\..\20240522\data\channel_id_0 2024_05_23_15_32_49 \n");
        ret = tecu_algorithm_close_channel(create_channel_id);
        ret = tecu_algorithm_deinit();
        return -1;
    }

    data_dir_path += argv[1];
    input_data_timems += argv[2];
    // 删除 "_box1"
    input_data_file_path = data_dir_path + R"(/)" + input_data_timems + ".dat";
    input_data_timems = input_data_timems.substr(0, input_data_timems.find("_box1"));
    input_data_timems = input_data_timems.substr(0, input_data_timems.find("_box2"));
    ZDEBUG("data_dir_path:%s input_data_timems:%s \n",
           data_dir_path.c_str(), input_data_timems.c_str());

    input_data_timems_path = data_dir_path + R"(/)" + input_data_timems + ".txt";
    base_device_id_file_path = data_dir_path + R"(/)" + "base_device_id.txt";
    devicelist_file_path = data_dir_path + R"(/)" + "devicelist.dat";
    devicemap_file_path = data_dir_path + R"(/)" + "devicemap.dat";

    ZDEBUG("input_data_file_path:%s \n", input_data_file_path.c_str());
    ZDEBUG("input_data_timems_path:%s \n", input_data_timems_path.c_str());
    ZDEBUG("base_device_id_file_path:%s \n", base_device_id_file_path.c_str());
    ZDEBUG("devicelist_file_path:%s \n", devicelist_file_path.c_str());
    ZDEBUG("devicemap_file_path:%s \n", devicemap_file_path.c_str());
    ZDEBUG("lane_config_path:%s \n", lane_config_path.c_str());

    std::vector<RoadConfigAlg> road_config_vec;
    ret = read_road_lanes_config(lane_config_path, road_config_vec);
    if(ret != 0){
        ZERROR("read_road_lanes_config failure! \n");
    }else{
        ZINFO("read_road_lanes_config success! \n");
    }
    ret = read_devices_config(devicelist_file_path, road_config_vec);
    if(ret != 0){
        ZERROR("read_devices_config failure! \n");
    }else{
        ZINFO("read_devices_config success! \n");
    }
    // print_road_config(road_config_vec);
    ret = tecu_algorithm_set_param(create_channel_id,
                              TecuAlgorithmSetType::TECU_ALG_TYPE_ROAD_CONFIG,
                              &road_config_vec,
                              road_config_vec.size());

    DeviceList device_list;
    ret = read_device_to_list(devicelist_file_path, device_list);
//     // 保留device_id为1 2 的设备
//     int device_id_list[2] = {3, 4};
//     for(int i = 0; i < device_list.device_cnt; i++){
//     if(std::find(device_id_list, device_id_list + 2, device_list.device_list[i].device_id) == device_id_list + 2){
//         device_list.device_list[i] = device_list.device_list[device_list.device_cnt - 1];
//         device_list.device_cnt--;
//         i--; // 回退索引以检查替换后的元素
//     }
// }
    // 打印device_list
    printf("device_list.device_cnt:%d\n", device_list.device_cnt);
    for(int i = 0; i < device_list.device_cnt; i++){
        printf("device_list.device_list[%d].grp_id:%d\n", i, device_list.device_list[i].grp_id);
    }
    ret = tecu_algorithm_set_param(create_channel_id,
                              TecuAlgorithmSetType::TECU_ALG_TYPE_BASE_GRP,
                              &(device_list.device_list),
                              device_list.device_cnt);
    if(ret != 0){
        ZERROR("tecu_algorithm_set_param failure! \n");
    }else{
        ZINFO("tecu_algorithm_set_param success! \n");
    }
    ret = tecu_algorithm_set_param(create_channel_id,
                              TecuAlgorithmSetType::TECU_ALG_TYPE_CALIB_HDM,
                              &(device_list.device_list),
                              device_list.device_cnt);
    if(ret != 0){
        ZERROR("tecu_algorithm_set_param failure! \n");
    }else{
        ZINFO("tecu_algorithm_set_param success! \n");
    }
    int edgebox_id = 1;
    ret = tecu_algorithm_set_param(create_channel_id,
                              TecuAlgorithmSetType::TECU_ALG_TYPE_EDGEBOX_DEVICE,
                              &edgebox_id,
                              0);
    if(ret != 0){
        ZERROR("tecu_algorithm_set_param failure! \n");
    }else{
        ZINFO("tecu_algorithm_set_param success! \n");
    }
    ret = tecu_algorithm_start(create_channel_id);

    std::vector<long long> input_data_timems_vec;

    std::vector<MultiDeviceTargets> targets_array;
    std::vector<long long> timestamps;

    // 打开数据文件
    FILE* data_file = fopen(input_data_file_path.c_str(), "rb");
    if (nullptr == data_file) {
        ZERROR("无法打开数据文件: %s\n", input_data_file_path.c_str());
        return -1;
    }

    // 打开时间戳文件
    FILE* time_file = fopen(input_data_timems_path.c_str(), "r");
    if (nullptr == time_file) {
        ZERROR("无法打开时间戳文件: %s\n", input_data_timems_path.c_str());
        fclose(data_file);
        return -1;
    }

    char time_buffer[128];
    size_t frame_count = 0;
    long long current_timestamp = 0;
    long long last_timestamp = 0;

    // 逐帧读取和处理
    auto last_process_time = std::chrono::steady_clock::now(); // 记录上次处理时间
    long long dt = 110;

    // 定义 MultiBoxTargets 结构体变量用于读取数据
    MultiBoxTargets box_targets;

    while (true) {
        printf("\n\n");
        // 读取时间戳
        if (fgets(time_buffer, sizeof(time_buffer), time_file) == nullptr) {
            break;  // 时间戳文件读取完毕
        }
        current_timestamp = atoll(time_buffer);
        if(last_timestamp == 0){
            last_timestamp = current_timestamp;
        }
        dt = current_timestamp - last_timestamp;
        last_timestamp = current_timestamp;

        // 清零当前帧数据
        memset(&box_targets, 0, sizeof(MultiBoxTargets));
        // MultiDeviceTargets &box_targets.multi_device_targets = box_targets.multi_device_targets;

        // 读取设备数量
        if (fread(&box_targets.multi_device_targets.device_cnt, sizeof(unsigned int), 1, data_file) != 1) {
            break;  // 数据文件读取完毕
        }else{
            // printf("Fread current_frame.device_cnt:%d\n", box_targets.multi_device_targets.device_cnt);
        }

        // 验证设备数量
        if (box_targets.multi_device_targets.device_cnt > (DEV_GRP_NUM_MAX * 2)) {
            ZERROR("非法的设备数量: %u，帧数: %zu\n", box_targets.multi_device_targets.device_cnt, frame_count);
            break;
        }

        // 读取每个设备的数据
        bool read_error = false;
        for (unsigned int i = 0; i < box_targets.multi_device_targets.device_cnt; i++) {
            printf("current_frame.device_cnt:%d\n", box_targets.multi_device_targets.device_cnt);
            InputTargets &device = box_targets.multi_device_targets.device_input[i];

            // 读取设备ID和目标数量
            if (fread(&device.grp_id, sizeof(unsigned int), 1, data_file) != 1 ||
                fread(&device.grp_type, sizeof(unsigned int), 1, data_file) != 1 ||
                fread(&device.target_cnt, sizeof(unsigned int), 1, data_file) != 1 ||
                fread(&device.timestamp_ms, sizeof(long long), 1, data_file) != 1) {
                printf("read_error\n");
                read_error = true;
                break;
            }

            // 验证目标数量
            if (device.target_cnt > MAX_OBJECT_NUM) {
                ZERROR("非法的目标数量: %u，设备ID: %u，帧数: %zu\n",
                       device.target_cnt, device.grp_id, frame_count);
                read_error = true;
                break;
            }

            // 读取目标数据
            Target target_tmp[MAX_OBJECT_NUM];
            if (device.target_cnt > 0) {
                if (fread(target_tmp,
                         sizeof(Target),
                         device.target_cnt,
                         data_file) != device.target_cnt) {
                    read_error = true;
                    break;
                }
            }
            // 打印target_tmp
            // printf("device id: %d\n", device.grp_id);
            // print_targets(target_tmp, device.target_cnt);
            // 旧版本target转换
            change_target2tecu_target(target_tmp, device.targets.target_devices, device.target_cnt, device.grp_id);
        }

        // print_box_targets(box_targets);

        // printf("设备数量1: %u\n", box_targets.multi_device_targets.device_cnt);
        // for (unsigned int i = 0; i < box_targets.multi_device_targets.device_cnt; i++) {
        //     printf("设备ID: %u, 目标数量: %u\n",
        //           box_targets.multi_device_targets.device_input[i].grp_id,
        //           box_targets.multi_device_targets.device_input[i].target_cnt);
        // }

        OutputTargets output_targets_;
        {
            // 读取目标数量
            if (fread(&output_targets_.target_cnt, sizeof(unsigned int), 1, data_file) != 1)
            {
                read_error = true;
                break;
            }
            // 验证目标数量
            if (output_targets_.target_cnt > MAX_OBJECT_NUM * DEV_GRP_NUM_MAX)
            {
                ZERROR("非法的融合目标数量: %u，帧数: %zu\n",
                       output_targets_.target_cnt, frame_count);
                read_error = true;
                break;
            }
            for (unsigned int i = 0; i < output_targets_.target_cnt; i++)
            {
                Target target_tmp2[MAX_OBJECT_NUM];
                // 读取设备ID和目标数量
                if (fread(&output_targets_.fusion_targets[i].splice_id, sizeof(unsigned int), 1, data_file) != 1 ||
                    fread(&output_targets_.fusion_targets[i].grp_id, sizeof(unsigned int), 1, data_file) != 1 ||
                    fread(&output_targets_.fusion_targets[i].timestamp_ms, sizeof(long long), 1, data_file) != 1 ||
                    fread(&output_targets_.fusion_targets[i].splicing_x, sizeof(float), 1, data_file) != 1 ||
                    fread(&output_targets_.fusion_targets[i].splicing_y, sizeof(float), 1, data_file) != 1 ||
                    fread(&target_tmp2, sizeof(Target), 1, data_file) != 1)
                {
                    read_error = true;
                    break;
                }
                change_target2tecu_target(target_tmp2, &output_targets_.fusion_targets[i].splice_target,
                    1,
                    output_targets_.fusion_targets[i].grp_id);
            }
        }

        if (read_error) {
            ZERROR("读取第 %zu 帧数据失败\n", frame_count);
            break;
        }
        // 调试输出
        printf("设备数量: %u\n", box_targets.multi_device_targets.device_cnt);
        for (unsigned int i = 0; i < box_targets.multi_device_targets.device_cnt; i++) {
            printf("设备ID: %u, 目标数量: %u\n",
                  box_targets.multi_device_targets.device_input[i].grp_id,
                  box_targets.multi_device_targets.device_input[i].target_cnt);
        }
        printf("target_devices.id:");
        for(int lot = 0; lot < box_targets.multi_device_targets.device_cnt; lot++){
            for(int j = 0; j < box_targets.multi_device_targets.device_input[lot].target_cnt; j++){
                printf("%d(%d) ",
                        box_targets.multi_device_targets.device_input[lot].targets.target_devices[j].id, box_targets.multi_device_targets.device_input[lot].grp_id);
            }
        }
        printf("\n");
        printf("融合目标数量: %u\n", output_targets_.target_cnt);
        printf("last_output_target.target_cnt:");
        for(unsigned int j = 0; j < output_targets_.target_cnt; j++){
            printf("%d(%d) ",
                    output_targets_.fusion_targets[j].splice_id, output_targets_.fusion_targets[j].splice_target.id);
        }
        printf("\n");
        // print_box_targets(box_targets);
        // long long start_time = 1744599320000 + (8*60*60*1000) - (1*1*60*1000); // RTK时间
        // long long end_time = 1744599547000 + (8*60*60*1000) + (1*1*60*1000);
        // current_timestamp = current_timestamp - (8*60*60*1000);  // 设备时间
        // if(current_timestamp < start_time || current_timestamp > end_time){
        //     printf("时间戳: %lld 不在范围内\n", current_timestamp);
        // }else{
        //     ret = tecu_algorithm_target_input_multi_box(create_channel_id, &box_targets, current_timestamp);
        // }
        ret = tecu_algorithm_target_input_multi_box(create_channel_id, &box_targets, current_timestamp);
        if (ret != 0) {
            ZERROR("处理第 %zu 帧失败，错误码: %d\n", frame_count, ret);
        }

        frame_count++;
        // if (frame_count > 200) break;
        printf("\r处理进度: [%zu] 时间戳: %lld 系统时间: %lld \n",
       frame_count, current_timestamp, last_process_time);
    }

    fclose(data_file);
    fclose(time_file);

    ZINFO("总共处理 %zu 帧数据\n", frame_count);

    ret = tecu_algorithm_close_channel(create_channel_id);
    ret = tecu_algorithm_deinit();

    return ret;
}