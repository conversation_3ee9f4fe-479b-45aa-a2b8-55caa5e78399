//
// Created by wangxing on 2024/12/5.
//

#pragma once
#include "vehicleLaneSmoothheader.h"
#include "tracks_splicing_header.h"

namespace tecu_r1000_0_algorithm
{
    class VehicleLaneSmoothing
    {
    public:
        VehicleLaneSmoothing();
        ~VehicleLaneSmoothing();

        void lane_amend_targets(std::vector<SplicingTarget> &splicing_output_targets_vec);

        int lane_infos_update(std::vector<RoadConfigAlg> road_config);

        int get_lane_target_direction(CalibPoint target_point,
                                      double &target_lane_direction);

        std::vector<LaneInfo> get_lane_infos();

        /**
         * @brief 获取输入点所在车道网格的方向
         * @param point 输入点
         * @param direction 输出参数，存储找到的车道方向
         * @return 如果找到对应的车道网格返回true，否则返回false
         */
        bool get_lane_direction(const CalibPoint& point, double& direction);

    private:
        void road_config_lanes_sort(std::vector<RoadConfigAlg> &road_config);

        void road_config_lanes_sample(std::vector<RoadConfigAlg> &road_config_vec,
                                      int max_lane_number);

        static bool compare_lane_index(const LaneAlg &a, const LaneAlg &b);

        std::vector<LaneInfo> lane_infos; // 划分的车道道路矩阵网格信息

        std::vector<LaneTargetAmend> lane_amend_target_vec; // 车道修正的目标容器

        void lane_amend_target_vec_update(std::vector<SplicingTarget> splicing_output_targets_vec,
                                          std::vector<LaneTargetAmend> &lane_amend_target_vec_,
                                          long long path_max_duration);

        void lane_id_update(std::vector<LaneTargetAmend> &lane_amend_target_vec_);

        int get_lane_id(std::vector<LaneTargetPoint> target_amend_point_path,
                        long long min_change_lane_duration);

        LaneSmoothResult get_in_lane_res(std::vector<LaneTargetPoint> target_amend_point_path,
                                         long long out_lane_duration);

        void lane_id_assign(LaneTargetAmend &lane_target,
                            int detect_lane_id);

        int get_change_lane_frmae_count(std::vector<LaneTargetPoint> target_amend_point_path,
                                        int previous_lane_id,
                                        int current_lane_id);

        void get_lane_smooth_point(CalibPoint target_point,
                                   LaneTargetPoint &lane_smooh_target_point,
                                   std::vector<LaneTargetPoint> &lane_smooth_points);

        void lane_amend_target_point_update(std::vector<LaneTargetAmend> &lane_amend_target_vec_);

        void lane_amend_target_vec_delete(std::vector<LaneTargetAmend> &lane_amend_target_vec_);
        void update_splicing_output_targets_vec(
            std::vector<LaneTargetAmend> lane_amend_target_vec_,
            std::vector<SplicingTarget> &splicing_output_targets_vec_);

        int lane_infos_assign(std::vector<RoadConfigAlg> road_config_vec,
                              std::vector<LaneInfo> &lane_infos,
                              DeivceInfo the_first_device);

        void lane_infos_clear(); // 道路配置清空

        void print_road_config(const std::vector<RoadConfigAlg> road_config);

        void print_lane_infos(const std::vector<LaneInfo> lane_infos);

        int get_first_device(std::vector<DeivceInfo> device_vec, DeivceInfo &first_device);

        void lonlat_mercator(double lng, double lat, float &x, float &y);

        double calculate_angle(const CalibPoint &A, const CalibPoint &B);

        int find_vertical_intersection(const CalibPoint point,
                                       const std::vector<CalibPoint> line_segment,
                                       CalibPoint &intersection_point);
    };
}
