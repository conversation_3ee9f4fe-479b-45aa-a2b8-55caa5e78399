//
// Created by <PERSON><PERSON><PERSON> on 2024/12/5.
//
#include "vehicleLaneSmoothing.h"
#include "zlogger.h"
#include "limits.h"

using namespace tecu_r1000_0_algorithm;

ZLOGGER_HEADER_DEFINE("ALG_TRACK_LANESMOOTHING")

VehicleLaneSmoothing::VehicleLaneSmoothing()
{
    lane_infos_clear();
    ZINFO("lane_infos_clear! lane_infos.size:%d lane_infos.capacity:%d\n",
          lane_infos.size(), lane_infos.capacity());
    ZINFO("CREATE VehicleLaneSmoothing, lane_infos_clear! lane_infos.size:%d lane_infos.capacity:%d\n",
          lane_infos.size(), lane_infos.capacity());
}

VehicleLaneSmoothing::~VehicleLaneSmoothing()
{
    lane_infos_clear();
    ZINFO("DELETE VehicleLaneSmoothing, lane_infos_clear! lane_infos.size:%d lane_infos.capacity:%d\n",
          lane_infos.size(), lane_infos.capacity());
}

void VehicleLaneSmoothing::lane_infos_clear()
{
    for (size_t li = 0; li < lane_infos.size(); li++)
    {
        for (size_t lic = 0; lic < lane_infos[li].lane_areas.size(); lic++)
        {
            std::vector<cv::Point2f>().swap(lane_infos[li].lane_areas[lic].lane_area);
        }
        std::vector<LaneArea>().swap(lane_infos[li].lane_areas);
    }
    std::vector<LaneInfo>().swap(lane_infos);
}

///*
// * @Name: lane_amend_target
// * @Description: 对车道内的目标进行车道级的修正，轨迹缓存2000ms；
// * 车道保持: 当目标连续500ms在某个车道时，则使用该车道的信息，并且划分目标的车道号
// * 变道:    当目标连续500ms，车道号发送改变，则触发变道逻辑；
// *              首先从历史轨迹当查询距离上个车道修正的偏离中位数；
// *              找到偏离中位数后, 当目标小于中位数时则为变道开始时刻；当前时刻为变道结束的时刻；计算得到变道时长
// *              当没有找到中位数后, 则变道时长为轨迹的时长；
// *         变道时长已知时， 则下一帧的目标轨迹点为两个车道中心点的加权；
// *         当变道结束后，开始进行下一个变道； 变道结束前，则不进行下一次的变道；
// *         当目标连续2000ms不在车道内时，则更新状态为删除；
// *
// * @Input
// * splicing_output_targets_vec: 输出目标的信息
//
// * @Output
// *
// *
// *
// *
// * @Edit History
// * Date: 2024/12/15
// * Time: 16:05
// * Author: WangXing
// * Content: Create
//*/
void VehicleLaneSmoothing::lane_amend_targets(std::vector<SplicingTarget> &splicing_output_targets_vec)
{
    // ZDEBUG("lane_amend_targets, splicing_output_targets_vec.size:%d \n", splicing_output_targets_vec.size());

    // // 打印splicing_output_targets_vec
    // for (size_t stv = 0; stv < splicing_output_targets_vec.size(); stv++) {
    //     if(splicing_output_targets_vec[stv].target_output.fusion_id == 0){
    //         continue;
    //     }
    //     ZDEBUG("splicing_output_targets_vec-input fusion_id:%d x:%.4f y:%.4f \n", 
    //     splicing_output_targets_vec[stv].target_output.fusion_id,
    //     splicing_output_targets_vec[stv].target_kalman->kf_update_res[0], 
    //     splicing_output_targets_vec[stv].target_kalman->kf_update_res[1]);
    // }

    // lane_amend_target_vec容器的更新与删除
    lane_amend_target_vec_update(splicing_output_targets_vec,
                                 this->lane_amend_target_vec,
                                 LANE_AMEND_PATH_MAX_DURATION);

    // 目标车道号的确定
    lane_id_update(this->lane_amend_target_vec);

    // 修正目标信息
    lane_amend_target_point_update(this->lane_amend_target_vec);

    // // 删除车道外的车辆
    lane_amend_target_vec_delete(lane_amend_target_vec);
    
    // // 更新splicing_output_targets_vec
    update_splicing_output_targets_vec(lane_amend_target_vec, splicing_output_targets_vec);

    // // 打印splicing_output_targets_vec
    // for (size_t stv = 0; stv < splicing_output_targets_vec.size(); stv++) {
    //     if(splicing_output_targets_vec[stv].target_output.fusion_id == 0){
    //         continue;
    //     }
    //     ZDEBUG("splicing_output_targets_vec-output fusion_id:%d x:%.4f y:%.4f \n", 
    //     splicing_output_targets_vec[stv].target_output.fusion_id,
    //     splicing_output_targets_vec[stv].target_kalman->kf_update_res[0], 
    //     splicing_output_targets_vec[stv].target_kalman->kf_update_res[1]);
    // }
    
#ifdef LANE_AMEND_DEBUG
    ZDEBUG("lane_amend_target_vec Print, lane_amend_target_vec.size:%d \n", lane_amend_target_vec.size());
    for (unsigned int latv = 0; latv < lane_amend_target_vec.size(); latv++)
    {
        std::string lane_amend_points_str = " ";
        for (size_t lap = 0; lap < lane_amend_target_vec[latv].lane_amend_points.size(); lap++)
        {
            lane_amend_points_str += "," + std::to_string(lane_amend_target_vec[latv].lane_amend_points[lap].target_point.x) + "," + std::to_string(lane_amend_target_vec[latv].lane_amend_points[lap].target_point.y);
        }
        ZDEBUG("splicing_id:%d c_lid:%d p_lid:%d c_lane_frame_count:%d c_lane_frame_number:%d lane_amend_res:%d update_target_point x:%.4f y:%.4f amend_target_point x:%.4f y:%.4f lane_amend_points:%s target_amend_point_path size:%d is_delete:%d\n",
               lane_amend_target_vec[latv].splicing_id,
               lane_amend_target_vec[latv].current_lane_id,
               lane_amend_target_vec[latv].previous_lane_id,
               lane_amend_target_vec[latv].change_lane_frame_count,
               lane_amend_target_vec[latv].change_lane_frame_number,
               lane_amend_target_vec[latv].lane_amend_res,
               lane_amend_target_vec[latv].update_target_point.x, lane_amend_target_vec[latv].update_target_point.y,
               lane_amend_target_vec[latv].amend_target_point.x, lane_amend_target_vec[latv].amend_target_point.y,
               lane_amend_points_str.c_str(),
               lane_amend_target_vec[latv].target_amend_point_path.size(),
               lane_amend_target_vec[latv].is_delete);
    }
#endif
}


/*
 * @Name: lane_amend_target_vec_delete
 * @Description: 删除车道外的车辆
 *
 * @Input
 * lane_amend_target_vec_: 车道修正目标的缓存容器
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2025/1/7
 * Time: 15:00
 * Author: JiaTao
 * Content:Create
 */
void VehicleLaneSmoothing::lane_amend_target_vec_delete(std::vector<LaneTargetAmend> &lane_amend_target_vec_)
{
    size_t original_size = lane_amend_target_vec_.size();
    
#ifdef LANE_AMEND_DEBUG
    // 打印修改前的状态
    ZDEBUG("标记前状态 - 总数量: %zu\n", original_size);
    for (const auto& target : lane_amend_target_vec_) {
        ZDEBUG("目标ID: %d, lane_amend_res: %d, is_delete: %d\n", 
               target.splicing_id, static_cast<int>(target.lane_amend_res), target.is_delete);
    }
#endif

    // 将车道外的目标标记为删除
    for (auto& target : lane_amend_target_vec_) {
        if (target.lane_amend_res == LaneSmoothResult::OutLane) {
            target.is_delete = true;
        }
    }

#ifdef LANE_AMEND_DEBUG
    // 统计被标记删除的目标数量
    size_t marked_count = std::count_if(lane_amend_target_vec_.begin(), lane_amend_target_vec_.end(),
        [](const LaneTargetAmend& target) { return target.is_delete; });
        
    ZDEBUG("标记后状态 - 标记删除数量: %zu, 当前数量: %zu\n", 
           marked_count, lane_amend_target_vec_.size());
    for (const auto& target : lane_amend_target_vec_) {
        ZDEBUG("目标ID: %d, lane_amend_res: %d, is_delete: %d\n", 
               target.splicing_id, static_cast<int>(target.lane_amend_res), target.is_delete);
    }
#endif
}


/*
 * @Name: update_splicing_output_targets_vec
 * @Description: 更新splicing_output_targets_vec
 *
 * @Input
 * lane_amend_target_vec_: 修正目标的缓存容器
 * splicing_output_targets_vec: 输出目标的容器
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2025/1/7
 * Time: 16:00
 * Author: JiaTao
 * Content: Create
 */
void VehicleLaneSmoothing::update_splicing_output_targets_vec(
    std::vector<LaneTargetAmend> lane_amend_target_vec_, 
    std::vector<SplicingTarget> &splicing_output_targets_vec_)
{
    for (auto it = splicing_output_targets_vec_.rbegin(); it != splicing_output_targets_vec_.rend(); ) {
        bool should_delete = false;
        
        for (const auto& amend_target : lane_amend_target_vec_) {
            if (it->target_output.fusion_id == amend_target.splicing_id) {
                if (amend_target.is_delete) {
                    should_delete = true;
                } else {
                    // 输出修正的距离
                    // float amend_distance = sqrt(pow(it->target_output.target_coor_in_base.x - amend_target.amend_target_point.x, 2) + 
                    //                            pow(it->target_output.target_coor_in_base.y - amend_target.amend_target_point.y, 2));
                    // ZINFO("id:%d amend_distance: %f\n", it->target_output.fusion_id, amend_distance);
                    it->target_kalman->kf_update_res[0] = amend_target.amend_target_point.x;
                    it->target_kalman->kf_update_res[1] = amend_target.amend_target_point.y;
                    it->lane_id = amend_target.current_lane_id;
                }
                break;
            }
        }
        
        if (should_delete) {
            it->target_kalman = nullptr;
            it = decltype(it)(splicing_output_targets_vec_.erase(std::next(it).base()));
        } else {
            ++it;
        }
    }
    
#ifdef LANE_AMEND_DEBUG
    ZDEBUG("After update: splicing_output_targets_vec_ size: %zu\n", 
           splicing_output_targets_vec_.size());
    for (const auto& target : splicing_output_targets_vec_) {
        ZDEBUG("Remaining target fusion_id: %d\n", target.target_output.fusion_id);
    }
#endif
}

/*
 * @Name: lane_amend_target_point_update
 * @Description: 对目标点的车道级别修正
 *
 * @Input
 * lane_amend_target_vec_: 修正目标的缓存容器
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/12/16
 * Time: 19:42
 * Author: WangXing
 * Content: Create
 */
void VehicleLaneSmoothing::lane_amend_target_point_update(std::vector<LaneTargetAmend> &lane_amend_target_vec_)
{
    // 遍历每个需要修正的目标
    for (size_t atp = 0; atp < lane_amend_target_vec_.size(); atp++)
    {
        // 如果目标不在任何车道上，则不进行更新
        if (lane_amend_target_vec_[atp].lane_amend_res == LaneSmoothResult::NotLane)
        {
            // 不更新
        }
        else
        {
            // 如果目标没有变道
            if (lane_amend_target_vec_[atp].change_lane_frame_count == 0)
            {
                // 遍历目标的车道修正点
                for (size_t lap = 0; lap < lane_amend_target_vec_[atp].lane_amend_points.size(); lap++)
                {
                    // 如果当前车道号与修正点的车道号一致
                    if (lane_amend_target_vec_[atp].current_lane_id == lane_amend_target_vec_[atp].lane_amend_points[lap].lane_id)
                    {
                        // 更新修正目标点为该修正点的目标点
                        lane_amend_target_vec_[atp].amend_target_point = lane_amend_target_vec_[atp].lane_amend_points[lap].target_point;
                        break; // 找到匹配的车道号后退出循环
                    }
                }
            }
            else
            {
                // 处理变道中的目标
                bool exist_previous_laneid = false;
                unsigned int exist_previous_index = 0;
                bool exist_current_laneid = false;
                unsigned int exist_current_index = 0;

                // 查找当前和之前车道的索引
                for (size_t lap = 0; lap < lane_amend_target_vec_[atp].lane_amend_points.size(); lap++)
                {
                    if (lane_amend_target_vec_[atp].current_lane_id == lane_amend_target_vec_[atp].lane_amend_points[lap].lane_id)
                    {
                        exist_current_index = lap;
                        exist_current_laneid = true;
                    }
                    if (lane_amend_target_vec_[atp].previous_lane_id == lane_amend_target_vec_[atp].lane_amend_points[lap].lane_id)
                    {
                        exist_previous_index = lap;
                        exist_previous_laneid = true;
                    }
                    // 如果找到了当前和之前的车道索引，退出循环
                    if (exist_current_laneid == true && exist_previous_laneid == true)
                    {
                        break;
                    }
                }

                // 减少变道帧计数，表示变道过程的推进
                lane_amend_target_vec_[atp].change_lane_frame_count--;

                // 计算变道过程中前一个车道的权重比例
                float previous_rate = float(lane_amend_target_vec_[atp].change_lane_frame_count) / float(lane_amend_target_vec_[atp].change_lane_frame_number);

                // 如果找到了当前和之前的车道索引
                if (exist_current_laneid == true && exist_previous_laneid == true)
                {
                    // 获取前一个车道和当前车道的目标点
                    CalibPoint previous_target_point = lane_amend_target_vec_[atp].lane_amend_points[exist_previous_index].target_point;
                    CalibPoint current_target_point = lane_amend_target_vec_[atp].lane_amend_points[exist_current_index].target_point;

                    // 计算修正目标点为前一个车道和当前车道目标点的加权平均
                    CalibPoint amend_target_point = {
                        previous_rate * previous_target_point.x + (1.0f - previous_rate) * current_target_point.x,
                        previous_rate * previous_target_point.y + (1.0f - previous_rate) * current_target_point.y,
                    };

                    // 更新修正目标点
                    lane_amend_target_vec_[atp].amend_target_point = amend_target_point;
                }

                // 如果变道帧计数减少到0，表示变道完成，重置变道帧数
                if (lane_amend_target_vec_[atp].change_lane_frame_count == 0)
                {
                    lane_amend_target_vec_[atp].change_lane_frame_number = 0;
                }
            }
        }
    }
}

/*
 * @Name: lane_id_update
 * @Description: 车道的更新
 *
 * @Input
 * lane_amend_target_vec_: 车道的确定
 * min_change_lane_duration: 车道的确定时长
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/12/16
 * Time: 13:55
 * Author: WangXing
 * Content: Create
 */
void VehicleLaneSmoothing::lane_id_update(std::vector<LaneTargetAmend> &lane_amend_target_vec_)
{
    for (size_t latv = 0; latv < lane_amend_target_vec_.size(); latv++)
    {
        // 轨迹长度
        unsigned int path_length = lane_amend_target_vec_[latv].target_amend_point_path.size();
        bool update_amend_flag = true;
        if (path_length <= 2)
        {
            // 不更新车道id
            // 不更新lane_amend_res
            // 不更新amend_target_point
            update_amend_flag = false;
        }
        else
        {
            long long path_duration = fabs(lane_amend_target_vec_[latv].target_amend_point_path[path_length - 1].time_ms - lane_amend_target_vec_[latv].target_amend_point_path[0].time_ms);
            if (path_duration < MIN_CHANGE_LANE_DURATION)
            {
                // 不更新车道id
                // 不更新lane_amend_res
                // 不更新amend_target_point
                update_amend_flag = false;
            }
        }

        if (update_amend_flag == true)
        {
            // 车道号变道更新
            int get_current_laneid = get_lane_id(lane_amend_target_vec_[latv].target_amend_point_path,
                                                 MIN_CHANGE_LANE_DURATION);
#ifdef LANE_AMEND_DEBUG
            std::string path_laneid_str = "";
            for (size_t tapp = path_length - 1; tapp < lane_amend_target_vec_[latv].target_amend_point_path.size(); tapp--)
            {
                path_laneid_str += " " + std::to_string(lane_amend_target_vec_[latv].target_amend_point_path[tapp].lane_id);
            }
            ZDEBUG("get_lane_id splicing_id:%d get_current_laneid:%d path_laneid_str:%s\n",
                   lane_amend_target_vec_[latv].splicing_id,
                   get_current_laneid, path_laneid_str.c_str());
#endif

            // 变道结束后更新
            if (lane_amend_target_vec_[latv].change_lane_frame_count == 0)
            {
                lane_id_assign(lane_amend_target_vec_[latv],
                               get_current_laneid);
            }

            // 目标是否在车道内的更新
            LaneSmoothResult lane_res = get_in_lane_res(lane_amend_target_vec_[latv].target_amend_point_path,
                                                        OUT_LANE_DURATION);

            // 车道是否在车道内的更新
            lane_amend_target_vec_[latv].lane_amend_res = lane_res;
        }
        else
        {
            // 不更新lane_id;
        }
    }
}

/*
 * @Name: lane_id_assign
 * @Description: 车道信息更新
 *
 * @Input
 * lane_target: 修正目标的信息
 * detect_lane_id: 检测车道的id
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/12/16
 * Time: 15:58
 * Author: WangXing
 * Content: Create
 */
void VehicleLaneSmoothing::lane_id_assign(LaneTargetAmend &lane_target,
                                          int detect_lane_id)
{
    // 变道更新
    if (lane_target.current_lane_id == -1 && detect_lane_id == -1)
    {
        lane_target.current_lane_id = detect_lane_id;
        lane_target.previous_lane_id = detect_lane_id;
    }
    else if (lane_target.current_lane_id == -1 && detect_lane_id != -1)
    {
        lane_target.current_lane_id = detect_lane_id;
    }
    else if (lane_target.current_lane_id != -1 && detect_lane_id == -1)
    {
        // 不更新
    }
    else
    {
        if (lane_target.current_lane_id == detect_lane_id)
        {
            // 不更新
        }
        else
        {
            // 当前检测的车道号与之前的车道号不相同, 则开始变道
            lane_target.previous_lane_id = lane_target.current_lane_id;
            lane_target.current_lane_id = detect_lane_id;

            // 计算变道的帧数
            lane_target.change_lane_frame_number = get_change_lane_frmae_count(
                lane_target.target_amend_point_path,
                lane_target.previous_lane_id,
                lane_target.current_lane_id);
            lane_target.change_lane_frame_count = lane_target.change_lane_frame_number;
        }
    }
}

/*
 * @Name: get_change_lane_frmae_count
 * @Description: 得到变道的帧数
 *
 * @Input
 * target_amend_point_path: 缓存的轨迹
 * previous_lane_id: 变道前的车道号
 * current_lane_id: 变道后的车道号
 *
 * @Output
 * N: 变道的帧数
 *
 * @Edit History
 * Date: 2024/12/16
 * Time: 19:07
 * Author: WangXing
 * Content: Create
 */
int VehicleLaneSmoothing::get_change_lane_frmae_count(std::vector<LaneTargetPoint> target_amend_point_path,
                                                      int previous_lane_id,
                                                      int current_lane_id)
{
    int change_lane_frame_number = target_amend_point_path.size();

    int path_length = target_amend_point_path.size();
    // 统计轨迹中先前车道号的偏移量
    std::vector<float> previous_lane_target_offset_vec;
    for (size_t tapp = path_length - 1; tapp < target_amend_point_path.size(); tapp--)
    {
        if (previous_lane_id == target_amend_point_path[tapp].lane_id)
        {
            previous_lane_target_offset_vec.push_back(target_amend_point_path[tapp].target_offset);
        }
    }

    if (previous_lane_target_offset_vec.size() <= 2)
    {
        return change_lane_frame_number;
    }
    else
    {
        std::sort(previous_lane_target_offset_vec.begin(), previous_lane_target_offset_vec.end()); // 对向量进行排序
        size_t size = previous_lane_target_offset_vec.size();
        float previous_offset_median = previous_lane_target_offset_vec[0];
        if (size % 2 == 0)
        {
            // 如果向量大小是偶数，返回中间两个数的平均值
            previous_offset_median = (previous_lane_target_offset_vec[size / 2 - 1] + previous_lane_target_offset_vec[size / 2]) / 2.0f;
        }
        else
        {
            // 如果向量大小是奇数，返回中间的数
            previous_offset_median = previous_lane_target_offset_vec[size / 2];
        }

        int change_lane_frame_count = 0;
        for (size_t tapp = path_length - 1; tapp < target_amend_point_path.size(); tapp--)
        {
            if (previous_lane_id == target_amend_point_path[tapp].lane_id)
            {
                if (target_amend_point_path[tapp].target_offset <= previous_offset_median)
                {
                    break;
                }
            }
            change_lane_frame_count++;
        }
        return change_lane_frame_count;
    }
}

/*
 * @Name: get_in_lane_res
 * @Description: 得到目标是否在车道外
 *
 * @Input
 * target_amend_point_path: 目标轨迹
 * out_lane_duration: 目标在车道外的时长
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/12/16
 * Time: 15:13
 * Author: WangXing
 * Content: Create
 */
LaneSmoothResult VehicleLaneSmoothing::get_in_lane_res(std::vector<LaneTargetPoint> target_amend_point_path,
                                                       long long out_lane_duration)
{
    unsigned int path_length = target_amend_point_path.size();
    long long detect_time_ms = target_amend_point_path[path_length - 1].time_ms;
    int lane_out_id = -1;
    LaneSmoothResult lane_res = LaneSmoothResult ::NotLane;

    bool out_lane = false;
    for (size_t tapp = path_length - 1; tapp < target_amend_point_path.size(); tapp--)
    {
        // if(detect_time_ms - target_amend_point_path[tapp].time_ms > out_lane_duration)
        // {
        //     break;
        // }
        // if (lane_out_id != target_amend_point_path[tapp].lane_id)
        // {
        //     out_lane = false;
        //     break;
        // }
        // 如果在车道外，则直接设置为out_lane
        if (lane_out_id == target_amend_point_path[tapp].lane_id)
        {
            out_lane = true;
            break;
        }
    }
    if (out_lane == true)
    {
        lane_res = LaneSmoothResult ::OutLane;
    }
    else
    {
        lane_res = LaneSmoothResult ::InLane;
    }

    return lane_res;
}

/*
 * @Name: get_lane_id
 * @Description: 得到车道号
 *
 * @Input
 * target_amend_point_path: 修正后的目标轨迹
 * min_change_lane_duration: 确定车道号的最短轨迹长度
 *
 * @Output
 * -1: 未获取车道号
 * 0 ~ N: 相对应的车道号
 *
 * @Edit History
 * Date: 2024/12/16
 * Time: 14:20
 * Author: WangXing
 * Content: Create
 */
int VehicleLaneSmoothing::get_lane_id(std::vector<LaneTargetPoint> target_amend_point_path,
                                      long long min_change_lane_duration)
{
    unsigned int path_length = target_amend_point_path.size();
    int detect_lane_id = target_amend_point_path[path_length - 1].lane_id;
    long long detect_time_ms = target_amend_point_path[path_length - 1].time_ms;
    bool exist_lane_id = true;
    for (size_t tapp = path_length - 1; tapp < target_amend_point_path.size(); tapp--)
    {
        if (detect_time_ms - target_amend_point_path[tapp].time_ms > min_change_lane_duration)
        {
            break;
        }
        if (detect_lane_id != target_amend_point_path[tapp].lane_id)
        {
            exist_lane_id = false;
            break;
        }
    }
    if (exist_lane_id == true)
    {
        return detect_lane_id;
    }
    else
    {
        return -1;
    }
}

/*
 * @Name: lane_amend_target_vec_update
 * @Description: 车道修正目标容器的更新
 *
 * @Input
 * splicing_output_targets_vec: 输出的拼接目标容器
 * lane_amend_target_vec_: 车道修正后的目标
 * path_max_duration: 修正轨迹保留最大时长
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/12/16
 * Time: 11:35
 * Author: WangXing
 * Content: Create
 */
void VehicleLaneSmoothing::lane_amend_target_vec_update(std::vector<SplicingTarget> splicing_output_targets_vec,
                                                        std::vector<LaneTargetAmend> &lane_amend_target_vec_,
                                                        long long path_max_duration)
{
    // 更新 lane_amend_target_vec容器中的target_amend_point_path
    for (size_t sotv = 0; sotv < splicing_output_targets_vec.size(); sotv++)
    {
        if (splicing_output_targets_vec[sotv].target_output.fusion_id == 0)
        {
            continue;
        }
        CalibPoint update_target_point =
            {
                splicing_output_targets_vec[sotv].target_kalman->kf_update_res[0],
                splicing_output_targets_vec[sotv].target_kalman->kf_update_res[1]};
        LaneTargetPoint lane_smooh_target_point;
        lane_smooh_target_point.lane_id = -1;
        lane_smooh_target_point.target_offset = 0;
        lane_smooh_target_point.target_point = update_target_point;
        lane_smooh_target_point.time_ms = splicing_output_targets_vec[sotv].target_output.target_timestamp_ms;
        std::vector<LaneTargetPoint> lane_smooth_points;
        get_lane_smooth_point(update_target_point,
                              lane_smooh_target_point,
                              lane_smooth_points);
        // #ifdef LANE_AMEND_DEBUG
        //         ZDEBUG("splicing_id:%d lane_smooh_target_point l_id:%d target_offset:%.2f target_point x:%.2f y:%.2f\n",
        //                splicing_output_targets_vec[sotv].target_output.fusion_id,
        //                lane_smooh_target_point.lane_id,
        //                lane_smooh_target_point.target_offset,
        //                lane_smooh_target_point.target_point.x, lane_smooh_target_point.target_point.y);
        //         ZDEBUG("splicing_id:%d lane_smooth_points size:%d \n",
        //                splicing_output_targets_vec[sotv].target_output.fusion_id,
        //                lane_smooth_points.size());
        //         for(size_t lsp = 0; lsp < lane_smooth_points.size(); lsp++)
        //         {
        //             ZDEBUG("th:%d/%d l_id:%d x:%2f y:%.2f target_offset:%.2f time_ms:%lld\n",
        //                    lsp, lane_smooth_points.size(),
        //                    lane_smooth_points[lsp].lane_id,
        //                    lane_smooth_points[lsp].target_point.x, lane_smooth_points[lsp].target_point.y,
        //                    lane_smooth_points[lsp].target_offset, lane_smooth_points[lsp].time_ms);
        //         }
        // #endif

        bool exist_amend_target = false;
        unsigned int exist_index = 0;
        for (size_t latv = 0; latv < lane_amend_target_vec.size(); latv++)
        {
            if (splicing_output_targets_vec[sotv].target_output.fusion_id == lane_amend_target_vec[latv].splicing_id)
            {
                exist_amend_target = true;
                exist_index = latv;
                break;
            }
        }

        if (exist_amend_target == true)
        {
            lane_amend_target_vec[exist_index].update_target_point = update_target_point;
            lane_amend_target_vec[exist_index].amend_target_point = update_target_point;
            lane_amend_target_vec[exist_index].lane_amend_points = lane_smooth_points;
            // 不更新current_lane_id、
            //  previous_lane_id、amend_target_point,
            //  change_lane_frame_count, change_lane_frame_number;
            // 初始化splicing_line_ret
            lane_amend_target_vec[exist_index].target_amend_point_path.push_back(lane_smooh_target_point);
        }
        else
        {
            LaneTargetAmend add_amend_target;
            add_amend_target.splicing_id = splicing_output_targets_vec[sotv].target_output.fusion_id;
            add_amend_target.target_amend_point_path.push_back(lane_smooh_target_point);

            add_amend_target.update_target_point = update_target_point;
            add_amend_target.amend_target_point = update_target_point;
            add_amend_target.current_lane_id = -1;
            add_amend_target.previous_lane_id = -1;
            add_amend_target.change_lane_frame_count = 0;
            add_amend_target.change_lane_frame_number = 0;
            add_amend_target.lane_amend_points = lane_smooth_points;
            add_amend_target.is_delete = false;

            add_amend_target.lane_amend_res = LaneSmoothResult ::NotLane;
            lane_amend_target_vec.push_back(add_amend_target);
        }
    }

    // 目标删除
    // 输入目标长时间未更新，则删除
    std::vector<LaneTargetAmend>::iterator lane_amend_target_vec_iter;
    for (lane_amend_target_vec_iter = lane_amend_target_vec.begin(); lane_amend_target_vec_iter != lane_amend_target_vec.end();)
    {
        bool exist_target = false;
        for (size_t sotv = 0; sotv < splicing_output_targets_vec.size(); sotv++)
        {
            if (lane_amend_target_vec_iter->splicing_id == splicing_output_targets_vec[sotv].target_output.fusion_id)
            {
                exist_target = true;
                break;
            }
        }
        if (exist_target == false)
        {
            lane_amend_target_vec_iter = lane_amend_target_vec.erase(lane_amend_target_vec_iter);
        }
        else
        {
            lane_amend_target_vec_iter++;
        }
    }

    // 轨迹修正的长度更新
    for (size_t latv = 0; latv < lane_amend_target_vec.size(); latv++)
    {
        size_t target_amend_point_path_length = lane_amend_target_vec[latv].target_amend_point_path.size();
        if (target_amend_point_path_length <= 2)
        {
            continue;
        }
        long long path_time = fabs(lane_amend_target_vec[latv].target_amend_point_path[target_amend_point_path_length - 1].time_ms - lane_amend_target_vec[latv].target_amend_point_path[0].time_ms);
        if (path_time > path_max_duration)
        {
            std::vector<LaneTargetPoint>::iterator target_amend_point_path_iter = lane_amend_target_vec[latv].target_amend_point_path.begin();
            lane_amend_target_vec[latv].target_amend_point_path.erase(target_amend_point_path_iter);
        }
    }
}

/*
 * @Name: lane_infos_update
 * @Description: 车道内信息的更新
 *
 * @Input
 * road_config: 道路配置信息
 * lane_infos_update: 待更新的车道配置容器
 *
 * @Output
 * 0: 更新成功  -1: 更新失败
 *
 * @Edit History
 * Date: 2024/12/5
 * Time: 14:44
 * Author: WangXing
 * Content: Create
 */
int VehicleLaneSmoothing::lane_infos_update(std::vector<RoadConfigAlg> road_config_vec)
{
    lane_infos_clear();
    ZINFO("lane_infos_clear! \n");

    int ret = 0;
    // 将道路信息中的点按照lane_index进行排序
    road_config_lanes_sort(road_config_vec);

    // 将道路中的点进行采样
    road_config_lanes_sample(road_config_vec, LANE_MAX_POINT_NUMBER);

    // 打印道路配置信息
    print_road_config(road_config_vec);

    if (road_config_vec.size() == 0)
    {
        ZINFO("road_config_vec == 0 \n");
        ret = -1;
        return ret;
    }

    // 依据设备序号选择第一台设备
    DeivceInfo first_device;
    ret = get_first_device(road_config_vec[0].device_vec, first_device);
    if (ret == -1)
    {
        ZINFO("get_first_device Failed! \n");
        return ret;
    }

    // 计算第一台设备的经纬度
    lonlat_mercator(first_device.device_longitude,
                    first_device.device_latitude,
                    first_device.x,
                    first_device.y);
    ZINFO("the first device device_id:%d longitude:%.6f latitude:%.6f x:%.2f y:%.2f \n",
          first_device.device_id,
          first_device.device_longitude,
          first_device.device_latitude,
          first_device.x,
          first_device.y);

    // 车道信息的委派
    ret = lane_infos_assign(road_config_vec, this->lane_infos, first_device);

    if (ret == -1)
    {
        lane_infos_clear();
    }
    else
    {
        print_lane_infos(this->lane_infos);
    }
    return ret;
}

/*
 * @Name: road_config_lanes_sample
 * @Description: 对网页端下发的每条车道
 *
 * @Input
 * road_config_vec: 道路配置信息
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/12/25
 * Time: 15:29
 * Author: WangXing
 * Content: Create
 */
void VehicleLaneSmoothing::road_config_lanes_sample(std::vector<RoadConfigAlg> &road_config_vec,
                                                    int max_lane_number)
{
    for (size_t rc = 0; rc < road_config_vec.size(); rc++)
    {
        if (road_config_vec[rc].lane_vec.size() == 0)
        {
            continue;
        }
        else
        {
            // 当前的道路车道线的点的数量相同
            unsigned int lane_number = road_config_vec[rc].lane_vec[0].coordinate.size();
            if (lane_number < max_lane_number)
            {
                continue;
            }
            else
            {
                unsigned int sample_point = lane_number / max_lane_number + 1;
                for (size_t lv = 0; lv < road_config_vec[rc].lane_vec.size(); lv++)
                {
                    std::vector<CoordinateAlg> lane_coordinate_vec = road_config_vec[rc].lane_vec[lv].coordinate;
                    std::vector<CoordinateAlg> sample_lane_vec;
                    for (size_t tlv = 0; tlv < lane_coordinate_vec.size(); tlv++)
                    {
                        if (tlv % sample_point == 0)
                        {
                            sample_lane_vec.push_back(lane_coordinate_vec[tlv]);
                        }
                        if (tlv == lane_number - 1)
                        {
                            sample_lane_vec.push_back(lane_coordinate_vec[tlv]);
                        }
                    }
                    ZINFO("lane_coordinate_vec size:%d sample_lane_vec size:%d \n",
                          lane_coordinate_vec.size(), sample_lane_vec.size());
                    road_config_vec[rc].lane_vec[lv].coordinate.assign(sample_lane_vec.begin(), sample_lane_vec.end());
                }
            }
        }
    }
}

/*
 * @Name:compare_lane_index
 * @Description:比较函数
 *
 * @Input
 * 无
 *
 * @Output
 *
 *
 * @Edit History
 * Date: 2024/12/18
 * Time: 20:47
 * Author: WangXing
 * Content: Create
 */
bool VehicleLaneSmoothing::compare_lane_index(const LaneAlg &a, const LaneAlg &b)
{
    return a.lane_index < b.lane_index;
}

/*
 * @Name: road_config_lanes_sort
 * @Description: 对道路中的信息按照车道线进行排序
 *
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/12/18
 * Time: 20:40
 * Author: WangXing
 * Content: Create
 */
void VehicleLaneSmoothing::road_config_lanes_sort(std::vector<RoadConfigAlg> &road_config)
{
    for (size_t rc = 0; rc < road_config.size(); rc++)
    {
        std::sort(road_config[rc].lane_vec.begin(), road_config[rc].lane_vec.end(), compare_lane_index);
    }
}

/*
 * @Name: get_lane_smooth_point
 * @Description: 车道平滑后的目标位置
 *
 * @Input
 * target_point: 拼接后的目标位置
 * lane_smooth_point: 拼接后的车道平滑后的目标位置
 * target_lane_id: 目标的车道号
 *
 * @Output
 * 0: 目标车道级别平滑处理成功
 * -1: 目标车道级别平滑处理失败
 *
 * @Edit History
 * Date: 2024/12/7
 * Time: 15:38
 * Author: WangXing
 * Content: Create
 */
void VehicleLaneSmoothing::get_lane_smooth_point(CalibPoint target_point,
                                                 LaneTargetPoint &lane_smooh_target_point,
                                                 std::vector<LaneTargetPoint> &lane_smooth_points)
{
    if (lane_infos.size() == 0)
    {
        ZERROR("lane_infos.size() == 0, target_point is not lane smooth point, target_point x:%.2f y:%.2f is off road lane!\n",
               target_point.x,
               target_point.y);
        return;
    }
    // 目标点与车道线所以的垂线
    for (size_t lis = 0; lis < lane_infos.size(); lis++)
    {
        for (size_t lia = 0; lia < lane_infos[lis].lane_areas.size(); lia++)
        {
            CalibPoint target_intersection_point = {0, 0};
            int ret = find_vertical_intersection(target_point,
                                                 lane_infos[lis].lane_areas[lia].lane_center_line,
                                                 target_intersection_point);
            if (ret == -1)
            {
                continue;
            }
            else
            {

                // ZERROR("get_lane_smooth_point target_point x:%.2f y:%.2f target_intersection_point x:%.2f y:%.2f lane_center_line p1 x:%.2f y:%.2f p2 x:%2.f y:%.2f\n",
                //        target_point.x, target_point.y,
                //        target_intersection_point.x, target_intersection_point.y,
                //        lane_infos[lis].lane_areas[lia].lane_center_line[0].x,
                //        lane_infos[lis].lane_areas[lia].lane_center_line[0].y,
                //        lane_infos[lis].lane_areas[lia].lane_center_line[1].x,
                //        lane_infos[lis].lane_areas[lia].lane_center_line[1].y);
                float x_offset = fabs(target_intersection_point.x - target_point.x);
                float y_offset = fabs(target_intersection_point.y - target_point.y);
                float target_to_lane_offset = sqrt(pow(x_offset, 2) + pow(y_offset, 2));

                LaneTargetPoint lane_target_point;
                lane_target_point.lane_id = lane_infos[lis].lane_id;
                lane_target_point.target_point = target_intersection_point;
                lane_target_point.target_offset = target_to_lane_offset;
                lane_target_point.time_ms = lane_smooh_target_point.time_ms;
                lane_smooth_points.push_back(lane_target_point);
            }
        }
    }

    // 目标在车道外
    if (lane_smooth_points.size() == 0)
    {
        // ZERROR("lane_smooth_points.size() == 0, target_point is not lane smooth point, target_point x:%.2f y:%.2f is off road lane!\n",
        //        target_point.x,
        //        target_point.y);
        return;
    }

    float max_offset = MAXFLOAT;
    unsigned int most_nearest_index = 0;
    for (size_t lsp = 0; lsp < lane_smooth_points.size(); lsp++)
    {
        if (lane_smooth_points[lsp].target_offset < max_offset)
        {
            max_offset = lane_smooth_points[lsp].target_offset;
            most_nearest_index = lsp;
        }
    }

    if (max_offset < MAXFLOAT)
    {
        lane_smooh_target_point = lane_smooth_points[most_nearest_index];
    }
    else
    {
        ZERROR("max_offset == MAXFLOAT, target_point is not lane smooth point, target_point x:%.2f y:%.2f\n",
               target_point.x,
               target_point.y);
        return;
    }
}

/*
 * @Name: get_lane_direction
 * @Description: 得到目标所在位置的车道方位
 *
 * @Input
 * target_point: 目标所在的位置
 * target_lane_direction: 目标所在位置的方位
 *
 * @Output
 * 0: 成功获取目标所在车道的方位
 * -1: 不能成功获取目标所在的方位
 *
 * @Edit History
 * Date: 2024/12/7
 * Time: 15:44
 * Author: WangXing
 * Content: Create
 */
int VehicleLaneSmoothing::get_lane_target_direction(CalibPoint target_point,
                                                    double &target_lane_direction)
{
    if (lane_infos.size() == 0)
    {
        return -1;
    }
    float max_offset = MAXFLOAT;
    double lane_direction = 0;
    for (size_t lis = 0; lis < lane_infos.size(); lis++)
    {
        for (size_t lia = 0; lia < lane_infos[lis].lane_areas.size(); lia++)
        {
            CalibPoint target_intersection_point = {0, 0};
            int ret = find_vertical_intersection(target_point,
                                                 lane_infos[lis].lane_areas[lia].lane_center_line,
                                                 target_intersection_point);
            if (ret == -1)
            {
                continue;
            }
            else
            {
                float x_offset = fabs(target_intersection_point.x - target_point.x);
                float y_offset = fabs(target_intersection_point.y - target_point.y);
                float target_to_lane_offset = sqrt(pow(x_offset, 2) + pow(y_offset, 2));
                if (target_to_lane_offset < max_offset)
                {
                    lane_direction = lane_infos[lis].lane_areas[lia].lane_direction;
                    max_offset = target_to_lane_offset;
                }
            }
        }
    }

    if (max_offset < MAXFLOAT)
    {
        target_lane_direction = lane_direction;
        return 0;
    }
    else
    {
        ZERROR("target_point is not lane direction, target_point x:%.2f y:%.2f \n",
               target_point.x,
               target_point.y);
        return -1;
    }
}

/*
 * @Name: lane_infos_assign
 * @Description: 车道信息的委派
 *
 * @Input
 * road_config: 道路配置信息
 * lane_infos:  车道信息
 * the_first_device: 编号为1的设备
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/12/5
 * Time: 17:53
 * Author: WangXing
 * Content: Create
 */
int VehicleLaneSmoothing::lane_infos_assign(std::vector<RoadConfigAlg> road_config_vec,
                                            std::vector<LaneInfo> &lane_infos,
                                            DeivceInfo the_first_device)
{
    ZINFO("lane_infos_assign start! \n");
    // 校验道路车道的数量是否正常
    for (size_t rcv = 0; rcv < road_config_vec.size(); rcv++)
    {
        RoadConfigAlg road_config = road_config_vec[rcv];
        if (road_config.coordinate_vec.size() < 2)
        {
            ZINFO("th:%d/%d road coordinate number is not normal, number:%d \n",
                  rcv,
                  road_config_vec.size(),
                  road_config.coordinate_vec.size());
            return -1;
        }
        ZINFO("road_config lane_vec:%d\n", road_config.lane_vec.size());
        for (size_t lv = 0; lv < road_config.lane_vec.size(); lv++)
        {
            if (road_config.lane_vec[lv].coordinate.size() < 2)
            {
                ZINFO("th:%d/%d lane_line coordinate number is not normal, number:%d \n",
                      lv,
                      road_config.lane_vec.size(),
                      road_config.lane_vec[lv].coordinate.size());
                return -1;
            }
        }
    }
    ZINFO("road check is normal! \n");

    // 生成道路的方向和区域
    int init_lane_id = 1;
    for (size_t rcv = 0; rcv < road_config_vec.size(); rcv++)
    {
        std::vector<LaneAlg> road_lanes = road_config_vec[rcv].lane_vec;
        for (size_t rl = 0; rl < road_lanes.size(); rl++)
        {
            std::vector<CoordinateAlg> lane_line_first = road_lanes[rl].coordinate;
            std::string lane_point_str = "";
            for (size_t lcount = 0; lcount < lane_line_first.size(); lcount++)
            {
                cv::Point2f area_p = {0, 0};
                lonlat_mercator(lane_line_first[lcount].longitude,
                                lane_line_first[lcount].latitude,
                                area_p.x,
                                area_p.y);
                area_p.x -= the_first_device.x;
                area_p.y -= the_first_device.y;
                std::string point_str = " " + std::to_string(area_p.x) + " " + std::to_string(area_p.y) + ",";
                lane_point_str += point_str;
            }
            ZINFO("the lane:%d/%d lane_index:%d lane_point_str:%s \n",
                  rl, road_lanes.size(), road_lanes[rl].lane_index, lane_point_str.c_str());
        }

        for (size_t lv = 1; lv < road_lanes.size(); lv++)
        {
            LaneInfo road_lane;
            std::vector<CoordinateAlg> lane_line_first = road_lanes[lv - 1].coordinate;
            std::vector<CoordinateAlg> lane_line_second = road_lanes[lv].coordinate;
            std::vector<LaneArea> road_lane_areas;
            for (size_t lcount = 1; lcount < lane_line_first.size(); lcount++)
            {
                cv::Point2f area_p1 = {0, 0};
                lonlat_mercator(lane_line_first[lcount - 1].longitude,
                                lane_line_first[lcount - 1].latitude,
                                area_p1.x,
                                area_p1.y);
                area_p1.x -= the_first_device.x;
                area_p1.y -= the_first_device.y;
                cv::Point2f area_p2 = {0, 0};
                lonlat_mercator(lane_line_first[lcount].longitude,
                                lane_line_first[lcount].latitude,
                                area_p2.x,
                                area_p2.y);
                area_p2.x -= the_first_device.x;
                area_p2.y -= the_first_device.y;
                cv::Point2f area_p3 = {0, 0};
                lonlat_mercator(lane_line_second[lcount].longitude,
                                lane_line_second[lcount].latitude,
                                area_p3.x,
                                area_p3.y);
                area_p3.x -= the_first_device.x;
                area_p3.y -= the_first_device.y;
                cv::Point2f area_p4 = {0, 0};
                lonlat_mercator(lane_line_second[lcount - 1].longitude,
                                lane_line_second[lcount - 1].latitude,
                                area_p4.x,
                                area_p4.y);
                area_p4.x -= the_first_device.x;
                area_p4.y -= the_first_device.y;

                std::vector<cv::Point2f> lane_area_point; // 矩形区域
                lane_area_point.push_back(area_p1);
                lane_area_point.push_back(area_p2);
                lane_area_point.push_back(area_p3);
                lane_area_point.push_back(area_p4);

                std::vector<CalibPoint> lane_center_line; // 中心线
                CalibPoint first_center = {
                    (area_p1.x + area_p4.x) / 2.0f,
                    (area_p1.y + area_p4.y) / 2.0f};
                CalibPoint second_center = {
                    (area_p2.x + area_p3.x) / 2.0f,
                    (area_p2.y + area_p3.y) / 2.0f};
                lane_center_line.push_back(first_center);
                lane_center_line.push_back(second_center);

                double lane_direction = calculate_angle(first_center, second_center);
                if (std::isnan(lane_direction))
                {
                    ZINFO("road_id:%s lane_direction is NAN! first_center x:%.2f y:%.2f second_center x:%.2f y:%.2f\n",
                          road_config_vec[rcv].road_id.c_str(),
                          first_center.x,
                          first_center.y,
                          second_center.x,
                          second_center.y);
                    return -1;
                }
                LaneArea temp_lane_area;
                temp_lane_area.lane_direction = lane_direction;
                temp_lane_area.lane_center_line = lane_center_line;
                temp_lane_area.lane_area = lane_area_point;
                road_lane_areas.push_back(temp_lane_area);
            }
            road_lane.lane_id = init_lane_id;
            road_lane.lane_areas = road_lane_areas;
            lane_infos.push_back(road_lane);
            init_lane_id += 1;
        }
    }
    ZINFO("lane_infos_assign ending! \n");
    return 0;
}

/*
 * @Name: get_first_device
 * @Description: 获取第一台设备
 *
 * @Input
 * device_vec:   设备的信息列表
 * first_device: 序号考前的第一台设备信息
 * @Output
 * 0: 成功； -1:失败
 *
 * @Edit History
 * Date: 2024/12/5
 * Time: 15:42
 * Author: WangXing
 * Content: Create
 */
int VehicleLaneSmoothing::get_first_device(std::vector<DeivceInfo> device_vec, DeivceInfo &first_device)
{
    int ret = 0;
    unsigned int min_device_id = UINT8_MAX;
    unsigned int min_device_index = 0;
    for (size_t dc = 0; dc < device_vec.size(); dc++)
    {
        if (device_vec[dc].device_id < min_device_id)
        {
            min_device_id = device_vec[dc].device_id;
            min_device_index = dc;
        }
    }

    if (min_device_id < UINT8_MAX)
    {
        first_device = device_vec[min_device_index];
    }
    else
    {
        ret = -1;
    }
    return ret;
}

/*
 * @Name:lonlatToMercator
 * @Description:将经纬度坐标转换为墨卡托坐标
 *
 * @Input
 * lng 经度值
 * lat 纬度值
 * 墨卡托坐标对象，包含x和y属性
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/9/27
 * Time: 16:59
 * Author: WangXing
 * Content: Create
 */
void VehicleLaneSmoothing::lonlat_mercator(double lng, double lat, float &x, float &y)
{
    const double earthRad = 6378137.0;
    x = lng * (M_PI / 180.0) * earthRad;
    double a = lat * (M_PI / 180.0);
    y = earthRad / 2.0 * log((1.0 + sin(a)) / (1.0 - sin(a)));
}

/*
 * @Name:
 * @Description:
 *
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/10/23
 * Time: 16:26
 * Author: WangXing
 * Content: Create
 */
double VehicleLaneSmoothing::calculate_angle(const CalibPoint &A, const CalibPoint &B)
{
    double deltaX = B.x - A.x;
    double deltaY = B.y - A.y;

    // 计算向量的模长
    double magnitude = std::sqrt(deltaX * deltaX + deltaY * deltaY);

    // 计算与x轴正方向的夹角的余弦值
    double cosineAngle = deltaX / magnitude;

    // 计算角度（弧度）
    double angleRadians = std::acos(cosineAngle);

    // 将角度转换为度数
    double angleDegrees = angleRadians * (180.0 / M_PI);

    // 由于acos返回的是0到π之间的值，所以这里的角度是0到180度之间的
    // 如果需要得到完整的角度（0到360度），可以根据deltaY的符号来判断
    if (deltaY < 0)
    {
        angleDegrees = 360.0 - angleDegrees;
    }

    // 注意：这里假设了角度是在0到360度之间，且逆时针为正方向。
    return angleDegrees;
}

/*
 * @Name: find_vertical_intersection
 * @Description: 判断由已知点引出的垂直线是否与给定线段相交，如果相交则输出相交点, 并返回成功，如果不相交则返回失败；
 *
 * @Input
 * point: 待判断的点
 * line_segment: 线段
 * intersection_point: 相交点
 *
 * @Output
 * 0: 存在相交点
 * -1: 不存在相交点
 *
 * @Edit History
 * Date: 2024/12/5
 * Time: 14:59
 * Author: WangXing
 * Content: Create
 */
int VehicleLaneSmoothing::find_vertical_intersection(const CalibPoint point,
                                                     const std::vector<CalibPoint> line_segment,
                                                     CalibPoint &intersection_point)
{
    float x0 = point.x;
    float y0 = point.y;

    float x1 = line_segment[0].x;
    float y1 = line_segment[0].y;

    float x2 = line_segment[1].x;
    float y2 = line_segment[1].y;

    double dx = x2 - x1;
    double dy = y2 - y1;

    // 检查线段端点是否相同
    if (dx == 0 && dy == 0)
    {
        ZERROR("The end points of a line segment are the same!  point and line_segment is not intersection! \n");
        return -1;
    }

    // 计算垂足
    double t = ((x0 - x1) * dx + (y0 - y1) * dy) / (dx * dx + dy * dy);
    double x = x1 + t * dx;
    double y = y1 + t * dy;

    // 检查垂足是否在线段上
    if (t >= 0 && t <= 1)
    {
        intersection_point.x = x;
        intersection_point.y = y;
        return 0;
    }
    else
    {
        return -1;
    }
}

/*
 * @Name: road_config
 * @Description: 打印车道配置信息
 *
 * @Input
 * road_config: 车道配置信息
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/12/5
 * Time: 14:59
 * Author: WangXing
 * Content: Create
 */
void VehicleLaneSmoothing::print_road_config(const std::vector<RoadConfigAlg> road_config_vec)
{
    ZINFO("road_config_vec size:%d \n", road_config_vec.size());
    for (size_t rcv = 0; rcv < road_config_vec.size(); rcv++)
    {
        RoadConfigAlg road_config = road_config_vec[rcv];
        ZINFO("road_config th:%d/%d road_id:%s road_name:%s road_lane_width:%.2f road_width:%.2f \n",
              rcv, road_config_vec.size(),
              road_config.road_id.c_str(),
              road_config.road_name.c_str(),
              road_config.road_lane_width,
              road_config.road_width);
        ZINFO("road_config coordinate_vec size:%d \n",
              road_config.coordinate_vec.size());
        for (size_t cv = 0; cv < road_config.coordinate_vec.size(); cv++)
        {
            ZINFO("th:%d/%d latitude:%.6f longitude:%.6f \n",
                  cv, road_config.coordinate_vec.size(),
                  road_config.coordinate_vec[cv].latitude,
                  road_config.coordinate_vec[cv].longitude);
        }
        ZINFO("road_config lane_vec:%d\n", road_config.lane_vec.size());
        for (size_t lv = 0; lv < road_config.lane_vec.size(); lv++)
        {
            ZINFO("th:%d/%d lane_index:%d lane_line coordinate_vec size:%d \n",
                  lv,
                  road_config.lane_vec.size(),
                  road_config.lane_vec[lv].lane_index,
                  road_config.lane_vec[lv].coordinate.size());
            for (size_t lvc = 0; lvc < road_config.lane_vec[lv].coordinate.size(); lvc++)
            {
                ZINFO("th:%d/%d latitude:%.6f longitude:%.6f \n",
                      lvc, road_config.lane_vec[lv].coordinate.size(),
                      road_config.lane_vec[lv].coordinate[lvc].latitude,
                      road_config.lane_vec[lv].coordinate[lvc].longitude);
            }
        }
        ZINFO("road_config device_vec:%d \n", road_config.device_vec.size());
        for (size_t dv = 0; dv < road_config.device_vec.size(); dv++)
        {
            ZINFO("th:%d/%d device_id:%d device_enable:%d device_direction:%.6f device_latitude:%.6f device_longitude:%6f x:%.2f y:%.2f\n",
                  dv, road_config.device_vec.size(),
                  road_config.device_vec[dv].device_id,
                  road_config.device_vec[dv].device_enable,
                  road_config.device_vec[dv].device_direction,
                  road_config.device_vec[dv].device_latitude,
                  road_config.device_vec[dv].device_longitude,
                  road_config.device_vec[dv].x,
                  road_config.device_vec[dv].y);
        }
        ZINFO("\n \n");
    }
}

/*
 * @Name: print_lane_infos
 * @Description: 打印绘制的车道信息
 *
 * @Input
 * lane_infos: 车道信息
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/12/5
 * Time: 20:13
 * Author: WangXing
 * Content: Create
 */
void VehicleLaneSmoothing::print_lane_infos(const std::vector<LaneInfo> lane_infos_)
{
    ZINFO("lane_infos_ size:%d \n", lane_infos_.size());
    for (size_t li = 0; li < lane_infos_.size(); li++)
    {
        ZINFO("lane_id:%d lane_areas size:%d \n",
              lane_infos_[li].lane_id,
              lane_infos_[li].lane_areas.size());

        std::string lane_direction_str = "";
        for (size_t la = 0; la < lane_infos_[li].lane_areas.size(); la++)
        {
            std::ostringstream lane_direction_str_center;
            lane_direction_str_center << std::fixed << std::setprecision(1) << (lane_infos_[li].lane_areas[la].lane_direction);
            lane_direction_str += " " + lane_direction_str_center.str();
        }
        ZINFO("lane_direction_lane_id:%d lane_direction_str:%s \n",
              lane_infos_[li].lane_id,
              lane_direction_str.c_str());

        for (size_t la = 0; la < lane_infos_[li].lane_areas.size(); la++)
        {
            std::string lane_areas_str = "";
            for (size_t laa = 0; laa < lane_infos_[li].lane_areas[la].lane_area.size(); laa++)
            {
                std::ostringstream lane_area_x;
                lane_area_x << std::fixed << std::setprecision(1) << (lane_infos_[li].lane_areas[la].lane_area[laa].x);
                lane_areas_str += " " + lane_area_x.str();

                std::ostringstream lane_area_y;
                lane_area_y << std::fixed << std::setprecision(1) << (lane_infos_[li].lane_areas[la].lane_area[laa].y);
                lane_areas_str += " " + lane_area_y.str();
            }
            ZINFO("lane_areas_lane_id:%d th:%d/%d lane_areas_str:%s \n",
                  lane_infos_[li].lane_id,
                  la, lane_infos_[li].lane_areas.size(),
                  lane_areas_str.c_str());
        }

        for (size_t la = 0; la < lane_infos_[li].lane_areas.size(); la++)
        {
            std::string lane_center_line_str = "";
            for (size_t laa = 0; laa < lane_infos_[li].lane_areas[la].lane_center_line.size(); laa++)
            {
                std::ostringstream lane_center_line_x;
                lane_center_line_x << std::fixed << std::setprecision(1) << (lane_infos_[li].lane_areas[la].lane_center_line[laa].x);
                lane_center_line_str += " " + lane_center_line_x.str();

                std::ostringstream lane_center_line_y;
                lane_center_line_y << std::fixed << std::setprecision(1) << (lane_infos_[li].lane_areas[la].lane_center_line[laa].y);
                lane_center_line_str += " " + lane_center_line_y.str();
            }
            ZINFO("lane_center_line_lane_id:%d th:%d/%d lane_center_line_str:%s \n",
                  lane_infos_[li].lane_id,
                  la, lane_infos_[li].lane_areas.size(),
                  lane_center_line_str.c_str());
        }
    }
}

std::vector<LaneInfo> VehicleLaneSmoothing::get_lane_infos()
{
    return this->lane_infos;
}