#pragma once
#include "tecu1000_algorithm_header.h"
#include <opencv2/opencv.hpp>            // C++
#include <opencv2/highgui/highgui_c.h>   // C
#include <opencv2/imgproc/imgproc_c.h>   // C

#define LANE_AMEND_PATH_MAX_DURATION 4000       //车道修正轨迹缓存的时长
#define OUT_LANE_DURATION 0                  //目标在车道外的时长
#define MIN_CHANGE_LANE_DURATION 500            //车道变道的时长
#define LANE_MAX_POINT_NUMBER 100               //一条车道线最多的数量

using namespace tecu1000_alg;
// #define LANE_AMEND_DEBUG


namespace tecu_r1000_0_algorithm
{
    /********************************************************
     * Description          			 道路车道信息的结构体
     * lane_area                         车道雷划分的四个角点 使用opencv判断点是否在区域内
     * lane_center_line                  四个角点的中心线
     * lane_direction                    该矩形区域内车道的方向
    ********************************************************/
    typedef struct
    {
        std::vector<cv::Point2f> lane_area;
        std::vector<CalibPoint>  lane_center_line;
        double     lane_direction;
    }LaneArea;


    /********************************************************
     * Description          			 道路中车道信息
     * lane_areas                        每条道路划分的车道区域数量
     * lane_id                           车道号            自定义车道号: 暂时由左到右边
    ********************************************************/
    typedef struct
    {
        std::vector<LaneArea> lane_areas;
        int lane_id;
    }LaneInfo;

    /********************************************************
     * Description                       车道平滑结果返回
     * NotLane                           不存在车道信息
     * OutLane                           目标在车道外面
     * lane_id                           目标在车道里面
    ********************************************************/
    typedef enum
    {
        NotLane,
        OutLane,
        InLane
    }LaneSmoothResult;

    /********************************************************
     * Description          			 目标所在车道区域的位置
     * target_point                      目标在车道区域中的位置
     * target_offset                     目标距离车道中心的偏移量
     * lane_id                           车道号            自定义车道号: 暂时由左到右边
     * lane_ret                          目标进行车道修正后的结果
     * time_ms                           目标的时间戳
    ********************************************************/
    typedef struct
    {
        CalibPoint       target_point;
        float            target_offset;
        int              lane_id;
        long long        time_ms;
    }LaneTargetPoint;



    /********************************************************
     * Description                       车道目标修正信息
     * splicing_id                       拼接目标的ID
     * current_lane_id                   当前车道号
     * previous_lane_id                  之前的车道号
     * change_lane_frame_count           变道帧计数
     * change_lane_frame_number          变道帧编号
     * lane_amend_points                 车道修正点的集合
     * update_target_point               更新后的目标点
     * amend_target_point                修正后的目标点
     * target_amend_point_path           目标修正点路径
     * lane_amend_res                    车道修正结果
     * is_delete                         是否删除
    ********************************************************/
    typedef struct
    {
        unsigned int        splicing_id;                
        int                 current_lane_id;            
        int                 previous_lane_id;           
        int                 change_lane_frame_count;    
        int                 change_lane_frame_number;   
        std::vector<LaneTargetPoint> lane_amend_points; 

        CalibPoint          update_target_point;        
        CalibPoint          amend_target_point;         
        std::vector<LaneTargetPoint> target_amend_point_path; 

        LaneSmoothResult    lane_amend_res;    
        bool                is_delete;
    }LaneTargetAmend;
}

