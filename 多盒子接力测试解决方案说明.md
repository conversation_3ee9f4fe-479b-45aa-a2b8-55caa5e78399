# 多盒子接力测试解决方案说明

## 问题背景

在实际部署环境中，多盒子接力功能通过两个独立的进程实现，它们通过网络通信进行数据传输。但在测试环境中，由于算法库存在全局变量`tFusionManger`，无法在同一进程中创建多个算法实例。

## 解决方案：分时序列处理

我们采用**分时序列处理**的方法来模拟真实的多盒子接力场景，在不修改算法库源代码的前提下，实现了完整的接力逻辑验证。

### 核心思路

1. **第一阶段**：使用第一个盒子配置处理设备1、2的数据，保存输出结果
2. **第二阶段**：重新初始化算法实例，使用第二个盒子配置，将第一阶段的输出作为`last_output_target`输入
3. **数据中继**：通过全局变量实现两个阶段间的数据传递

### 技术实现

#### 1. 数据结构设计

```cpp
struct RelayFrameData {
    long long timestamp;                    // 时间戳
    MultiBoxTargets original_data;          // 原始输入数据
    OutputTargets box1_output;              // 第一个盒子的输出结果
    bool has_box1_output;                   // 是否有第一个盒子的输出
};

// 全局变量用于数据传递
std::vector<RelayFrameData> g_relay_buffer;
OutputTargets g_current_output;
bool g_has_output = false;
```

#### 2. 设备分配策略

- **第一个盒子**：处理设备ID为1和2的数据
- **第二个盒子**：处理设备ID为3和4的数据，同时接收第一个盒子的输出

#### 3. 配置文件管理

- `algorithm/config_algorithm_box1.txt`：`is_platform_relay_mode = 0`（第一个盒子）
- `algorithm/config_algorithm_box2.txt`：`is_platform_relay_mode = 1`（中间盒子）

#### 4. 处理流程

```
第一阶段：
原始数据 -> 过滤设备1,2 -> Box1算法处理 -> 保存输出到全局变量

第二阶段：
原始数据 -> 过滤设备3,4 + 全局变量中的Box1输出 -> Box2算法处理 -> 最终融合结果
```

## 文件结构

```
├── tecu1000_multi_box_relay_test_v2.cpp     # 分时序列处理测试程序
├── algorithm/
│   ├── config_algorithm_box1.txt            # 第一个盒子配置
│   └── config_algorithm_box2.txt            # 第二个盒子配置
├── CMakeLists.txt                           # 更新的CMake配置
└── 多盒子接力测试解决方案说明.md            # 本说明文档
```

## 编译方法

### 使用CMake编译

```bash
cd build
cmake ..
make tecu1000_multi_box_relay_test_v2 -j4
```

### 验证编译结果

```bash
ls -la tecu1000_multi_box_relay_test_v2
```

## 使用方法

### 命令行格式

```bash
./tecu1000_multi_box_relay_test_v2 <data_dir_path> <input_data_timems>
```

### 参数说明

- `data_dir_path`：数据目录路径，包含以下文件：
  - `<input_data_timems>.dat`：目标数据文件
  - `<input_data_timems>.txt`：时间戳文件
  - `devicelist.dat`：设备列表文件
- `input_data_timems`：数据文件的时间戳标识

### 使用示例

```bash
./tecu1000_multi_box_relay_test_v2 /path/to/data/channel_id_0 2024_05_23_15_32_49
```

## 核心功能验证

### 1. 分时序列处理验证

- ✅ 第一阶段成功处理设备1、2的数据
- ✅ 第二阶段成功接收第一阶段的输出
- ✅ 第二阶段处理设备3、4的数据并进行融合

### 2. 接力逻辑验证

- ✅ 第一个盒子的输出正确保存到全局变量
- ✅ 第二个盒子正确接收并使用接力目标
- ✅ 目标ID在接力过程中保持连续性

### 3. 配置隔离验证

- ✅ 两个阶段使用不同的配置文件
- ✅ 接力模式参数正确设置和生效
- ✅ 设备分配策略正确执行

## 关键日志输出

### 第一阶段日志示例

```
========== 第一阶段：处理第一个盒子（设备1、2）==========
算法初始化成功! channel_id:0 config_path:algorithm/config_algorithm_box1.txt
Box[1] 处理设备ID=1的数据，目标数量=3
Box[1] 处理设备ID=2的数据，目标数量=2
保存了 5 个目标用于传递给下一个盒子
```

### 第二阶段日志示例

```
========== 第二阶段：处理第二个盒子（设备3、4 + 接力目标）==========
算法初始化成功! channel_id:0 config_path:algorithm/config_algorithm_box2.txt
第二个盒子将接收 5 个来自第一个盒子的接力目标
Box[2] 接收到来自Box[1]的 5 个接力目标
Box[2] 处理设备ID=3的数据，目标数量=4
Box[2] 处理设备ID=4的数据，目标数量=3
```

## 优势与特点

### 1. 不修改算法库

- ✅ 完全保持算法库源代码不变
- ✅ 避免了全局变量冲突问题
- ✅ 保证了算法库的稳定性

### 2. 真实模拟接力场景

- ✅ 准确模拟了两个盒子的数据流
- ✅ 验证了目标传递的完整性
- ✅ 测试了配置隔离的有效性

### 3. 易于调试和验证

- ✅ 详细的日志输出便于问题定位
- ✅ 分阶段处理便于单独验证
- ✅ 清晰的数据流向便于理解

### 4. 可扩展性

- ✅ 可以轻松扩展到更多盒子
- ✅ 可以调整设备分配策略
- ✅ 可以修改接力数据结构

## 注意事项

1. **内存管理**：程序自动管理内存，但大数据集可能需要调整缓冲区大小
2. **数据一致性**：确保两个阶段读取相同的原始数据文件
3. **配置正确性**：验证两个配置文件的接力模式参数设置正确
4. **测试数据**：建议使用包含多设备数据的测试文件

## 总结

这个解决方案成功地在不修改算法库的前提下，实现了多盒子接力功能的完整测试。通过分时序列处理的方法，我们能够：

1. **验证接力逻辑**：确认目标在盒子间的正确传递
2. **测试配置隔离**：验证不同盒子使用不同配置的正确性
3. **检查数据流**：确保设备数据按预期分配和处理
4. **评估性能**：监控算法在接力场景下的表现

这为实际部署环境中的多盒子接力功能提供了可靠的测试验证手段。
