#!/bin/bash

#
# 多盒子接力测试CMake编译脚本（最终版本）
# Created by JiaTao on 2024/12/18
#

echo "=========================================="
echo "多盒子接力测试编译脚本"
echo "=========================================="

# 设置构建目录
BUILD_DIR="build"
PROJECT_ROOT=$(pwd)

# 检查是否在正确的目录
if [ ! -f "CMakeLists.txt" ]; then
    echo "错误: 未找到CMakeLists.txt文件，请在项目根目录运行此脚本"
    exit 1
fi

# 创建构建目录（如果不存在）
if [ ! -d "$BUILD_DIR" ]; then
    echo "创建构建目录: $BUILD_DIR"
    mkdir -p "$BUILD_DIR"
fi

# 进入构建目录
cd "$BUILD_DIR"

echo "运行CMake配置..."
cmake .. -DCMAKE_BUILD_TYPE=Debug

# 检查CMake配置是否成功
if [ $? -ne 0 ]; then
    echo "CMake配置失败!"
    exit 1
fi

echo "编译多盒子接力测试程序..."
make tecu1000_multi_box_relay_test -j$(nproc)

# 检查编译结果
if [ $? -eq 0 ]; then
    echo ""
    echo "=========================================="
    echo "编译成功!"
    echo "=========================================="
    echo ""
    echo "可执行文件: $PROJECT_ROOT/tecu1000_multi_box_relay_test"
    echo "文件大小: $(ls -lh ../tecu1000_multi_box_relay_test | awk '{print $5}')"
    echo ""
    echo "使用方法:"
    echo "  $PROJECT_ROOT/tecu1000_multi_box_relay_test <data_dir_path> <input_data_timems>"
    echo ""
    echo "示例:"
    echo "  $PROJECT_ROOT/tecu1000_multi_box_relay_test /path/to/data/channel_id_0 2024_05_23_15_32_49"
    echo ""
    echo "配置文件:"
    echo "  - 第一个盒子: algorithm/config_algorithm_box1.txt (is_platform_relay_mode = 0)"
    echo "  - 第二个盒子: algorithm/config_algorithm_box2.txt (is_platform_relay_mode = 1)"
    echo ""
    echo "设备分配:"
    echo "  - 第一个盒子: 处理设备ID 1, 2"
    echo "  - 第二个盒子: 处理设备ID 3, 4"
    echo ""
    echo "验证要点:"
    echo "  1. 目标ID在盒子间传递时保持连续性"
    echo "  2. 轨迹拼接的准确性"
    echo "  3. 边界区域目标的平滑过渡"
    echo "  4. 多盒子接力数据流的正确性"
    echo ""
    echo "=========================================="
else
    echo "编译失败!"
    exit 1
fi

# 返回项目根目录
cd "$PROJECT_ROOT"

echo "构建完成!"
