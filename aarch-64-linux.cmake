# 交叉编译工具链配置
SET(CMAKE_SYSTEM_NAME Linux)
SET(CMAKE_SYSTEM_PROCESSOR aarch64)

# 指定交叉编译器
SET(CMAKE_C_COMPILER   aarch64-linux-gnu-gcc)
SET(CMAKE_CXX_COMPILER aarch64-linux-gnu-g++)

# 搜索库和头文件时只在宿主系统上查找
SET(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
SET(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
SET(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)

# OpenCV ARM库路径 - 修改为实际存在的路径
SET(OpenCV_DIR "/home/<USER>/libs/opencv-4.5.5/build_aarch/install")
SET(OpenCV_INCLUDE_DIR "/home/<USER>/libs/opencv-4.5.5/build_aarch/install/include/opencv4")
SET(OpenCV_LIB_DIR "/home/<USER>/libs/opencv-4.5.5/build_aarch/install/lib")

# Ascend库路径 - 使用适当的交叉编译库路径
# 应该使用ARM版本的Ascend库
SET(ASCEND_DIR "/usr/local/Ascend/ascend-toolkit/latest")
SET(ACL_INC_DIR "${ASCEND_DIR}/arm64-linux/runtime/include")
SET(ACL_LIB_DIR "${ASCEND_DIR}/aarch64-linux/devlib/aarch64") 