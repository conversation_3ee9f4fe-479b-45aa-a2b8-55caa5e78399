#define _CRT_SECURE_NO_WARNINGS   //_CRT_SECURE_NO_WARNINGS 宏来禁用strcpy方法的警告
#include "pipeline_track_fusion.h"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <chrono>
#include <ctime>
#include <cstdio>
#include "zlogger.h"

ZLOGGER_HEADER_DEFINE("ALG_PIPLINE")

using namespace tecu_r1000_0_algorithm;

trackFusionManger::trackFusionManger(const std::string &config_file)
{
	config = new Config(config_file);
	config->print_config_info();

    edgebox_id = 0;
	this->is_platform_relay_mode = false;
    ZINFO("trackFusionManger edgebox_id:%d \n",edgebox_id);
}

trackFusionManger::~trackFusionManger()
{
	ZINFO("release input_data_file!\n");
	if (nullptr != config)
	{
		delete config;
		config = nullptr;
		ZINFO("release config!\n");
	}
}

int trackFusionManger::set_param(int channel_id, AlgorithmSetType alg_set_type, void *alg_param, unsigned int cnt)
{
	if (alg_set_type == AlgorithmSetType::ALG_TYPE_BASE_DEVICE)
	{
		int param_base_deviceid  = *(int *)alg_param;
		ZINFO("set_param channel id:%d  base_device_id:%d \n \n",
				 		 channel_id, param_base_deviceid);

		if (pipeline_base_device_id.find(channel_id) == pipeline_base_device_id.end())
		{
			//不存在
			ZERROR("channel id:%d is not exist config base_device_id_map failure! \n \n",
				    channel_id);
			return -1;
		}
		else
		{
			//存在，更新
			pipeline_base_device_id[channel_id] = param_base_deviceid;
			ZINFO("channel id:%d config base_device_id update sucess! \n \n",
				   channel_id);
			if (pipeline_data_save_map.find(channel_id) == pipeline_data_save_map.end())
			{

			}
			else
			{
				pipeline_data_save_map[channel_id]->base_device_id_record_fifo(param_base_deviceid);
			}
			return 0;
		}
	}
	if (alg_set_type == AlgorithmSetType::ALG_TYPE_DEVICE_LIST)
	{
		DeivceInfo* param_device_list = (DeivceInfo *)alg_param;
		ZINFO("set_param channel id:%d  device_cnt:%d \n",
						 channel_id, cnt);

		for (unsigned int dc = 0; dc < cnt; dc++)
		{
			ZINFO("device_id:%d longitude:%f latitude:%f x:%f y:%f direction:%f enable:%d\n",
					((DeivceInfo *)alg_param + dc)->device_id,
					((DeivceInfo *)alg_param + dc)->device_longitude,
					((DeivceInfo *)alg_param + dc)->device_latitude,
                    ((DeivceInfo *)alg_param + dc)->x,
                    ((DeivceInfo *)alg_param + dc)->y,
					((DeivceInfo *)alg_param + dc)->device_direction,
					((DeivceInfo *)alg_param + dc)->device_enable);
		}
		if (pipeline_devices_list.find(channel_id) == pipeline_devices_list.end())
		{
			//不存在
			ZERROR("channel id:%d is not exist config device_list failure! \n \n",
				    channel_id);
			return -1;
		}
		else
		{
			//存在，更新
			DeviceList tranfer_device_list;
			tranfer_device_list.device_cnt = cnt;
			for (unsigned int dc = 0; dc < cnt; dc++)
			{
				tranfer_device_list.device_list[dc] = *(param_device_list + dc);
				ZINFO("pipeline_devices_list map channel id:%d device_id:%d longitude:%f latitude:%f x:%f y:%f direction:%f enable:%d \n",
												 channel_id,
												 tranfer_device_list.device_list[dc].device_id,
												 tranfer_device_list.device_list[dc].device_longitude,
												 tranfer_device_list.device_list[dc].device_latitude,
                                                 tranfer_device_list.device_list[dc].x,
                                                 tranfer_device_list.device_list[dc].y,
												 tranfer_device_list.device_list[dc].device_direction,
												 tranfer_device_list.device_list[dc].device_enable);
			}
			pipeline_devices_list[channel_id] = tranfer_device_list;
			if (pipeline_data_save_map.find(channel_id) == pipeline_data_save_map.end())
			{

			}
			else
			{
				pipeline_data_save_map[channel_id]->device_list_record_fifo(tranfer_device_list);
			}
			ZINFO("channel id:%d config device_list update sucess, device_cnt:%d! \n \n", channel_id, tranfer_device_list.device_cnt);
			return 0;
		}
	}
	if (alg_set_type == AlgorithmSetType::ALG_TYPE_CALIB_MATCH)
	{
		CalibMatch param_calib_match = *(CalibMatch *)alg_param;
		ZINFO("set_param channel id:%d  calib_match.device1_id:%d calib_match.device2_id:%d calib_match.calib_enable,calib_match.calib_status:%d calib_match.calib_match_cnt:%d \n",
			             channel_id, param_calib_match.device1_id, param_calib_match.device2_id,
			             param_calib_match.calib_enable, param_calib_match.calib_status, param_calib_match.calib_match_cnt);
		if (param_calib_match.calib_match_cnt >= config->calib_min_cnt)
		{
			for (unsigned int cm = 0; cm < param_calib_match.calib_match_cnt; cm++)
			{
				ZINFO(" device_point1.x:%f device_point1.y:%f, device_point2.x:%f, device_point2.y:%f \n",
					param_calib_match.match_points[cm].device_point1.x, param_calib_match.match_points[cm].device_point1.y,
					param_calib_match.match_points[cm].device_point2.x, param_calib_match.match_points[cm].device_point2.y);
			}
			DeviceCalibMat device_calib_mat_21;
			DeviceCalibMat device_calib_mat_12;
			int ret_calib = devices_calib(param_calib_match, device_calib_mat_21, device_calib_mat_12);
			if (ret_calib == -1)
			{
				((CalibMatch *)alg_param)->calib_status = CalibStatus::DEVICES_CALIB_FAILURE;
				ZERROR("calib error is too big, calibstatus:%d \n\n", CalibStatus::DEVICES_CALIB_FAILURE);
				return 0;
			}
			else
			{
				ZINFO("calib success !!! \n");
				(*(CalibMatch *)alg_param).calib_status = CalibStatus::DEVICES_CALIB_SUCCESS;
				ZINFO("channel id:%d device_id:%d <-> device_id:%d devices_calib update success! \n \n",
					   channel_id, param_calib_match.device1_id, param_calib_match.device2_id);
				return 0;
			}
		}
		else
		{
			ZERROR("calib point cnt is too less! \n");
			((CalibMatch *)alg_param)->calib_status = CalibStatus::DEVICES_CALIB_FAILURE;
			return 0;
		}
	}
	if (alg_set_type == AlgorithmSetType::ALG_TYPE_MATCH_LIST)
	{
		ZINFO("set_param channel id:%d device_match_cnt:%d \n",
						 channel_id, cnt);
		if (cnt >= config->device_map_min_cnt)
		{
			CalibMatch calib_match;
			ZINFO("calib_match.sizeof:%d  calib_match.match_points sizeof:%d calib_match.calib_status sizeof:%d calib_match.calib_enable sizeof:%d calib_match.device1_id sizeof:%d \n",
				   sizeof(calib_match), sizeof(calib_match.match_points),sizeof(calib_match.calib_status), sizeof(calib_match.calib_enable),sizeof(calib_match.device1_id));
			for (unsigned int cm = 0; cm < cnt; cm++)
			{
				ZINFO(" (CalibMatch *)alg_param sizeof:%d calib_enable:%d device1_id:%d device2_id:%d, calib_status:%d \n",
					sizeof((CalibMatch *)alg_param + cm),
					((CalibMatch *)alg_param + cm)->calib_enable, ((CalibMatch *)alg_param + cm)->device1_id,
					((CalibMatch *)alg_param + cm)->device2_id,   ((CalibMatch *)alg_param + cm)->calib_status);
			}
			if (alg_param == nullptr)
			{
				ZERROR("channel id:%d alg_param is nullpter! \n\n",
					    channel_id);
				return -1;
			}
			CalibMatch* param_map_list = (CalibMatch *)alg_param;
			CalibMatchList param_calib_match_list;
			param_calib_match_list.device_match_cnt = cnt;
			for (unsigned int cm = 0; cm < cnt; cm++)
			{
				param_calib_match_list.device_matchs[cm] = *(param_map_list + cm);
				ZINFO(" param_calib_match_list size calib_enable:%d device1_id:%d device2_id:%d, calib_status:%d \n",
						param_calib_match_list.device_matchs[cm].calib_enable, param_calib_match_list.device_matchs[cm].device1_id,
						param_calib_match_list.device_matchs[cm].device2_id,   param_calib_match_list.device_matchs[cm].calib_status);
			}
			if (pipeline_devices_map.find(channel_id) == pipeline_devices_map.end())
			{
				ZERROR("channel id:%d is not exist config devices_map failure! \n\n",
					    channel_id);
				return -1;
			}
			else
			{
				std::vector<DevicesCalibMat> devices_map_vec;
				int ret_devices_map = devices_map(param_calib_match_list, channel_id, devices_map_vec);
				if (ret_devices_map == -1)
				{
					ZERROR("channel id:%d is devices_map failure! \n\n",
						    channel_id);
					return -1;
				}
				ret_devices_map = devices_map_update(devices_map_vec, channel_id, alg_param, cnt);
				if (ret_devices_map == -1)
				{
					ZERROR("channel id:%d is devices_map_update failure! \n\n",
						   channel_id);
					return -1;
				}
				if (pipeline_data_save_map.find(channel_id) == pipeline_data_save_map.end())
				{

				}
				else
				{
					pipeline_data_save_map[channel_id]->device_map_record_fifo(param_calib_match_list);
				}
				print_pipeline_devices_map();
				return 0;
			}
		}
		else
		{
			ZERROR("device_match_cnt cnt is too less! \n\n");
			return -1;
		}
	}
    //雷达（雷视）与卡口相机的标定
    if(alg_set_type == AlgorithmSetType::ALG_TYPE_RC_CALIB)
    {
        RcCalibMatch param_calib_match = *(RcCalibMatch *)alg_param;
//        DeivceInfo    rc_device;							          //雷视设备信息
//        BCameraInfo   bc_device;                                    //卡口相机的设备信息
//        unsigned int  calib_match_cnt;                              //设备间标定点的数量
//        RcMatchPoints rc_calib_points[MAX_MATCH_NUM];	              //雷达与相机的标定匹配点
//        float         calib_errors[MAX_MATCH_NUM];                  //标定的误差值数组
//        CalibStatus   calib_status;                                 //设备间的标定状态
//        bool          calib_enable;                                 //设备标定组是否启用的标志, true:启用, false:不启用

//        unsigned int	 device_id;                                   //设备编号
//        double           device_longitude;                          //设备的经度 小数点精确到8位
//        double           device_latitude;                           //设备的维度 小数点精确到8位
//        double           device_direction;                          //方位角 pengge edit
//        bool             device_enable;            		   	      //设备启用标志, true:启用, false:不启用 pengge edit
        ZINFO("set_param ALG_TYPE_RC_CALIB channel id:%d rc_device_info device_id:%d device_longitude:%.2f device_latitude:%.2f device_direction:%.2f device_enable:%d \n",
              channel_id,param_calib_match.rc_device.device_id,
              param_calib_match.rc_device.device_longitude,param_calib_match.rc_device.device_latitude,param_calib_match.rc_device.device_direction,
              param_calib_match.rc_device.device_enable);
//        unsigned int device_id;                                 //卡口相机设备编号
//        float device_height;                                    //卡口相机安装高度,单位mm
//        float cmos_width;                                       //CMOS宽,单位mm
//        float cmos_height;		                                //CMOS高,单位mm
//        int origin_width;		                                //原始图像宽
//        int origin_height;		                                //原始图像高
//        int focus;
        ZINFO("set_param ALG_TYPE_RC_CALIB channel id:%d bc_device_info device_id:%d device_height:%.2f cmos_width:%.2f cmos_height:%.2f image_width:%d image_height:%d focus:%d \n",
              channel_id,param_calib_match.bc_device.device_id,param_calib_match.bc_device.device_height,
              param_calib_match.bc_device.cmos_width,param_calib_match.bc_device.cmos_height,
              param_calib_match.bc_device.image_width, param_calib_match.bc_device.image_height,
              param_calib_match.bc_device.focus);

        ZINFO("set_param ALG_TYPE_RC_CALIB channel id:%d calib_match_cnt:%d calib_status:%d calib_enable:%d \n",
              channel_id,param_calib_match.calib_match_cnt,param_calib_match.calib_status,param_calib_match.calib_enable);
        //雷达与相机标定匹配点数超过设定值,则标定失败
        if(param_calib_match.calib_match_cnt > MAX_MATCH_NUM)
        {
            ZERROR("set_param ALG_TYPE_RC_CALIB calib_match_cnt is too more! exceed to MAX_MATCH_NUM \n");
            ((CalibMatch *)alg_param)->calib_status = CalibStatus::DEVICES_CALIB_FAILURE;
            return -1;
        }

        for(unsigned int cm = 0; cm < param_calib_match.calib_match_cnt; cm++)
        {
            ZINFO("set_param ALG_TYPE_RC_CALIB image_point.x:%.2f image_point.y:%.2f, radar_point.x:%.2f, radar_point.y:%.2f \n",
                  param_calib_match.rc_calib_points[cm].image_point.x,   param_calib_match.rc_calib_points[cm].image_point.y,
                  param_calib_match.rc_calib_points[cm].radar_point.x,   param_calib_match.rc_calib_points[cm].radar_point.y);
        }

        if (param_calib_match.calib_match_cnt >= config->rv_calib_min_cnt)
        {
            RvCalibHomography rv_calib_mat;
            rv_calib_mat.rc_calib_infos = param_calib_match;
            int ret = pipeline_crhomography_map[channel_id]->rc_devices_calib(rv_calib_mat);
            if(ret == 0)
            {
                //标定成功
                ret = pipeline_crhomography_map[channel_id]->rccalib_vec_update(rv_calib_mat);
                //更新
                //标定状态更新
                ((RcCalibMatch *)alg_param)->calib_status = CalibStatus::DEVICES_CALIB_SUCCESS;
            }
            else
            {
                //标定状态更新
                ((RcCalibMatch *)alg_param)->calib_status = CalibStatus::DEVICES_CALIB_FAILURE;
            }
            //标定误差的更新
            for(unsigned int cc = 0; cc < param_calib_match.calib_match_cnt; cc++)
            {
                ((RcCalibMatch *)alg_param)->calib_errors[cc] = rv_calib_mat.rc_calib_infos.calib_errors[cc];
            }
            return 0;
        }
        else
        {
            ZERROR("set_param ALG_TYPE_RC_CALIB calib_match_cnt is too less! \n");
            ((CalibMatch *)alg_param)->calib_status = CalibStatus::DEVICES_CALIB_FAILURE;
            return 0;
        }
    }
    //边缘智能盒与卡口相机的标定
    if(alg_set_type == AlgorithmSetType::ALG_TYPE_EDGEBOX_DEVICE)
    {
        int param_edgebox_id  = *(int *)alg_param;
        ZINFO("set_param ALG_TYPE_EDGEBOX_DEVICE channel id:%d  param_edgebox_id:%d \n \n",
              channel_id, param_edgebox_id);
        edgebox_id = param_edgebox_id;
        ZINFO("set_param ALG_TYPE_EDGEBOX_DEVICE channel id:%d  edgebox_id:%d \n \n",
              channel_id, edgebox_id);
		// 设置该边缘智能盒是否需要接力
		if(cnt == 0){
			is_platform_relay_mode = false;
			ZINFO("set_param ALG_TYPE_EDGEBOX_DEVICE channel id:%d  edgebox_id:%d is_platform_relay_mode:%d \n \n",
              channel_id, edgebox_id, is_platform_relay_mode);
		}
		else{
			is_platform_relay_mode = true;
			ZINFO("set_param ALG_TYPE_EDGEBOX_DEVICE channel id:%d  edgebox_id:%d is_platform_relay_mode:%d \n \n",
              channel_id, edgebox_id, is_platform_relay_mode);
		}
        if (pipeline_input_targets_map.find(channel_id) == pipeline_input_targets_map.end())
        {
            ZERROR("set_param target_input channel id:%d pipeline_input_targets_map is not exist! \n",
                   channel_id);
            return -1;
        }
        // pipeline_input_targets_map[channel_id]->is_platform_relay_mode = is_platform_relay_mode;
        return 0;
    }
    //基于经纬度的标定
    if(alg_set_type == AlgorithmSetType::ALG_TYPE_CALIB_HDM)
    {
        DeivceInfo* param_device_list = (DeivceInfo *)alg_param;
        ZINFO("set_param ALG_TYPE_CALIB_HDM channel id:%d  device_cnt:%d \n",
              channel_id, cnt);


        for (unsigned int dc = 0; dc < cnt; dc++)
        {
            ZINFO("set_param ALG_TYPE_CALIB_HDM device_id:%d longitude:%f latitude:%f x:%f y:%f direction:%f enable:%d\n",
                  ((DeivceInfo *)alg_param + dc)->device_id,
                  ((DeivceInfo *)alg_param + dc)->device_longitude,
                  ((DeivceInfo *)alg_param + dc)->device_latitude,
                  ((DeivceInfo *)alg_param + dc)->x,
                  ((DeivceInfo *)alg_param + dc)->y,
                  ((DeivceInfo *)alg_param + dc)->device_direction,
                  ((DeivceInfo *)alg_param + dc)->device_enable);
        }
        if (pipeline_devices_list.find(channel_id) == pipeline_devices_list.end())
        {
            //不存在
            ZERROR("set_param ALG_TYPE_CALIB_HDM channel id:%d is not exist, config device_list failure! \n \n",
                   channel_id);
            return -1;
        }
        else {
            //存在，更新
            DeviceList tranfer_device_list;
            tranfer_device_list.device_cnt = cnt;
            for (unsigned int dc = 0; dc < cnt; dc++)
            {
                tranfer_device_list.device_list[dc] = *(param_device_list + dc);
                ZINFO("set_param ALG_TYPE_CALIB_HDM channel id:%d device_id:%d longitude:%f latitude:%f x:%f y:%f direction:%f enable:%d \n",
                      channel_id,
                      tranfer_device_list.device_list[dc].device_id,
                      tranfer_device_list.device_list[dc].device_longitude,
                      tranfer_device_list.device_list[dc].device_latitude,
                      tranfer_device_list.device_list[dc].x,
                      tranfer_device_list.device_list[dc].y,
                      tranfer_device_list.device_list[dc].device_direction,
                      tranfer_device_list.device_list[dc].device_enable);
            }
            pipeline_devices_list[channel_id] = tranfer_device_list;
            if (pipeline_data_save_map.find(channel_id) == pipeline_data_save_map.end())
            {

            }
            else
            {
                pipeline_data_save_map[channel_id]->device_list_record_fifo(tranfer_device_list);
            }
            std::vector<DevicesCalibMat> devices_map_vec;
            int ret_dmd = devices_map_hdm(channel_id, devices_map_vec);
            if (ret_dmd == -1)
            {
                ZERROR("set_param ALG_TYPE_CALIB_HDM  devices_map_hdm channel id:%d is devices_map failure! \n",
                       channel_id);
                return -1;
            }
            ret_dmd = devices_map_hdm_update(devices_map_vec, channel_id);
            if (ret_dmd == -1)
            {
                ZERROR("set_param ALG_TYPE_CALIB_HDM  devices_map_hdm channel id:%d is devices_map_update failure! \n",
                       channel_id);
                return -1;
            }
            ZINFO("set_param ALG_TYPE_CALIB_HDM  devices_map_hdm channel id:%d is devices_map success! \n",
                   channel_id);
            return 0;
        }
    }
    //基于车道信息的标定
    if(alg_set_type == AlgorithmSetType::ALG_TYPE_ROAD_CONFIG)
    {
        ZINFO("set_param ALG_TYPE_ROAD_CONFIG channel id:%d  road number:%d \n",
              channel_id, cnt);
        std::vector<RoadConfigAlg>* param_road_config =  (std::vector<RoadConfigAlg>*)alg_param;
        if (pipeline_input_targets_map.find(channel_id) == pipeline_input_targets_map.end())
        {
            ZERROR("set_param target_input channel id:%d pipeline_input_targets_map is not exist! \n",
                   channel_id);
            return -1;
        }
        pipeline_input_targets_map[channel_id]->laneSmooth->lane_infos_update(*param_road_config);
    }
    return 0;
}

int trackFusionManger::open_channel(algorithm_output_cb output_cb, void *user_ptr)
{
	//创建唯一的通道号
	mtx.lock();
	if (pipeline_base_device_id.size() >= (unsigned int)config->channel_id_max - 1)
	{
		ZERROR("channel cnt is too big , current channel cnt:%d! \n \n",
			    pipeline_base_device_id.size());
		return -1;
	}
	int create_channel_id = get_channel_id();
	if (pipeline_base_device_id.find(create_channel_id)     == pipeline_base_device_id.end()
	&& pipeline_devices_list.find(create_channel_id)        == pipeline_devices_list.end()
	&& pipeline_devices_map.find(create_channel_id)         == pipeline_devices_map.end()
	&& pipeline_output_callback_map.find(create_channel_id) == pipeline_output_callback_map.end()
	&& pipeline_input_targets_map.find(create_channel_id)   == pipeline_input_targets_map.end()
    && pipeline_crcalib_map.find(create_channel_id) == pipeline_crcalib_map.end()
    && pipeline_rrcalib_map.find(create_channel_id) == pipeline_rrcalib_map.end()
    && pipeline_crhomography_map.find(create_channel_id) == pipeline_crhomography_map.end())
	{
		pipeline_base_device_id.insert(std::make_pair(create_channel_id, config->init_base_device_id));

		DeviceList init_device_list;
		init_device_list.device_cnt = 0;
		pipeline_devices_list.insert(std::make_pair(create_channel_id, init_device_list));

		DevicesMap init_devices_map;
		pipeline_devices_map.insert(std::make_pair(create_channel_id, init_devices_map));

        CalibCameraRadar *init_calib_camera_radar = new CalibCameraRadar(create_channel_id, *config);
        pipeline_crcalib_map.insert(std::make_pair(create_channel_id, init_calib_camera_radar));

        CalibCameraRadarByHomograph *init_homography_camera_radar = new CalibCameraRadarByHomograph(create_channel_id, *config);
        pipeline_crhomography_map.insert(std::make_pair(create_channel_id, init_homography_camera_radar));

        TargetOutputCallback *target_output_callback = new TargetOutputCallback(output_cb, user_ptr, create_channel_id, config->callback_fps);
		target_output_callback->thread_open();
		pipeline_output_callback_map.insert(std::make_pair(create_channel_id, target_output_callback));

		TrackSplicing *track_splicing = new TrackSplicing(create_channel_id, init_device_list, init_devices_map, *config);
		pipeline_input_targets_map.insert(std::make_pair(create_channel_id, track_splicing));

		if (config->save_data == true)
		{
			DataSave *data_save = new DataSave(create_channel_id, config->files_dir_path,config->input_data_max_write_size,config->save_simulation,config->save_match_error);
			pipeline_data_save_map.insert(std::make_pair(create_channel_id, data_save));
		}

        CalibRadarRadar *calib_rradar = new CalibRadarRadar(create_channel_id, *config);
        pipeline_rrcalib_map.insert(std::make_pair(create_channel_id, calib_rradar));
        ZINFO("channel id:%d  create success! \n \n",
              create_channel_id);


		mtx.unlock();
		return create_channel_id;
	}
	else
	{
		ZERROR("channel id:%d  create failure! \n \n",
			    create_channel_id);
		mtx.unlock();
		return -1;
	}
}

int trackFusionManger::close_channel(int channel_id)
{
	ZINFO("close channel id:%d map infos! \n",
		   channel_id);
	//删除 pipeline_base_device_id
	for (auto it = pipeline_base_device_id.begin(); it != pipeline_base_device_id.end();)
	{
		if (it->first == channel_id)
		{
			pipeline_base_device_id.erase(it);
			ZINFO("close channel id:%d base_device_id! \n",
				   channel_id);
			break;
		}
		it++;
	}
	//删除pipeline_devices_list
	for (auto it = pipeline_devices_list.begin(); it != pipeline_devices_list.end();)
	{
		if (it->first == channel_id)
		{
			pipeline_devices_list.erase(it);
			ZINFO("close channel id:%d pipeline_devices_list! \n",
				   channel_id);
			break;
		}
		it++;
	}

	//删除pipeline_devices_map
	for (auto it = pipeline_devices_map.begin(); it != pipeline_devices_map.end();)
	{
		if (it->first == channel_id)
		{
			std::map<unsigned int, DevicesCalibMat>().swap(it->second.device_map_mats);
			pipeline_devices_map.erase(it);
			ZINFO("close channel id:%d pipeline_devices_map! \n",
				         channel_id);
			break;
		}
		it++;
	}

    //删除pipeline_rvcalib_map
    for(auto it = pipeline_crcalib_map.begin(); it != pipeline_crcalib_map.end();)
    {
        if(it->first == channel_id)
        {
            CalibCameraRadar* deinit_calib_cr = it->second;
            pipeline_crcalib_map.erase(it);
            if(nullptr != deinit_calib_cr)
            {
                delete deinit_calib_cr;
                deinit_calib_cr = nullptr;
            }
            ZINFO("close channel id:%d pipeline_crcalib_map! \n",
                  channel_id);
            break;
        }
        it++;
    }

    //pipeline_crhomography_map
    for(auto it = pipeline_crhomography_map.begin(); it != pipeline_crhomography_map.end();)
    {
        if(it->first == channel_id)
        {
            CalibCameraRadarByHomograph* deinit_calib_cr = it->second;
            pipeline_crhomography_map.erase(it);
            if(nullptr != deinit_calib_cr)
            {
                delete deinit_calib_cr;
                deinit_calib_cr = nullptr;
            }
            ZINFO("close channel id:%d pipeline_crhomography_map! \n",
                  channel_id);
            break;
        }
        it++;
    }

    //删除pipeline_rrcalib_map
    for(auto it = pipeline_rrcalib_map.begin(); it != pipeline_rrcalib_map.end();)
    {
        if(it->first == channel_id)
        {
            CalibRadarRadar *deinit_calib_rr = it->second;
            pipeline_rrcalib_map.erase(it);
            if(nullptr != deinit_calib_rr)
            {
                delete deinit_calib_rr;
                deinit_calib_rr = nullptr;
            }
            ZINFO("close channel id:%d pipeline_rrcalib_map! \n",
                  channel_id);
            break;
        }
        it++;
    }

    //删除pipeline_output_callback_map
	for (auto it = pipeline_output_callback_map.begin(); it != pipeline_output_callback_map.end();)
	{
		if (it->first == channel_id)
		{
			TargetOutputCallback* targetoutput_callback = it->second;
			targetoutput_callback->thread_close();
			pipeline_output_callback_map.erase(it);
			if (nullptr != targetoutput_callback)
			{
				delete targetoutput_callback;
				targetoutput_callback = nullptr;
			}
			ZINFO("close channel id:%d pipeline_output_callback_map! \n",
				         channel_id);
			break;
		}
		it++;
	}

	//删除pipeline_input_targets_map
	for (auto it = pipeline_input_targets_map.begin(); it != pipeline_input_targets_map.end();)
	{
		if (it->first == channel_id)
		{
			TrackSplicing* input_track_splicing = it->second;
			pipeline_input_targets_map.erase(it);
			if (nullptr != input_track_splicing)
			{
				delete input_track_splicing;
				input_track_splicing = nullptr;
			}
			ZINFO("close channel id:%d pipeline_input_targets_map! \n",
				   channel_id);
			break;
		}
		it++;
	}

    //删除pipeline_data_save_map
	for (auto it = pipeline_data_save_map.begin(); it != pipeline_data_save_map.end();)
	{
		if (it->first == channel_id)
		{
			DataSave *data_save = it->second;
			pipeline_data_save_map.erase(it);
			if (nullptr != data_save)
			{
				delete data_save;
//				data_save = nullptr;
			}
			ZINFO("close channel id:%d pipeline_data_save_map! \n",
				  channel_id);
			break;
		}
		it++;
	}
	return 0;
}

int trackFusionManger::start_algorithm(int channel_id)
{
	mtx.lock();
    ZINFO("start_algorithm channel_id:%d \n", channel_id);

	if (pipeline_devices_map.find(channel_id) == pipeline_devices_map.end())
	{
		ZERROR("start_algorithm channel id:%d pipeline_devices_map is not exist! \n",
			                    channel_id);
		mtx.unlock();
		return -1;
	}
	if (pipeline_input_targets_map.find(channel_id) == pipeline_input_targets_map.end())
	{
		ZERROR("start_algorithm channel id:%d pipeline_input_targets_map is not exist! \n",
			    channel_id);
		mtx.unlock();
		return -1;
	}
	if (pipeline_devices_list.find(channel_id) == pipeline_devices_list.end())
	{
		ZERROR("start_algorithm channel id:%d pipeline_devices_list is not exist! \n",
			    channel_id);
		mtx.unlock();
		return -1;
	}
    if(edgebox_id == -1)
    {
        ZERROR("start_algorithm edgebox_id is not set, edgebox_id:%d \n",
               edgebox_id);
        mtx.unlock();
        return -1;
    }

    unsigned int pipe_device_id          = pipeline_base_device_id[channel_id];
	DevicesMap pipe_devices_map          = pipeline_devices_map[channel_id];
	DeviceList pipe_device_list          = pipeline_devices_list[channel_id];
    //雷达与雷达标定加入配置信息
    //    if(pipe_device_list.device_cnt > 0)
    //    {
    //        pipeline_rrcalib_map[channel_id]->rRadarCalibConfig(pipe_device_id,
    //                                                            pipe_device_list,
    //                                                            pipe_devices_map);
    //    }

	if (pipe_devices_map.device_map_mats.size() >= 0)
	{
		//算法参数更新
		pipeline_input_targets_map[channel_id]->tracks_splicing_config_update(pipe_device_list,
                                                                              pipe_devices_map,
                                                                              pipeline_crcalib_map[channel_id],
                                                                              edgebox_id);
		//算法参数更新
		ZINFO("start_algorithm channel id:%d pipeline_devices_map device_map_mats exceed zero! \n",
			   channel_id);
		mtx.unlock();
		return 0;
	}
	else
	{
		ZERROR("start_algorithm channel id:%d pipeline_devices_map device_map_mats is zero! \n",
			    				channel_id);
		mtx.unlock();
		return -1;
	}
}



/**
 * @brief 接收多设备目标输入并进行处理
 *
 * 该函数处理指定通道的目标输入数据，包括以下逻辑：
 * 1. 检查目标输入、输出回调相关映射是否存在，不存在则返回错误；
 * 2. 若配置启用数据保存，则将输入数据写入数据保存FIFO；
 * 3. 根据是否开启自动标定，执行雷达标定并在标定结束后更新目标拼接配置，
 *    或直接调用目标拼接处理函数；
 * 4. 遍历输出回调映射，更新回调线程中的目标数据（例如拼接设备的第一个设备信息及输出目标）。
 *
 * @param channel_id           指定的通道编号
 * @param multi_device_targets 多设备目标输入数据结构体
 * @param timestamp_ms         数据的时间戳（毫秒）
 * @return int                 成功返回0，失败返回-1
 */
int trackFusionManger::target_input(int channel_id, MultiDeviceTargets multi_device_targets, long long timestamp_ms)
{
	this->is_platform_relay_mode = true;
	InputTargets input_targets = multi_device_targets.device_input[0];
	// TODO 暂时未修改 使用默认值

	if (pipeline_input_targets_map.find(channel_id) == pipeline_input_targets_map.end())
	{
		ZERROR("target_input channel id:%d pipeline_input_targets_map is not exist! \n",
			                 channel_id);
		return -1;
	}

	if (pipeline_output_callback_map.find(channel_id) == pipeline_output_callback_map.end())
	{
		ZERROR("target_input channel id:%d pipeline_output_callback_map is not exist! \n",
			    channel_id);
		return -1;
	}

	if (pipeline_data_save_map.find(channel_id) == pipeline_data_save_map.end())
	{

	}
	else
	{
		// pipeline_data_save_map[channel_id]->input_data_record_fifo_simple(multi_device_targets, timestamp_ms);
	}


    //判断是否开启自动标定的按键
    if(config->auto_devices_map == true)
    {
        pipeline_rrcalib_map[channel_id]->rRadarCalib(input_targets, timestamp_ms);
        if(pipeline_rrcalib_map[channel_id]->rrcalib_state == RrCalibState::CalibEnd)
        {
            DevicesMap pipe_devices_map          = pipeline_rrcalib_map[channel_id]->rrcalib_device_map;
            DeviceList pipe_device_list          = pipeline_devices_list[channel_id];
            pipeline_input_targets_map[channel_id]->tracks_splicing_config_update(pipe_device_list,
                                                                                  pipe_devices_map,
                                                                                  pipeline_crcalib_map[channel_id],
                                                                                  edgebox_id);
            pipeline_rrcalib_map[channel_id]->rrcalib_state =  RrCalibState::CalibNo;
        }
        if( pipeline_rrcalib_map[channel_id]->rrcalib_state ==  RrCalibState::CalibNo)
        {
            // pipeline_input_targets_map[channel_id]->tracks_splicing_main(multi_device_targets, timestamp_ms);
        }
    }
    else
    {
        pipeline_input_targets_map[channel_id]->tracks_splicing_main(multi_device_targets, timestamp_ms);

#ifdef DEBUG_DATA_SAVE
	if (pipeline_data_save_map.find(channel_id) == pipeline_data_save_map.end())
	{

	}
	else
	{
		last_output_target1.target_cnt = 0;
		last_output_target2.target_cnt = 0;

        // 初始化多设备目标数据
		multi_device_targets1.device_cnt = 0;
		multi_device_targets2.device_cnt = 0;

        // 正确初始化 MultiBoxTargets
        multi_box_targets1.last_output_target = last_output_target1;
        multi_box_targets1.multi_device_targets = multi_device_targets1;

        multi_box_targets2.last_output_target = last_output_target2;
        multi_box_targets2.multi_device_targets = multi_device_targets2;

		multi_box_targets1.multi_device_targets.device_cnt = 0;
		multi_box_targets2.multi_device_targets.device_cnt = 0;
		unsigned int box1_index = 0;
		unsigned int box2_index = 0;
		// 遍历每一个divice
		for (int mdt = 0; mdt < multi_device_targets.device_cnt; mdt++)
		{
			if (multi_device_targets.device_input[mdt].device_id == 1 || multi_device_targets.device_input[mdt].device_id == 2)
			{
				multi_box_targets1.multi_device_targets.device_input[box1_index] = multi_device_targets.device_input[mdt];
				box1_index++;
				multi_box_targets1.multi_device_targets.device_cnt++;
			}
			else if (multi_device_targets.device_input[mdt].device_id == 3 || multi_device_targets.device_input[mdt].device_id == 4)
			{
				multi_box_targets2.multi_device_targets.device_input[box2_index] = multi_device_targets.device_input[mdt];
				box2_index++;
				multi_box_targets2.multi_device_targets.device_cnt++;
			}
		}
		multi_box_targets1.last_output_target.target_cnt = 0;
		multi_box_targets2.last_output_target = pipeline_input_targets_map[channel_id]->splicing_output_targets_next;
		ZINFO("multi_box_targets1.last_output_target.target_cnt:%d \n", multi_box_targets1.last_output_target.target_cnt);
		ZINFO("multi_box_targets2.last_output_target.target_cnt:%d \n", multi_box_targets2.last_output_target.target_cnt);
		// 保存
		pipeline_data_save_map[channel_id]->input_boxdata_record_fifo_simple(multi_box_targets1, 1);
		pipeline_data_save_map[channel_id]->input_boxdata_record_fifo_simple(multi_box_targets2, 2);
		pipeline_data_save_map[channel_id]->input_timems_record_fifo_simple(timestamp_ms);

	}
#endif
    }
	for (auto it = pipeline_output_callback_map.begin(); it != pipeline_output_callback_map.end();)
	{
		for (auto ot = pipeline_input_targets_map.begin(); ot != pipeline_input_targets_map.end();)
		{
			if (it->first == ot->first)
			{
                output_mtx.lock();
                if(ot->second->splicing_devices_list.device_cnt > 0)
                {
                    //auto tran_start = std::chrono::system_clock::now();
                    it->second->thread_first_device   = ot->second->splicing_devices_list.device_list[0];
                    it->second->thread_last_device = ot->second->splicing_devices_list.device_list[ot->second->splicing_devices_list.device_cnt - 1];
                    it->second->thread_output_targets = ot->second->splicing_output_targets;
                    it->second->thread_output_targets_next = ot->second->splicing_output_targets_next;
					it->second->input_data_timestamp_ms = timestamp_ms;
                    //auto tran_end = std::chrono::system_clock::now();
                    //int tran_duration = std::chrono::duration_cast<std::chrono::milliseconds>(tran_end - tran_start).count();
                    //ZDEBUG("transfer took:%d \n", tran_duration);

                    // 通知回调线程数据已准备就绪
                    it->second->notify_data_ready();
                }
                output_mtx.unlock();
				break;
			}
			ot++;
		}
		it++;
	}
	return 0;
}


/**
 * @brief 接收多设备目标输入并处理多盒数据
 *
 * 该函数用于接收多设备目标输入数据，同时处理最后一个盒子输出目标数据。主要流程如下：
 * 1. 检查 pipeline_input_targets_map 和 pipeline_output_callback_map 是否存在对应 channel_id 的映射，若不存在则返回错误；
 * 2. 如果 pipeline_data_save_map 存在，则将输入数据写入数据保存FIFO；
 * 3. 判断是否开启自动标定：若启用自动标定则调用雷达自动标定接口，并在标定完成后更新目标拼接配置；否则直接调用目标拼接处理；
 * 4. 遍历 pipeline_output_callback_map 和 pipeline_input_targets_map 的映射，更新输出回调线程中包含的目标数据，
 *    包括第一个设备信息、最后一个设备信息，以及传递给平台和下一个盒子的输出目标数据。
 * 5. 如果 is_platform_relay_mode 为 true，则将输出目标数据传递给下一个盒子。
 *
 * @param channel_id           指定的通道编号
 * @param multi_box_targets   多设备目标输入数据结构体
 * @param timestamp_ms         数据的时间戳，单位为毫秒
 * @return int                 成功返回0，失败返回-1
 */
int trackFusionManger::target_input_multi_box(int channel_id, tecu1000_alg::MultiBoxTargets multi_box_targets, long long timestamp_ms)
{
	if(last_algorithm_call_time == 0){
		last_algorithm_call_time = timestamp_ms;
	}
	if((timestamp_ms - last_algorithm_call_time) < algorithm_call_duration){
		ZINFO("target_input_multi_box channel id:%d timestamp_ms:%lld last_algorithm_call_time:%lld < algorithm_call_duration:%f \n", channel_id, timestamp_ms, last_algorithm_call_time, algorithm_call_duration);
		return 0;
	}

	last_algorithm_call_time = timestamp_ms;
	InputTargets input_targets = multi_box_targets.multi_device_targets.device_input[0];

	if (pipeline_input_targets_map.find(channel_id) == pipeline_input_targets_map.end())
	{
		ZERROR("target_input channel id:%d pipeline_input_targets_map is not exist! \n",
			                 channel_id);
		return -1;
	}

	if (pipeline_output_callback_map.find(channel_id) == pipeline_output_callback_map.end())
	{
		ZERROR("target_input channel id:%d pipeline_output_callback_map is not exist! \n",
			    channel_id);
		return -1;
	}

	if (pipeline_data_save_map.find(channel_id) == pipeline_data_save_map.end())
	{

	}
	else
	{
		pipeline_data_save_map[channel_id]->input_boxdata_record_fifo_simple(multi_box_targets, 0);
		pipeline_data_save_map[channel_id]->input_timems_record_fifo_simple(timestamp_ms);
	}

    // 判断是否开启自动标定的按键
    if(config->auto_devices_map == true)
    {
        pipeline_rrcalib_map[channel_id]->rRadarCalib(input_targets, timestamp_ms);
        if(pipeline_rrcalib_map[channel_id]->rrcalib_state == RrCalibState::CalibEnd)
        {
            DevicesMap pipe_devices_map          = pipeline_rrcalib_map[channel_id]->rrcalib_device_map;
            DeviceList pipe_device_list          = pipeline_devices_list[channel_id];
            pipeline_input_targets_map[channel_id]->tracks_splicing_config_update(pipe_device_list,
                                                                                  pipe_devices_map,
                                                                                  pipeline_crcalib_map[channel_id],
                                                                                  edgebox_id);
            pipeline_rrcalib_map[channel_id]->rrcalib_state =  RrCalibState::CalibNo;
        }
        if( pipeline_rrcalib_map[channel_id]->rrcalib_state ==  RrCalibState::CalibNo)
        {
            pipeline_input_targets_map[channel_id]->tracks_splicing_main(multi_box_targets.multi_device_targets, timestamp_ms);
        }
    }
    else
    {
		try
		{
			pipeline_input_targets_map[channel_id]->tracks_splicing_main(multi_box_targets, timestamp_ms);
		}
		catch(const std::exception& e)
		{
			ZERROR("target_input_multi_box channel id:%d error:%s \n", channel_id, e.what());
		}
    }
	for (auto it = pipeline_output_callback_map.begin(); it != pipeline_output_callback_map.end();)
	{
		for (auto ot = pipeline_input_targets_map.begin(); ot != pipeline_input_targets_map.end();)
		{
			if (it->first == ot->first)
			{
                output_mtx.lock();
                if(ot->second->splicing_devices_list.device_cnt > 0)
                {
                    // 自动传递第一个和最后一个设备信息，以及输出目标数据
                    it->second->thread_first_device   = ot->second->splicing_devices_list.device_list[0];
                    it->second->thread_last_device = ot->second->splicing_devices_list.device_list[ot->second->splicing_devices_list.device_cnt - 1];
                    it->second->thread_output_targets = ot->second->splicing_output_targets;
                    it->second->thread_output_targets_next = ot->second->splicing_output_targets_next;
					it->second->input_data_timestamp_ms = ot->second->splicing_timestamp_ms;

                    // 通知回调线程数据已准备就绪
                    it->second->notify_data_ready();
                }
                output_mtx.unlock();
				break;
			}
			ot++;
		}
		it++;
	}
	return 0;
}

int trackFusionManger::get_channel_id()
{
	if (pipeline_base_device_id.size() == 0)
	{
		channel_id_count = config->channel_id_init;
	}
	channel_id_count++;
	if (channel_id_count > config->channel_id_max)
	{
		channel_id_count = config->channel_id_min;
	}
	if (pipeline_base_device_id.size() > 0)
	{
		for (int ci = 0; ci < config->channel_id_max; ci++)
		{
			bool exist_repeat_channel_id = false;
			for (auto it = pipeline_base_device_id.cbegin(); it != pipeline_base_device_id.cend(); ++it)
			{
				auto& pair = *it; // 或者直接使用it->first和it->second
				if (channel_id_count == pair.first)
				{
					channel_id_count ++;
					exist_repeat_channel_id = true;
					if (channel_id_count > config->channel_id_max)
					{
						channel_id_count = config->channel_id_min;
					}
					break;
				}
			}
			if (exist_repeat_channel_id == false)
			{
				break;
			}
		}
	}
	return channel_id_count;
}

std::string trackFusionManger::get_current_time_asstring()
{
	// 获取当前时间（以time_point<system_clock>格式）
	auto now = std::chrono::system_clock::now();

	// 将time_point转换为time_t（从epoch开始的秒数）
	std::time_t now_c = std::chrono::system_clock::to_time_t(now - std::chrono::hours(0)); // 假设我们不需要时区调整

	// 创建一个tm结构体以存储本地时间
	std::tm local_tm = *std::localtime(&now_c); // 注意这里我们直接赋值而不是使用指针

	// 创建一个ostringstream对象用于构建字符串
	std::ostringstream oss;

	// 使用put_time将tm结构体格式化为字符串
	oss << std::put_time(&local_tm, "%Y_%m_%d_%H_%M_%S");

	// 返回生成的字符串
	return oss.str();
}

void trackFusionManger::get_match_error(int channel_id, unsigned int id1, unsigned int id2, float x_error, float y_error, void *track_fusion_manager)
{
	if (nullptr != track_fusion_manager)
	{
		trackFusionManger *p = (trackFusionManger *)track_fusion_manager;
		if (p->pipeline_data_save_map.find(channel_id) == p->pipeline_data_save_map.end())
		{

		}
		else
		{
#ifdef DEBUG
			ZDEBUG("get_match_error channel id:%d id1:%d id2:%d x_error:%f y_error:%f \n",
				                   channel_id, id1, id2, x_error, y_error);
#endif
			p->pipeline_data_save_map[channel_id]->match_error_record_fifo(id1, id2, x_error, y_error);
		}
	}
}

void trackFusionManger::get_road_direction_info(int channel_id, RoadDirection road_direction_info,std::string file_path, void *track_fusion_manager)
{
    if(nullptr != track_fusion_manager)
    {
        trackFusionManger *p = (trackFusionManger *)track_fusion_manager;
        if(p->pipeline_data_save_map.find(channel_id) == p->pipeline_data_save_map.end())
        {

        }
        else
        {
            ZINFO("Save Road Direction INFO! \n");
            p->pipeline_data_save_map[channel_id]->road_direction_record_fifo(road_direction_info, file_path);
        }
    }
}