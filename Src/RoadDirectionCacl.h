//
// Created by Administrator on 2024/10/28.
//
#pragma once
#include <iostream>
#include <map>
#include <vector>
#include "calib_header.h"
#include "tracks_splicing_header.h"

namespace tecu_r1000_0_algorithm
{
    class RoadDirectionCacl
    {
    public:
        // 构造函数，初始化道路方向计算器
        // @param channel_id: 通道ID
        // @param file_folder_path: 文件保存路径
        RoadDirectionCacl(int channel_id, std::string file_folder_path);
        ~RoadDirectionCacl();

        // 获取道路方向
        void print_road_direction();

        // 获取设备方向映射
        // @param splicing_input_target_vec_: 拼接输入目标向量
        // @param min_detection_distance: 最小检测距离
        // @param max_detection_distance: 最大检测距离
        // @param device_lists: 设备列表
        // @param lane_infos: 车道信息 手动绘制
        void get_device_direction_map(std::vector<SplicingInputTarget> splicing_input_target_vec_,
                                      float min_detection_distance,
                                      float max_detection_distance,
                                      DeviceList device_lists,
                                      const std::vector<LaneInfo> lane_infos);


        void get_device_direction_map(std::vector<SplicingInputTarget> splicing_input_target_vec_,
                                float min_detection_distance,
                                float max_detection_distance,
                                DeviceList device_lists);


        // 获取道路方向
        // @param device_id: 设备ID
        // @param coor_in_base: 基准坐标系中的坐标点
        // @param road_direction: 输出的道路方向
        // @return: 0表示成功，-1表示失败
        int get_road_direction(unsigned int device_id, CalibPoint coor_in_base, float &road_direction);

        // 道路方向修正
        // @param device_id: 设备ID
        // @param coor_in_base: 基准坐标系中的坐标点
        // @param road_direction: 待修正的道路方向
        // @return: 修正后的道路方向
        float road_direction_amend(unsigned int device_id, CalibPoint coor_in_base, float road_direction);

        float road_direction_amend(unsigned int device_id, CalibPoint coor_in_base, std::vector<TargetOutput> target_output_path, float target_direction);

        // 道路方向状态
        RoadDirectionState road_direction_state;

    private:
        DeviceList road_device_lists;      // 道路设备列表
        RoadDirection road_direction;       // 道路方向信息

        // 更新设备列表
        // @return: 0表示设备列表发生变化，-1表示未变化
        int device_lists_update(DeviceList cur_device_lists, DeviceList &road_device_lists);

        float direction_grid_height;        // 方向网格高度
        float direction_grid_number;        // 方向网格数量
        float direction_grid_rate_threshold;// 方向网格率阈值
        int road_range_target_number;      // 道路范围目标数量
        int road_range_target_count;       // 道路范围目标计数

        int road_channel_id;               // 道路通道ID
        std::string file_save_path;        // 文件保存路径
        std::string road_device_list_save_path; // 道路设备列表保存路径

        // 打印设备列表信息
        void print_device_list(DeviceList road_device_lists_);

        // 打印道路方向信息
        void print_road_direction(RoadDirection road_direction_);

        // 保存道路方向信息
        void road_direction_save();

        // 加载道路方向信息
        void road_direction_load(DeviceList &road_device_lists_, RoadDirection &road_direction_, RoadDirectionState &road_direction_state_);

        // 清除设备方向向量
        void device_direction_vec_clear(RoadDirection &road_direction);

        // 创建设备方向向量
        RoadDirectionState device_direction_vec_create(RoadDirection &road_direction,
                                                       DeviceList device_list,
                                                       float device_detect_range,
                                                       float direction_grid_height,
                                                       float direction_grid_number,
                                                       int &road_range_target_number_,
                                                       int &road_range_target_count_);

        // 更新道路范围
        RoadDirectionState road_range_update(RoadDirection &road_direction,
                                             std::vector<SplicingInputTarget> splicing_input_target_vec_,
                                             int &road_range_target_number_,
                                             int &road_range_target_count_);

        // 更新道路范围 从车道信息中获取
        RoadDirectionState road_range_update(RoadDirection &road_direction,
                                                           const std::vector<LaneInfo> lane_infos);

        // 生成道路范围网格
        RoadDirectionState road_range_mesh_generation(RoadDirection &road_direction_,
                                                      float direction_grid_height_);

        // 生成道路范围网格 从车道信息中获取
        RoadDirectionState road_range_mesh_generation(RoadDirection &road_direction_,
                                                      const std::vector<LaneInfo> lane_infos);

        // 更新道路方向向量
        RoadDirectionState road_direction_vec_update(RoadDirection &road_direction_,
                                                     std::vector<SplicingInputTarget> splicing_input_target_vec);

        // 更新道路方向计算
        RoadDirectionState road_direction_calc_update(RoadDirection &road_direction_,
                                                      float direction_grid_number);

        // 网格向量去噪
        void grid_vec_denoise(std::vector<GridInfo> &grid_infos,
                              float denoise_rate);

        // 道路方向去噪
        void road_direction_denoise(std::vector<float> &direction_vec,
                                    std::vector<float> &coor_vec,
                                    float denoise_rate);

        // 计算向量平均值
        float cal_vector_mean(const std::vector<float>& data);
        
        // 计算向量的平均绝对偏差(MAD)
        float cal_vector_mad(const std::vector<float>& data);
        
        // 计算MAD均值
        float cal_mad_mean(const std::vector<float>& data, float threshold_multiplier);
        
        // 计算向量标准差
        float cal_vector_std(const std::vector<float>& data);

        // 从流中读取浮点数
        float readFloatFromStream(std::istream& is);
        
        // 从流中读取整数
        int readIntFromStream(std::istream& is);
        
        // 从流中读取网格信息
        GridInfo readGridInfoFromStream(std::istream& is);
        
        // 从流中读取道路方向信息
        RoadDirection readRoadDirectionFromStream(std::istream& is);
    };
}