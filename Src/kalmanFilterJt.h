#ifndef TRVF_8223_KALMAN_FILTER_JT_H
#define TRVF_8223_KALMAN_FILTER_JT_H

#include <vector>
#include <iostream>
#include <cmath>
#include "Dense"

using namespace Eigen;

class KalmanFilterJT
{
public:
    KalmanFilterJT()
    {
        initial_speed_ = 23.0;                  // 设置初始速度为23.0
        d_ = 0.0;                               // 初始化当前时间步的位移为0
        v_ = initial_speed_;                    // 初始化速度为初始速度

        // 初始方向设为沿x轴的单位向量
        direction_ = Eigen::Vector2d(1, 0);

        // 定义状态转移矩阵 F，仅考虑位移和速度
        // 状态向量为 [d, v]
        F_ = Eigen::Matrix2d::Identity();
        F_(0, 1) = dt_;                          // F矩阵中 (0,1) 元素设置为时间步长 dt，表示 d = d + v * dt

        // 定义观测矩阵 H，仅测量位移 d
        H_ = Eigen::Matrix<double, 1, 2>::Zero();
        H_(0, 0) = 1.0;                          // H矩阵中 (0,0) 元素设置为1，表示观测值对应 d_

        // 定义过程噪声协方差矩阵 Q，假设位移和速度的过程噪声较小
        Q_ = Eigen::Matrix2d::Zero();
        Q_(0, 0) = 1e-2;                         // 位移过程噪声方差
        Q_(1, 1) = 1e-2;                         // 速度过程噪声方差

        // 定义测量噪声协方差矩阵 R，假设测量噪声较低
        R_ = Eigen::Matrix<double, 1, 1>::Identity() * 1e-1;

        // 初始化估计协方差矩阵 P 为单位矩阵
        P_ = Eigen::Matrix2d::Identity();

        // 初始化其他成员变量
        is_first_measurement_ = true;            // 标记是否为第一个测量
        last_x_ = 0.0;                           // 上一帧的测量x坐标
        last_y_ = 0.0;                           // 上一帧的测量y坐标
        last_timestamp_ = 0;                     // 上一帧的时间戳
        accumulated_position_ = Eigen::Vector2d(0, 0); // 初始化累积位置为原点
    }

    void pred(const double x_meas, const double y_meas, const long long timestamp)
    {
        if (is_first_measurement_)
        {
            // 第一次测量时进行初始化，不进行预测和更新
            Eigen::Vector2d current_meas(x_meas, y_meas);   // 当前测量点
            d_ = (current_meas - accumulated_position_).dot(direction_); // 计算沿当前方向的位移
            v_ = initial_speed_;                            // 设置初始速度
            last_x_ = x_meas;                                // 更新上一帧测量x坐标
            last_y_ = y_meas;                                // 更新上一帧测量y坐标
            last_timestamp_ = timestamp;                     // 更新上一帧时间戳
            accumulated_position_ = current_meas;            // 设置累积位置为当前测量点
            return;
        }

        // 计算时间步长 dt，单位为秒
        dt_ = static_cast<double>(timestamp - last_timestamp_) * 0.001; // 假设时间戳为毫秒
        last_timestamp_ = timestamp;                                     // 更新上一帧时间戳

        // 计算当前测量与上一测量的位移差值
        Eigen::Vector2d current_meas(x_meas, y_meas);                   // 当前测量点
        Eigen::Vector2d prev_meas(last_x_, last_y_);                    // 上一测量点
        Eigen::Vector2d delta_meas = current_meas - prev_meas;          // 位移差值

        // 更新方向向量，仅当位移差值非零时进行更新
        if (delta_meas.norm() > 1e-6) // 使用小阈值避免除零
        {
            direction_ = delta_meas.normalized();                       // 更新方向为位移差值的单位向量
        }
        // 否则，保持之前的方向不变

        // 计算测量值 z，为位移差值在当前方向上的投影
        double z = delta_meas.dot(direction_);

        // 更新状态转移矩阵 F 的时间步长部分
        F_(0, 1) = dt_;

        // 预测步骤：预测下一个状态
        Eigen::Vector2d state_pred = F_ * Eigen::Vector2d(d_, v_);     // 预测状态向量
        d_ = state_pred(0);                                            // 更新预测位移
        v_ = state_pred(1);                                            // 更新预测速度

        // 预测协方差矩阵 P
        P_ = F_ * P_ * F_.transpose() + Q_;

        // 计算观测残差
        double y_residual = z - d_;

        // 计算卡尔曼增益 K
        Eigen::Matrix<double, 2, 1> K = P_ * H_.transpose() * (H_ * P_ * H_.transpose() + R_).inverse();

        // 更新状态向量 [d, v] = [d, v] + K * y_residual
        Eigen::Vector2d state_updated = Eigen::Vector2d(d_, v_) + K * y_residual;
        d_ = state_updated(0);                                        // 更新位移
        v_ = state_updated(1);                                        // 更新速度

        // 更新协方差矩阵 P = (I - K * H) * P
        Eigen::Matrix2d I = Eigen::Matrix2d::Identity();              // 单位矩阵
        P_ = (I - K * H_) * P_;

        // 更新累积位置，累加当前位移在方向上的分量
        accumulated_position_ += d_ * direction_;

        // 更新上一帧的测量值
        last_x_ = x_meas;                                             // 更新上一帧测量x坐标
        last_y_ = y_meas;                                             // 更新上一帧测量y坐标
    }

    Eigen::Vector4d get_state()
    {
        // 返回当前估计的位置信息和速度，保持接口一致性，后两个分量未使用
        if(is_first_measurement_)
        {
            is_first_measurement_ = false;
            return Eigen::Vector4d(last_x_, last_y_, 0, 0);        // 返回初始位置
        }
        // 计算实际位置为上一测量位置加上当前位移方向上的位移
        // return Eigen::Vector4d(accumulated_position_(0), accumulated_position_(1), 0, 0);  // 加速的的方差更小 但是会有位置偏移
        return Eigen::Vector4d(last_x_ + d_ * direction_(0), last_y_ + d_ * direction_(1), 0, 0); // 加速度的方差大 但是位置偏移小
    }

private:
    double dt_;                  // 时间步长 (秒)
    double d_;                   // 当前时间步的位移
    double v_;                   // 当前速度

    Eigen::Matrix2d F_;          // 状态转移矩阵
    Eigen::Matrix<double, 1, 2> H_; // 观测矩阵
    Eigen::Matrix2d Q_;          // 过程噪声协方差矩阵
    Eigen::Matrix<double, 1, 1> R_; // 测量噪声协方差矩阵
    Eigen::Matrix2d P_;          // 估计协方差矩阵

    Eigen::Vector2d direction_;  // 动态更新的运动方向单位向量

    bool is_first_measurement_;  // 是否为第一个测量
    double initial_speed_;       // 初始速度
    double last_x_, last_y_;     // 上一帧的测量位置
    long long last_timestamp_;   // 上一帧的时间戳

    Eigen::Vector2d accumulated_position_; // 累积位置
};

#endif // TRVF_8223_KALMAN_FILTER_JT_H
