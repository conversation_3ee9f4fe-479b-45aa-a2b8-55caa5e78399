﻿// algorithm_lib.cpp : 此文件包含 "main" 函数。程序执行将在此处开始并结束。
//
#include <string.h>
#include <vector>
#include "tecu1000_algorithm_header.h"
#include "zlogger.h"
#include "pipeline_track_fusion.h"


ZLOGGER_HEADER_DEFINE("ALG_LIB")

using namespace std;
using namespace tecu_r1000_0_algorithm;

//轨迹拼接管理类
trackFusionManger * tFusionManger = nullptr;


//算法初始化，返回 0:成功; -1:失败；
int tecu1000_alg::algorithm_init_(char *version)
{
	if (nullptr == tFusionManger)
	{
		std::string config_path = "algorithm/config_algorithm.txt";
		std::string log_path    = "algorithm/log/tecu_1000_0_algor.log";
		tFusionManger = new trackFusionManger(config_path);
		//算法的版本信息
		strcpy(version, tFusionManger->config->algor_version.c_str());

		// 调用接口进行日志初始化
        ZINFO("alg_version: %s config_path:%s log_path:%s \n",
              version, config_path.c_str(), log_path.c_str());
        ZINFO("-------------------- Algorithm init success--------------------  \n");
		return 0;
	}
	else
	{
        ZINFO("-------------------- Algorithm init failure--------------------  \n");
		return -1;
	}
}

/*
 * @Name: algorithm_init_with_config
 * @Description: 使用自定义配置文件路径初始化算法
 *
 * @Input
 * version: 算法版本信息输出缓冲区
 * config_path: 自定义配置文件路径
 *
 * @Output
 * 0: 成功
 * -1: 失败
 *
 * @Edit History
 * Date: 2024/12/18
 * Time: 10:00
 * Author: JiaTao
 * Content: 添加支持自定义配置文件的初始化函数
 */
int tecu1000_alg::algorithm_init_with_config_(char *version, const char *config_path)
{
	if (nullptr == tFusionManger)
	{
		std::string config_file_path = config_path ? config_path : "algorithm/config_algorithm.txt";
		std::string log_path = "algorithm/log/tecu_1000_0_algor.log";
		tFusionManger = new trackFusionManger(config_file_path);
		//算法的版本信息
		strcpy(version, tFusionManger->config->algor_version.c_str());

		// 调用接口进行日志初始化
        ZINFO("alg_version: %s config_path:%s log_path:%s \n",
              version, config_file_path.c_str(), log_path.c_str());
        ZINFO("-------------------- Algorithm init with config success--------------------  \n");
		return 0;
	}
	else
	{
        ZINFO("-------------------- Algorithm init with config failure--------------------  \n");
		return -1;
	}
}


//算法创建通道，失败返回:-1；成功返回channel_id：>=0;
int tecu1000_alg::algorithm_open_channel_(algorithm_output_cb output_cb, void *user_ptr)
{
	int ret_oc = tFusionManger->open_channel(output_cb, user_ptr);
	return ret_oc;
}


//算法关闭通道，返回 0:成功; -1:失败；
int tecu1000_alg::algorithm_close_channel_(int channel_id)
{
	int ret_cc = tFusionManger->close_channel(channel_id);
	return ret_cc;
}

//算法参数设置(标定/重置/传参)，返回 0:成功; -1:失败
int tecu1000_alg::algorithm_set_param_(int channel_id, AlgorithmSetType alg_set_type, void *alg_param, unsigned int cnt)
{
	int ret_set_param = tFusionManger->set_param(channel_id, alg_set_type, alg_param, cnt);
	return ret_set_param;
}


//算法启动，返回 0:成功; -1:失败; 启用新配置
int tecu1000_alg::algorithm_start_(int channel_id)
{
	//默认算法启动
	int ret_start = tFusionManger->start_algorithm(channel_id);
	return ret_start;
}

//算法目标输入，返回 0:成功; -1:失败；algorithm_start成功后可输入数据
int tecu1000_alg::algorithm_target_input_(int channel_id, MultiDeviceTargets *multi_device_targets, long long timestamp_ms)
{

#ifdef DEBUG
    ZDEBUG("InputTargets Print channel_id:%d device_id:%d input_targets cnt:%d time_ms:%lld\n",
                    channel_id, input_targets->device_id, input_targets->target_cnt, timestamp_ms);
    for (unsigned int c = 0; c < input_targets->target_cnt; c++)
    {
        if (input_targets->targets.target_devices[c].y > tFusionManger->config->min_detect_distance
         && input_targets->targets.target_devices[c].y < tFusionManger->config->max_detect_distance)
        {
            ZDEBUG("id:%d x:%.2f y:%.2f s:%.2f n_plate:%s np_s:%.2f p_color:%d pc_s:%.2f v_color:%d vc_s:%.2f v_type:%d vt_s:%.2f t_type:%d tt_s:%.2f\n",
                            input_targets->targets.target_devices[c].id,
                            input_targets->targets.target_devices[c].x,
                            input_targets->targets.target_devices[c].y,
                            input_targets->targets.target_devices[c].speed,

                            input_targets->targets.target_devices[c].number_plate,
                            input_targets->targets.target_devices[c].np_score,

                            input_targets->targets.target_devices[c].plate_color,
                            input_targets->targets.target_devices[c].pc_score,

                            input_targets->targets.target_devices[c].vehicle_color,
                            input_targets->targets.target_devices[c].vc_score,

                            input_targets->targets.target_devices[c].vehicle_type,
                            input_targets->targets.target_devices[c].vt_score,

                            input_targets->targets.target_devices[c].target_type,
                            input_targets->targets.target_devices[c].tt_score
            );
        }
    }
#endif

    /*
    ZDEBUG("\n\n\nalgorithm_target_inputchannel_id:%d device_cnt:%d timestamp_ms:%lld \n", channel_id, multi_device_targets->device_cnt, timestamp_ms);
    for(int i = 0; i < multi_device_targets->device_cnt; i++)
    {
        ZDEBUG("target_input device_id:%d target_cnt:%d timestamp_ms:%lld \n", multi_device_targets->device_input[i].device_id, multi_device_targets->device_input[i].target_cnt, multi_device_targets->device_input[i].timestamp_ms);

        for(int j = 0; j < multi_device_targets->device_input[i].target_cnt; j++)
        {
                ZDEBUG("input_targets id:%d x:%.2f y:%.2f s:%.2f n_plate:%s np_s:%.2f p_color:%d pc_s:%.2f v_color:%d vc_s:%.2f v_type:%d vt_s:%.2f t_type:%d tt_s:%.2f\n",
                            multi_device_targets->device_input[i].targets.target_devices[j].id,
                            multi_device_targets->device_input[i].targets.target_devices[j].x,
                            multi_device_targets->device_input[i].targets.target_devices[j].y,
                            multi_device_targets->device_input[i].targets.target_devices[j].speed,

                            multi_device_targets->device_input[i].targets.target_devices[j].number_plate,
                            multi_device_targets->device_input[i].targets.target_devices[j].np_score,

                            multi_device_targets->device_input[i].targets.target_devices[j].plate_color,
                            multi_device_targets->device_input[i].targets.target_devices[j].pc_score,

                            multi_device_targets->device_input[i].targets.target_devices[j].vehicle_color,
                            multi_device_targets->device_input[i].targets.target_devices[j].vc_score,

                            multi_device_targets->device_input[i].targets.target_devices[j].vehicle_type,
                            multi_device_targets->device_input[i].targets.target_devices[j].vt_score,

                            multi_device_targets->device_input[i].targets.target_devices[j].target_type,
                            multi_device_targets->device_input[i].targets.target_devices[j].tt_score
            );

        }
    }
*/
    int ret_input = tFusionManger->target_input(channel_id, *multi_device_targets, timestamp_ms);
	return 0;
}

//算法目标输入，返回 0:成功; -1:失败；algorithm_start成功后可输入数据
int tecu1000_alg::algorithm_target_input_multi_box_(int channel_id,
                            tecu1000_alg::MultiBoxTargets *multi_box_targets,
                            long long timestamp_ms)
{

    int ret_input = tFusionManger->target_input_multi_box(channel_id, *multi_box_targets, timestamp_ms);
	return 0;
}


//算法库去初始化，返回 0:成功;- 1:失败
int tecu1000_alg::algorithm_deinit_()
{
	if (nullptr != tFusionManger)
	{
		delete tFusionManger;
		tFusionManger = nullptr;
        ZINFO("release tFusionManger! \n");
	}
    ZINFO("release libLogger_Deinitialize!\n");
    ZINFO("-------------------- Algorithm deinit success-------------------- \n");

	return 0;
}







