#pragma once
#include <thread>
#include "tecu1000_algorithm_header.h"
#include "kalmanFilterJt.h"
#include <mutex>
#include <condition_variable>
#include <iostream>
#include <string.h>

// #define DEBUG_DATA_OUTPUT

using namespace tecu1000_alg;
namespace tecu_r1000_0_algorithm
{
    /********************************************************
     * Description          			 输出目标的平滑性处理
     * output_target                     输出目标信息
     * output_target_kalman              输出目标的滤波器
     * splicing_smooth_x                 平滑后的x轴值
     * splicing_smooth_y                 平滑后的y轴值
     * smooth_longitude                  平滑后的经度
     * smooth_latitude                   平滑后的维度
    ********************************************************/
    typedef struct
    {
        OutputTarget output_target;
        KalmanFilterJT output_target_kalman;
        float splicing_smooth_x;
        float splicing_smooth_y;
        double smooth_longitude;
        double smooth_latitude;
    }TargetOutputSmooth;



	class TargetOutputCallback
	{
	public:
		TargetOutputCallback(algorithm_output_cb output_cb, void *user_ptr, int channel_id, int callback_fps);
		~TargetOutputCallback();

        // 1. 传递给平台的信息
		OutputTargets thread_output_targets;

        // 2. 传递给下一个盒子的信息
        OutputTargets thread_output_targets_next;

        // 3. 第一个设备信息
        DeivceInfo     thread_first_device;

        // 4. 最后一个设备信息
        DeivceInfo     thread_last_device;

        // 输入数据的时间
        long long input_data_timestamp_ms;

        //输出目标平滑性
        std::vector<TargetOutputSmooth> target_output_smooth_vec;

        void target_output_smooth_vec_update(std::vector<TargetOutputSmooth> &target_output_smooth_vec_,
                                             OutputTargets &thread_output_targets_,
                                             long long time_ms);

        /*
         * @Name: notify_data_ready
         * @Description: 通知回调线程数据已准备就绪，触发回调执行
         *
         * @Input
         * 无
         *
         * @Output
         * 无
         *
         * @Edit History
         * Date: 2025/01/21
         * Time: 14:30
         * Author: JiaTao
         * Content: 添加生产者-消费者模式的事件通知机制
         */
        void notify_data_ready();

        //
		void thread_open();
		//�̹߳ر�
		void thread_close();
	private:

		//�㷨�ص������
		void algorithm_fusion_output_local();

		//�ص�����ע��
		algorithm_output_cb event_cb_local;
		void *user_ptr_local;
		//�ص�����ע��



		//�̵߳�ͨ����
		int channel_id;
		int callback_fps;

		std::thread *thread_output_callback;

		//线程控制标志位
		bool         thread_is_open;

		// 生产者-消费者模式的同步机制
		std::mutex callback_mutex;
		std::condition_variable callback_condition;
		bool data_ready;

	};
}


