#pragma once
#include <thread> 
#include <mutex>
#include <iostream>
#include <string.h>
#include "Dense"
#include "tecu1000_algorithm_header.h"
#include "calib_header.h"
#include "target_outputs_callback.h"
#include "calib_radar_camera.h"
#include "config.h"
#include "data_save.h"
#include "tracks_splicing.h"
#include "calib_radar_radar.h"
#include "vehicleLaneSmoothing.h"

using namespace tecu1000_alg;
#define SHOOTING_SOLUTION
//#define FRONT_BACK_SOLUTION
// #define DEBUG

// #define DEBUG1
#define DEBUG_DATA_SPLICING_INPUT
// #define DEBUG_DATA_SPLICING_OUTPUT
#define DEBUG_DATA_MULTI_BOX_OUTPUT

// #define DEBUG_DATA_SAVE

// #define DEBUG_DATA_THREAD_OUTPUT


namespace tecu_r1000_0_algorithm
{
	// 跟踪融合管理类，用于处理多设备数据的融合与校准
	class trackFusionManger 
	{
	public:
		// TODO: 调用间隔 保证算法两此调用时间大于100ms
		double algorithm_call_duration = 50;
		double last_algorithm_call_time = 0;

		// 构造函数，参数为配置文件路径
		trackFusionManger(const std::string &config_file);
		// 析构函数，负责资源释放
		~trackFusionManger();

		// 算法配置文件
		Config *config = nullptr;

        // 全局互斥锁，用于保护共享数据
        std::mutex mtx;

		// 全局互斥锁，用于保护共享数据
		std::mutex output_mtx;

		// 设置参数、校准与融合
		// 参数：channel_id - 通道编号, alg_set_type - 算法设置类型, alg_param - 算法参数指针, cnt - 参数数量
		int set_param(int channel_id, AlgorithmSetType alg_set_type, void *alg_param, unsigned int cnt);

		// 打开通道，成功返回通道ID (非负值)，失败返回 -1
		int open_channel(algorithm_output_cb output_cb, void *user_ptr);

		// 关闭通道，成功返回 0，失败返回 -1
		int close_channel(int channel_id);

		// 算法目标输入，成功返回 0，失败返回 -1
		int target_input(int channel_id, MultiDeviceTargets multi_device_targets, long long timestamp_ms);

		// 算法目标输入，成功返回 0，失败返回 -1
		int target_input_multi_box(int channel_id, tecu1000_alg::MultiBoxTargets multi_box_targets, long long timestamp_ms);

		// 启动算法，成功返回 0，失败返回 -1
		int start_algorithm(int channel_id);

		// 打印3x3矩阵信息
		static void print_matrix3d_info(Eigen::Matrix3d mat3d, std::string &mat_str);

        // 打印2x2矩阵信息
        static void print_matrix2d_info(Eigen::Matrix2d mat2d, std::string &mat_str);

        // 打印二维向量信息
        static void print_vector_info(Eigen::Vector2d vect2d, std::string &vect_str);

        // 打印 OpenCV 的 Mat 矩阵信息
        static void print_cvmat_info(cv::Mat cv_mat, std::string &mat_str);

		// 计算两个点之间的方向角，返回角度范围为 [0, M_PI]
		// 参数 exist_angle 指示是否存在有效角度信息（true：存在；false：不存在，默认按顺时针方向计为0）
		static double compute_direct(const Eigen::Vector2d& p1, const Eigen::Vector2d& p2, bool &exist_angle);

		// 获取系统当前时间的字符串表示（单位ms）
		static std::string get_current_time_asstring();

		// 记录匹配误差信息，用于调试或校准
		static void get_match_error(int channel_id, unsigned int id1, unsigned int id2, float x_error, float y_error, void *track_fusion_manager);

        // 导出道路方向信息到指定文件
		static void get_road_direction_info(int channel_id, RoadDirection road_direction_info, std::string file_path, void *track_fusion_manager);

	private:
		// 是否开启平台接力模式 如果是平台接力模式，则需要传递上一个盒子的输出目标给下一个盒子
		bool is_platform_relay_mode;

#ifdef DEBUG_DATA_SAVE
		// 构造两个盒子的数据
		MultiBoxTargets multi_box_targets1, multi_box_targets2;
		MultiDeviceTargets multi_device_targets1, multi_device_targets2;
        OutputTargets last_output_target1;
        OutputTargets last_output_target2;
#endif
		// 保存每个通道对应的目标输出回调函数
		std::map<int, TargetOutputCallback*> pipeline_output_callback_map;

		// 保存每个通道对应的输入目标拼接对象
		std::map<int, TrackSplicing*> pipeline_input_targets_map;

		// 通道ID计数器，用于生成新的通道ID
		int channel_id_count;
		// 获取新的通道ID
		int get_channel_id(); 

        // 记录边缘检测框的编号（用于边缘盒的唯一标识）
        int edgebox_id;

		// 保存每个通道的基础设备ID
		std::map<int, int> pipeline_base_device_id;

		// 保存每个通道的设备列表信息
		std::map<int, DeviceList> pipeline_devices_list;

		// 保存每个通道对应的全部设备信息映射
		std::map<int, DevicesMap> pipeline_devices_map;

		// 保存每个通道的设备数据保存对象，用于存储校准和融合数据
		std::map<int, DataSave*> pipeline_data_save_map;

        // 保存每个通道的相机雷达校准对象
        std::map<int, CalibCameraRadar*> pipeline_crcalib_map;

        // 保存每个通道使用同态矩阵进行相机雷达校准的对象
        std::map<int, CalibCameraRadarByHomograph*> pipeline_crhomography_map;

        // 保存每个通道的雷达间校准对象
        std::map<int, CalibRadarRadar*> pipeline_rrcalib_map;

		// 设备校准函数
		// 返回值：0 表示校准成功；-1 表示校准失败（校准数据不在允许范围内）
		int devices_calib(CalibMatch calib_match, DeviceCalibMat &device_calib_mat_21, DeviceCalibMat &device_calib_mat_12);

		// 检查设备校准误差
		// 返回值：0 表示误差在允许范围内；-1 表示误差过大，校准失败
		int check_calib_error(std::vector<Eigen::Vector2d> device2_checks, std::vector<Eigen::Vector2d> device1_checks, Eigen::Matrix3d calib_mat, unsigned int base_deviceid, unsigned int other_deviceid);

		// 计算其他设备到基础设备的转换矩阵
		// 参数：other_p1, other_p2 表示其他设备的两个点；base_p1, base_p2 表示基础设备的对应点
		// 返回值：转换矩阵，通过 exist_mat 指示矩阵是否有效
		Eigen::Matrix3d compute_transformation(const Eigen::Vector2d other_p1, const Eigen::Vector2d base_p1, const Eigen::Vector2d other_p2, const Eigen::Vector2d base_p2, unsigned int base_deviceid, unsigned int other_deviceid, bool &exist_mat);

		// 根据设备匹配列表生成设备映射矩阵集合
		// 返回值：0 表示成功；-1 表示失败
		int devices_map(CalibMatchList calib_match_list, int channel_id, std::vector<DevicesCalibMat> &devices_map_vec);

		// 通过HDM方式生成设备映射矩阵集合，用于获取设备位置信息
		// 返回值：0 表示成功；-1 表示失败
		int devices_map_hdm(int channel_id, std::vector<DevicesCalibMat> &devices_map_vec);

		// 更新设备映射矩阵集合
		// 返回值：0 表示更新成功；-1 表示更新失败
		int devices_map_update(std::vector<DevicesCalibMat> devices_map_vec, int channel_id, void *alg_param, unsigned int cnt);

		// 通过HDM方式更新设备映射矩阵集合
		// 返回值：0 表示更新成功；-1 表示更新失败
		int devices_map_hdm_update(std::vector<DevicesCalibMat> devices_map_vec, int channel_id);

		// 打印当前每个通道的设备映射信息（用于调试）
		void print_pipeline_devices_map();	
	};
}




