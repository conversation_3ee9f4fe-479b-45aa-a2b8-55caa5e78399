//
// Created by Administrator on 2024/10/28.
//
#include <iomanip> // 引入用于格式化输出的头文件
#include "RoadDirectionCacl.h"
#include "zlogger.h"
#include <cmath>
#include <numeric> // C++17 引入，用于 std::accumulate

ZLOGGER_HEADER_DEFINE("ALG_ROAD_DIRECTION")

using namespace tecu_r1000_0_algorithm;


RoadDirectionCacl::RoadDirectionCacl(int channle_id, std::string file_folder_path)
{
    direction_grid_height = 10.0f;                  //网格的高度
    direction_grid_number = 50.0f;                  //网格的数量
    direction_grid_rate_threshold   = 0.75f;        //网格的比例阈值

    road_range_target_number = 500;                 //默认值
    road_range_target_count  = 0;

    road_channel_id = channle_id;
    std::string channel_name = "channel_id_" + std::to_string(channle_id) + R"(/)";
    std::string channel_save_file_dir_path = file_folder_path + channel_name;
    std::string mkdir_str;
    mkdir_str = "mkdir -p ";
    mkdir_str += channel_save_file_dir_path;
    system(mkdir_str.c_str());


    file_save_path = channel_save_file_dir_path + "road_direction.dat";
    road_device_list_save_path = channel_save_file_dir_path + "road_direction_device_list.dat";

    ZINFO("RoadDirectionCacl file_save_path:%s road_device_list_save_path:%s \n",
          file_save_path.c_str(), road_device_list_save_path.c_str());

    road_device_lists.device_cnt = 0;
    road_direction_state         = RoadDirectionState::DIRECTION_INIT;
    road_direction_load(this->road_device_lists, this->road_direction, this->road_direction_state);
    ZINFO("RoadDirectionCacl road_direction_state:%d \n", road_direction_state);
}




RoadDirectionCacl::~RoadDirectionCacl()
{
    device_direction_vec_clear(this->road_direction);
}


void RoadDirectionCacl::road_direction_load(DeviceList &road_device_lists_, RoadDirection &road_direction_, RoadDirectionState &road_direction_state_)
{
    FILE* road_device_list_file = fopen(this->road_device_list_save_path.c_str(), "rb");
    if (road_device_list_file == NULL)
    {
        if (road_device_list_file == NULL) {
            ZINFO("road_device_list_file OPEN failed, road_device_list_save_path:%s \n", this->road_device_list_save_path.c_str());
        }
        if (road_device_list_file != NULL) {
            fclose(road_device_list_file);
        }
    }
    else
    {
        ZINFO("road_device_list_file OPEN success, road_device_list_save_path:%s \n",this->road_device_list_save_path.c_str());
        fread(&road_device_lists_, sizeof(DeviceList), 1, road_device_list_file);
        fclose(road_device_list_file);
        print_device_list(road_device_lists_);

    }

    std::ifstream road_direction_file(this->file_save_path);
    if (!road_direction_file.is_open())
    {
        // 处理文件打开失败的情况
        road_direction_file.close();
        ZINFO("road_direction_file OPEN failed, file_save_path:%s \n", this->file_save_path.c_str());
    }
    else
    {
        road_direction_ = readRoadDirectionFromStream(road_direction_file);
        road_direction_file.close();
        print_road_direction(road_direction_);

        road_direction_state_ = RoadDirectionState::DIRECTION_END;
    }
}

// 辅助函数，用于从输入流中读取一个 float 值
float RoadDirectionCacl::readFloatFromStream(std::istream& is)
{
    float value;
    is >> value;
    return value;
}

// 辅助函数，用于从输入流中读取一个 int 值（表示向量大小）
int RoadDirectionCacl::readIntFromStream(std::istream& is)
{
    int value;
    is >> value;
    return value;
}

// GridInfo 的反序列化函数
GridInfo RoadDirectionCacl::readGridInfoFromStream(std::istream& is)
{
    GridInfo grid;
    grid.grid_up_edge = readFloatFromStream(is);
    grid.grid_down_edge = readFloatFromStream(is);
    grid.direction_mad_std = readFloatFromStream(is);
    grid.direction_mad_mean = readFloatFromStream(is);

    int vecSize = readIntFromStream(is);
    grid.direction_vec.resize(vecSize);
    for (int i = 0; i < vecSize; ++i) {
        grid.direction_vec[i] = readFloatFromStream(is);
    }
    return grid;
}

// RoadDirection 的反序列化函数
RoadDirection RoadDirectionCacl::readRoadDirectionFromStream(std::istream& is)
{
    RoadDirection road;
    road.road_min_xcoor = readFloatFromStream(is);
    road.road_max_xcoor = readFloatFromStream(is);
    road.road_min_ycoor = readFloatFromStream(is);
    road.road_max_ycoor = readFloatFromStream(is);
    road.use_ycoor = readIntFromStream(is) != 0; // 假设 1 表示 true，0 表示 false

    int gridSize = readIntFromStream(is);
    road.grid_infos.resize(gridSize);
    for (int i = 0; i < gridSize; ++i) {
        road.grid_infos[i] = readGridInfoFromStream(is);
    }
    return road;
}


void RoadDirectionCacl::road_direction_save()
{
    std::ofstream road_direction_file(this->file_save_path.c_str());
    if (!road_direction_file) {
        ZDEBUG("road_direction_file OPEN failed! \n");
        return;
    }
    ZINFO("road_direction_save file_save_path:%s \n", this->file_save_path.c_str());
    // 将 RoadDirection 实例的数据写入文件
    this->road_direction.printToStream(road_direction_file);

    // 关闭文件
    road_direction_file.close();

    FILE *road_device_list_file = fopen(this->road_device_list_save_path.c_str(), "w");
    fwrite(&this->road_device_lists, 1, sizeof(DeviceList), road_device_list_file);
    fclose(road_device_list_file);
}

void RoadDirectionCacl::print_device_list(DeviceList road_device_lists_)
{
    ZINFO("road_device_lists.cnt:%d \n",
          road_device_lists_.device_cnt);
    for(size_t rct = 0; rct < road_device_lists_.device_cnt; rct++)
    {
        ZINFO("th:%d/%d device_id:%d x:%.2f y:%.2f latitude:%.2f longitude:%.2f direction:%.2f enable:%d \n",
              rct, road_device_lists_.device_cnt,
              road_device_lists_.device_list[rct].device_id,
              road_device_lists_.device_list[rct].x,
              road_device_lists_.device_list[rct].y,
              road_device_lists_.device_list[rct].device_latitude,
              road_device_lists_.device_list[rct].device_longitude,
              road_device_lists_.device_list[rct].device_direction,
              road_device_lists_.device_list[rct].device_enable);
    }
}


void RoadDirectionCacl::print_road_direction(RoadDirection road_direction_)
{
    ZINFO("road_direction_ use_ycoor:%d road_min_xcoor:%.2f road_max_xcoor:%.2f road_min_ycoor:%.2f road_max_ycoor:%.2f grid_infos.size:%d \n",
          road_direction_.use_ycoor,
          road_direction_.road_min_xcoor,
          road_direction_.road_max_xcoor,
          road_direction_.road_min_ycoor,
          road_direction_.road_max_ycoor,
          road_direction_.grid_infos.size());
    std::string grid_down_edge_str = "";
    std::string grid_up_edge_str   = "";
    std::string grid_number_str   = "";
    std::string mad_mean_str       = "";
    std::string mad_std_str        = "";
    for(size_t gi = 0; gi < road_direction_.grid_infos.size(); gi++)
    {
        std::ostringstream grid_down_oss_center;
        grid_down_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].grid_down_edge);
        grid_down_edge_str += " " + grid_down_oss_center.str();

        std::ostringstream grid_up_oss_center;
        grid_up_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].grid_up_edge);
        grid_up_edge_str += " " + grid_up_oss_center.str();

        std::ostringstream grid_number_oss_center;
        grid_number_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].direction_vec.size());
        grid_number_str += " " + grid_number_oss_center.str();

        std::ostringstream mad_mean_oss_center;
        mad_mean_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].direction_mad_mean);
        mad_mean_str += " " + mad_mean_oss_center.str();

        std::ostringstream mad_std_oss_center;
        mad_std_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].direction_mad_std);
        mad_std_str += " " + mad_std_oss_center.str();
    }
    ZINFO("road_direction_ grid_down_edge_str:%s\n", grid_down_edge_str.c_str());
    ZINFO("road_direction_ grid_up_edge_str:%s\n", grid_up_edge_str.c_str());
    ZINFO("road_direction_ grid_number_str:%s\n", grid_number_str.c_str());
    ZINFO("road_direction_ mad_mean_str:%s\n", mad_mean_str.c_str());
    ZINFO("road_direction_ mad_std_str:%s\n", mad_std_str.c_str());
    ZINFO("print_road_direction end!\n");
}

void RoadDirectionCacl::device_direction_vec_clear(RoadDirection &road_direction_)
{
    //清空device_direction_vec
    for(size_t gi = 0; gi < road_direction_.grid_infos.size(); gi++)
    {
        std::vector<float>().swap( road_direction_.grid_infos[gi].direction_vec);
    }
    std::vector<GridInfo>().swap(road_direction_.grid_infos);
}


/* TODO:JIATAO
 * @Name:get_road_direction
 * @Description: 根据设备id号和目标位置，获取所在道路的位置
 *
 * @Input
 * device_id: 设备ID号
 * CalibPoint: 目标位置
 * road_direction: 该目标在该位置的方位
 *
 * @Output
 * 0: 道路方位计算成功; -1: 计算失败
 *
 * @Edit History
 * Date: 2024/10/29
 * Time: 11:42
 * Author: WangXing
 * Content: Create
*/
int RoadDirectionCacl::get_road_direction(unsigned int device_id, CalibPoint coor_in_base, float &target_direction)
{
    int ret = -1;
    // 如果检测完成，则直接返回
    if(road_direction_state != RoadDirectionState::DIRECTION_END)
    {
        ZERROR("road_direction_state:%d is not DIRECTION_END\n", road_direction_state);
        return ret;
    }
    else
    {
        if(road_direction.grid_infos.size() == 0)
        {
            return ret;
        }
        //边界检测
        target_direction = 361.0f;
        unsigned int grid_length = road_direction.grid_infos.size();
        if(road_direction.use_ycoor == true)
        {
            if(coor_in_base.y <= road_direction.grid_infos[0].grid_down_edge)
            {
                target_direction = road_direction.grid_infos[0].direction_mad_mean;
            }
            else if(coor_in_base.y >= road_direction.grid_infos[grid_length - 1].grid_up_edge)
            {
                target_direction = road_direction.grid_infos[grid_length - 1].direction_mad_mean;
            }
            else
            {
                for(size_t gi = 0; gi < road_direction.grid_infos.size(); gi++)
                {
                    //边界检测
                    if(coor_in_base.y >= road_direction.grid_infos[gi].grid_down_edge
                       && coor_in_base.y < road_direction.grid_infos[gi].grid_up_edge)
                    {
                        target_direction = road_direction.grid_infos[gi].direction_mad_mean;
                        break;
                    }
                }
            }
        }
        else
        {
            if(coor_in_base.x <= road_direction.grid_infos[0].grid_down_edge)
            {
                target_direction = road_direction.grid_infos[0].direction_mad_mean;
            }
            else if(coor_in_base.x >= road_direction.grid_infos[grid_length - 1].grid_up_edge)
            {
                target_direction = road_direction.grid_infos[grid_length - 1].direction_mad_mean;
            }
            else
            {
                for(size_t gi = 0; gi < road_direction.grid_infos.size(); gi++)
                {
                    //边界检测
                    if(coor_in_base.x >= road_direction.grid_infos[gi].grid_down_edge
                    && coor_in_base.x < road_direction.grid_infos[gi].grid_up_edge)
                    {
                        target_direction = road_direction.grid_infos[gi].direction_mad_mean;
                        break;
                    }
                }
            }
        }
        if(fabs(target_direction - 361.0f) > 1e-4)
        {
            ret = 0;
        }
        else
        {
            ZERROR("target_direction:%.2f is 361.0f\n", target_direction);
            ret = -1;
        }
        return ret;
    }
}



/*
// * @Name: road_direction_amend
// * @Description: 道路方位的修正
// *
// * @Input
// * device_id: 设备ID号
// * CalibPoint: 目标位置
// * road_direction: 待修正的目标道路方位
// *
// * @Output
// * float: 修正后的道路方位
// *
// * @Edit History
// * Date: 2024/10/30
// * Time: 14:28
// * Author: WangXing
// * Content: Create
//*/
float RoadDirectionCacl::road_direction_amend(unsigned int device_id, CalibPoint coor_in_base, float target_direction)
{
    if(road_direction_state != RoadDirectionState::DIRECTION_END)
    {
        return target_direction;
    }
    else
    {
        if(road_direction.grid_infos.size() == 0)
        {
            return target_direction;
        }
        //边界检测
        int road_target_index = -1;
        unsigned int grid_length = road_direction.grid_infos.size();
        if(road_direction.use_ycoor == true)
        {
            if(coor_in_base.y <= road_direction.grid_infos[0].grid_down_edge)
            {
                road_target_index = 0;
            }
            else if(coor_in_base.y >= road_direction.grid_infos[grid_length - 1].grid_up_edge)
            {
                road_target_index = (int)grid_length - 1;
            }
            else
            {
                for(size_t gi = 0; gi < road_direction.grid_infos.size(); gi++)
                {
                    //边界检测
                    if(coor_in_base.y >= road_direction.grid_infos[gi].grid_down_edge
                       && coor_in_base.y < road_direction.grid_infos[gi].grid_up_edge)
                    {
                        road_target_index = (int)gi;
                        break;
                    }
                }
            }
        }
        else
        {
            if(coor_in_base.x <= road_direction.grid_infos[0].grid_down_edge)
            {
                road_target_index = 0;
            }
            else if(coor_in_base.x >= road_direction.grid_infos[grid_length - 1].grid_up_edge)
            {
                road_target_index = (int)grid_length - 1;
            }
            else
            {
                for(size_t gi = 0; gi < road_direction.grid_infos.size(); gi++)
                {
                    //边界检测
                    if(coor_in_base.x >= road_direction.grid_infos[gi].grid_down_edge
                       && coor_in_base.x < road_direction.grid_infos[gi].grid_up_edge)
                    {
                        road_target_index = (int)gi;
                        break;
                    }
                }
            }
        }
        if(road_target_index == -1)
        {
            return target_direction;
        }
        else
        {
            if(fabs(target_direction - road_direction.grid_infos[road_target_index].direction_mad_mean) < 0.5 * road_direction.grid_infos[road_target_index].direction_mad_std)
            {
                return (target_direction + road_direction.grid_infos[road_target_index].direction_mad_mean) / 2.0f;
            }
            else
            {
                return road_direction.grid_infos[road_target_index].direction_mad_mean;
            }
        }
    }
}


/*
 * @Name: road_direction_amend
 * @Description: 道路方位的修正
 * @Input
 * device_id: 设备ID号
 * coor_in_base: 目标位置
 * target_output_path: 目标路径
 * target_direction: 待修正的目标道路方位
 *
 * @Output
 * float: 修正后的道路方位
 *
 * @Edit History
 * Date: 2025/02/12
 * Time: 10:00
 */
float RoadDirectionCacl::road_direction_amend(unsigned int device_id, CalibPoint coor_in_base, std::vector<TargetOutput> target_output_path, float target_direction)
{
    // 修正后的目标运动方向默认为输入方向
    float amend_target_direction = target_direction;
    const int cal_size = 5;
    if (target_output_path.size() < cal_size)
    {
        // 当轨迹点不足时直接返回目标方向
        return amend_target_direction;
    }
    else
    {
        // 初始化x和y方向位移的累计值
        float sum_dx = 0.0f;
        float sum_dy = 0.0f;
        // 对前cal_size帧数据，计算相邻两点之间的位移并累计
        for (int i = 1; i < cal_size; i++)
        {
            // 计算当前点与前一个点在x方向上的位移
            float dx = target_output_path[i].target_coor_in_base.x - target_output_path[i - 1].target_coor_in_base.x;
            // 计算当前点与前一个点在y方向上的位移
            float dy = target_output_path[i].target_coor_in_base.y - target_output_path[i - 1].target_coor_in_base.y;
            sum_dx += dx;
            sum_dy += dy;
        }
        // 计算平均位移，此处的间隔数为cal_size - 1
        float avg_dx = sum_dx / (cal_size - 1);
        float avg_dy = sum_dy / (cal_size - 1);
        // 利用atan2函数计算平均运动方向（弧度制）并转换为角度
        amend_target_direction = std::atan2(avg_dy, avg_dx) * 180.0f / M_PI;
        if (amend_target_direction < 0) {
            amend_target_direction += 360.0f;
        }
    }
    // 保证amend_target_direction与target_direction的差值在20度以内,如果超过,进行裁剪
    if (fabs(amend_target_direction - target_direction) > 20.0f)
    {
        if (amend_target_direction > target_direction)
        {
            amend_target_direction = target_direction + 20.0f;
        }
        else
        {
            amend_target_direction = target_direction - 20.0f;
        }
    }
    // 返回修正后的运动方向
    return amend_target_direction;
}
/*
 * @Name: get_device_direction_map
 * @Description: 获取设备方向图，用于计算和更新道路的方向信息
 *              该函数实现了一个状态机，通过多个状态来完成道路方向的计算过程：
 *              1. DIRECTION_INIT: 初始化状态
 *              2. ROAD_RANGE_ACQUISITION: 道路范围获取状态，从车道信息获取道路范围
 *              3. ROAD_MESH_GENERATION: 网格生成状态，根据道路范围生成网格
 *              4. DIRECTION_ACQUISITION: 方向获取状态，采集目标方向信息
 *              5. DIRECTION_CALC: 方向计算状态，计算每个网格的方向
 *              6. DIRECTION_END: 结束状态，完成道路方向计算
 *
 * @Input
 * splicing_input_target_vec: 输入的目标数据向量
 * min_detection_distance: 最小检测距离
 * max_detection_distance: 最大检测距离
 * device_lists: 设备列表
 * lane_infos: 车道信息，用于获取道路范围
 *
 * @Output
 * void
 *
 * @Edit History
 * Date: 2025/02/06
 * Time: 10:00
 * Author: JIATAO
 * Content: 更新：从手动绘制的车道信息获取车道信息 保持原来的接口不变
 */
void RoadDirectionCacl::get_device_direction_map(std::vector<SplicingInputTarget> splicing_input_target_vec,
                                                float min_detection_distance,
                                                float max_detection_distance,
                                                DeviceList device_lists,
                                                const std::vector<LaneInfo> lane_infos)
{
    // 检查设备列表更新状态
    // ZINFO("get_device_direction_map device_lists.device_cnt:%d \n", device_lists.device_cnt);
    int device_update = device_lists_update(device_lists, this->road_device_lists);
    
    // 如果设备列表发生变化，重置状态机
    if(device_update == 0)
    {
        road_direction_state = RoadDirectionState::DIRECTION_INIT;
        ZINFO("RoadDirectionState::DIRECTION_INIT, device_lists is change! \n");
    }

    // 状态机处理逻辑
    if(road_direction_state == RoadDirectionState::DIRECTION_INIT)
    {
        ZINFO("RoadDirectionState::DIRECTION_INIT! \n");
        // 初始化状态：清除现有数据并创建新的道路方向基础信息
        device_direction_vec_clear(this->road_direction);
        // 创建road_direction的基础信息
        road_direction_state = device_direction_vec_create(this->road_direction,
                                                         this->road_device_lists,
                                                         max_detection_distance - min_detection_distance,
                                                         this->direction_grid_height,
                                                         this->direction_grid_number,
                                                         this->road_range_target_number,
                                                         this->road_range_target_count);
    }
    if(road_direction_state == RoadDirectionState::ROAD_RANGE_ACQUISITION)
    {
        ZINFO("RoadDirectionState::ROAD_RANGE_ACQUISITION! \n");
        // 道路范围获取状态：更新道路范围信息
        // road_direction_state = road_range_update(this->road_direction,
        //                                        splicing_input_target_vec,
        //                                        this->road_range_target_number,
        //                                        this->road_range_target_count);

        // 道路范围获取从手动设定的车道获取（lane_infos）
        road_direction_state = road_range_update(this->road_direction,
                                               lane_infos);
    }
    if(road_direction_state == RoadDirectionState::ROAD_MESH_GENERATION)
    {
        ZINFO("RoadDirectionState::ROAD_MESH_GENERATION!\n");
        // 网格生成状态：根据检测距离划分网格
        // road_direction_state = road_range_mesh_generation(
        //         this->road_direction,
        //         this->direction_grid_height);

        // 网格生成状态：根据手动设定的车道信息划分网格
        road_direction_state = road_range_mesh_generation(this->road_direction,
                                                     lane_infos);
        // 保存计算结果
        road_direction_save();
    }
    if(road_direction_state == RoadDirectionState::DIRECTION_END)
    {
        // ZINFO("RoadDirectionState::DIRECTION_END!\n");
        // DIRECTION_END状态：保持已统计的道路方向信息
        // 此状态下不进行任何处理，保持现有结果
    }
}



void RoadDirectionCacl::get_device_direction_map(std::vector<SplicingInputTarget> splicing_input_target_vec,
                                                 float min_detection_distance,
                                                 float max_detection_distance,
                                                 DeviceList device_lists)
{

    //根据设备数量是否发生改变判断其是否处于什么状态
    int device_update = device_lists_update(device_lists, this->road_device_lists);
    if(device_update == 0)
    {
        road_direction_state = RoadDirectionState::DIRECTION_INIT;
        ZINFO("RoadDirectionState::DIRECTION_INIT, device_lists is change! \n");
    }

    //当设备数量为0时，计算车道的方位
    if(road_direction_state == RoadDirectionState::DIRECTION_INIT)
    {
        device_direction_vec_clear(this->road_direction);
        //1. road_direction的级别信息
        road_direction_state = device_direction_vec_create(this->road_direction,
                                                           this->road_device_lists,
                                                           max_detection_distance - min_detection_distance,
                                                           this->direction_grid_height,
                                                           this->direction_grid_number,
                                                           this->road_range_target_number,
                                                           this->road_range_target_count);
        ZINFO("RoadDirectionState::DIRECTION_INIT! \n");
    }
    else if(road_direction_state == RoadDirectionState::ROAD_RANGE_ACQUISITION)
    {
        ZINFO("RoadDirectionState::ROAD_RANGE_ACQUISITION! \n");
        road_direction_state =  road_range_update(this->road_direction,
                                                  splicing_input_target_vec,
                                                  this->road_range_target_number,
                                                  this->road_range_target_count);
    }
    else if(road_direction_state == RoadDirectionState::ROAD_MESH_GENERATION)
    {
        ZINFO("RoadDirectionState::ROAD_MESH_GENERATION!\n");
        //2. 根据检测距离划分网格
        road_direction_state = road_range_mesh_generation(
                this->road_direction,
                this->direction_grid_height);
    }
    else if(road_direction_state == RoadDirectionState::DIRECTION_ACQUISITION)
    {
        ZINFO("RoadDirectionState::DIRECTION_ACQUISITION! \n");
        road_direction_state = road_direction_vec_update(
                this->road_direction,
                splicing_input_target_vec);
    }
    else if(road_direction_state == RoadDirectionState::DIRECTION_CALC)
    {
        ZINFO("RoadDirectionState::DIRECTION_CALC! \n");
        //3. 计算每个网格内的均值与方差
        road_direction_state = road_direction_calc_update(this->road_direction,
                                                          this->direction_grid_number);
        road_direction_save();
    }
    else
    {
        //RoadDirectionState::DIRECTION_END
        //保持统计的道路方向信息
        ZINFO("RoadDirectionState::DIRECTION_END! \n");
    }
}


void RoadDirectionCacl::print_road_direction()
{
    // 打印道路信息
    for(size_t gi = 0; gi < road_direction.grid_infos.size(); gi++)
    {
        ZINFO("grid_infos[%d].direction_vec.size(): %d direction_mad_mean:%.2f direction_mad_std:%.2f\n",
         gi, road_direction.grid_infos[gi].direction_vec.size(),
         road_direction.grid_infos[gi].direction_mad_mean,
         road_direction.grid_infos[gi].direction_mad_std);
    }
}





/*
 * @Name: device_lists_update
 * @Description: 设备信息是否发生改变，来决定是否重新统计道路的方位
 * （1） 根据设备的数量进行判断
 *
 * @Input
 * cur_device_list: 当前时刻设备信息
 * road_device_lists: 道路的设备信息
 *
 * @Output
 * 0: 设备信息发生变化
 *-1: 设备信息未发生改变
 *
 * @Edit History
 * Date: 2024/10/31
 * Time: 15:32
 * Author: WangXing
 * Content: Create
*/
int RoadDirectionCacl::device_lists_update(DeviceList cur_device_lists, DeviceList &road_device_lists)
{
    int device_list_update = -1;
    if(cur_device_lists.device_cnt == road_device_lists.device_cnt)
    {
        device_list_update = -1;
    }
    else
    {
        device_list_update = 0;
    }
    road_device_lists = cur_device_lists;
    return device_list_update;
}


RoadDirectionState RoadDirectionCacl::device_direction_vec_create(RoadDirection &road_direction,
                                                                  DeviceList device_list,
                                                                  float device_detect_range_,
                                                                  float direction_grid_height_,
                                                                  float direction_grid_number_,
                                                                  int &road_range_target_number_,
                                                                  int &road_range_target_count_)
{
    road_direction.road_min_xcoor = INT32_MAX;
    road_direction.road_max_xcoor = INT32_MIN;
    road_direction.road_min_ycoor = INT32_MAX;
    road_direction.road_max_ycoor = INT32_MIN;
    road_direction.use_ycoor     = false;

    float road_range             = device_list.device_cnt * device_detect_range_;
    road_range_target_number_ = road_range / direction_grid_height_ * direction_grid_number_;
    road_range_target_count_ = 0;


    ZINFO("device_direction_vec_create road_direction road_min_xcoor:%.2f road_max_xcoor:%.2f road_min_ycoor:%.2f road_max_ycoor:%.2f use_ycoor:%d device_cnt:%d device_detect_range:%.2f road_range_target_number:%d road_range_target_count_:%d\n",
          road_direction.road_min_xcoor,
          road_direction.road_max_xcoor,
          road_direction.road_min_ycoor,
          road_direction.road_max_ycoor,
          road_direction.use_ycoor,
          device_list.device_cnt,
          device_detect_range_,
          road_range_target_number_,
          road_range_target_count_);
    return RoadDirectionState::ROAD_RANGE_ACQUISITION;
}

/*
 * @Name: road_range_update
 * @Description: 更新道路范围信息
 *              通过遍历目标点来获取道路的最大最小坐标范围
 *              并根据目标点数量决定是否进入网格生成阶段
 *  road_range_update road_direction road_min_xcoor:-608.00 road_max_xcoor:202.25 road_min_ycoor:-202.50 road_max_ycoor:202.25 
 * 
 * @Input
 * road_direction: 道路方向信息结构体,包含道路边界坐标等信息
 * splicing_input_target_vec_: 输入的目标点向量
 * road_range_target_number_: 需要收集的目标点数量阈值
 * road_range_target_count_: 当前已收集的目标点计数
 *
 * @Output
 * RoadDirectionState: 返回道路方向计算的下一个状态
 *                     当收集到足够的点时返回ROAD_MESH_GENERATION状态
 *                     否则保持在ROAD_RANGE_ACQUISITION状态继续收集点
 */
RoadDirectionState RoadDirectionCacl::road_range_update(RoadDirection &road_direction,
                                                       std::vector<SplicingInputTarget> splicing_input_target_vec_,
                                                       int &road_range_target_number_,
                                                       int &road_range_target_count_)
{
    // JIATAO:TEST START
    road_range_target_number_ = 1500;
    // JIATAO:TEST END
    // 遍历所有输入的目标点
    for(size_t sit = 0; sit < splicing_input_target_vec_.size(); sit++)
    {
        // 更新X坐标的最大值
        if(splicing_input_target_vec_[sit].target_input.target_coor_in_base.x > road_direction.road_max_xcoor)
        {
            road_direction.road_max_xcoor = splicing_input_target_vec_[sit].target_input.target_coor_in_base.x;
        }
        // 更新X坐标的最小值
        if(splicing_input_target_vec_[sit].target_input.target_coor_in_base.x < road_direction.road_min_xcoor)
        {
            road_direction.road_min_xcoor = splicing_input_target_vec_[sit].target_input.target_coor_in_base.x;
        }
        // 更新Y坐标的最大值
        if(splicing_input_target_vec_[sit].target_input.target_coor_in_base.y > road_direction.road_max_ycoor)
        {
            road_direction.road_max_ycoor = splicing_input_target_vec_[sit].target_input.target_coor_in_base.y;
        }
        // 更新Y坐标的最小值
        if(splicing_input_target_vec_[sit].target_input.target_coor_in_base.y < road_direction.road_min_ycoor)
        {
            road_direction.road_min_ycoor = splicing_input_target_vec_[sit].target_input.target_coor_in_base.y;
        }
        // 累加目标点计数
        road_range_target_count_++;
    }

    // 打印当前收集的目标点数量和所需数量
    ZINFO("road_range_update road_range_target_count_:%d road_range_target_number_:%d \n",
          road_range_target_count_, road_range_target_number_);
    // 打印当前道路边界范围信息
    ZINFO("road_range_update road_direction road_min_xcoor:%.2f road_max_xcoor:%.2f road_min_ycoor:%.2f road_max_ycoor:%.2f \n",
          road_direction.road_min_xcoor,
          road_direction.road_max_ycoor,
          road_direction.road_min_ycoor,
          road_direction.road_max_ycoor);

    // 判断是否收集到足够的目标点
    if(road_range_target_count_ > road_range_target_number_)
    {
        // 如果收集到足够的点,进入网格生成阶段
        return RoadDirectionState::ROAD_MESH_GENERATION;
    }
    else
    {
        // 如果点数不够,继续保持在数据采集阶段
        return RoadDirectionState::ROAD_RANGE_ACQUISITION;
    }
}

/*
 * @Name: road_range_update
 * @Description: 更新道路范围 从手动规划的车道信息中获取
 * road_range_update road_direction road_min_xcoor:-948.00 road_max_xcoor:348.75 road_min_ycoor:-315.75 road_max_ycoor:348.75 
 * 
 * @Input
 * road_direction: 道路方向信息结构体,包含道路边界坐标等信息
 * lane_infos: 车道信息
 *
 * @Output
 * RoadDirectionState: 返回下一个状态(ROAD_RANGE_ACQUISITION)
 */

RoadDirectionState RoadDirectionCacl::road_range_update(RoadDirection &road_direction_,
                                                       const std::vector<LaneInfo> lane_infos)
{
    // 检查车道信息是否为空
    if (lane_infos.empty()) {
        ZINFO("road_range_update: lane_infos is empty\n");
        return RoadDirectionState::ROAD_RANGE_ACQUISITION;
    }

    // 初始化道路边界为第一条车道的边界
    road_direction_.road_min_xcoor = FLT_MAX;
    road_direction_.road_max_xcoor = -FLT_MAX;
    road_direction_.road_min_ycoor = FLT_MAX;
    road_direction_.road_max_ycoor = -FLT_MAX;

    // 遍历所有车道，更新道路边界
    for (const auto& lane : lane_infos) {
        // 遍历每个车道的所有区域
        for (const auto& lane_area : lane.lane_areas) {
            // 遍历区域的四个角点来更新边界
            for (const auto& point : lane_area.lane_area) {
                // 更新X坐标的边界
                if (point.x < road_direction_.road_min_xcoor) {
                    road_direction_.road_min_xcoor = point.x;
                }
                if (point.x > road_direction_.road_max_xcoor) {
                    road_direction_.road_max_xcoor = point.x;
                }

                // 更新Y坐标的边界
                if (point.y < road_direction_.road_min_ycoor) {
                    road_direction_.road_min_ycoor = point.y;
                }
                if (point.y > road_direction_.road_max_ycoor) {
                    road_direction_.road_max_ycoor = point.y;
                }
            }
        }
    }

    // 打印更新后的道路边界信息
    // 打印当前道路边界范围信息
    ZINFO("road_range_update road_direction road_min_xcoor:%.2f road_max_xcoor:%.2f road_min_ycoor:%.2f road_max_ycoor:%.2f \n",
          road_direction.road_min_xcoor,
          road_direction.road_max_ycoor,
          road_direction.road_min_ycoor,
          road_direction.road_max_ycoor);

    // 直接进入网格生成阶段
    return RoadDirectionState::ROAD_MESH_GENERATION;
}

/*
 * @Name: road_range_mesh_generation
 * @Description: 根据道路范围生成网格划分
 * 该函数将道路区域划分为等高的网格，用于后续方向计算
 *
 * @Input
 * road_direction_: 道路方向信息结构体，包含道路边界和网格信息
 * direction_grid_height_: 每个网格的高度
 *
 * @Output
 * RoadDirectionState: 返回下一个状态(DIRECTION_ACQUISITION)
 */
RoadDirectionState RoadDirectionCacl::road_range_mesh_generation(RoadDirection &road_direction_,
                                                                float direction_grid_height_)
{
    // 确定使用哪个坐标轴作为主轴（选择跨度更大的轴）
    // 如果y轴方向的跨度大于x轴，则使用y坐标作为主轴
    road_direction_.use_ycoor = road_direction_.road_max_ycoor - road_direction_.road_min_ycoor > 
                               road_direction_.road_max_xcoor - road_direction_.road_min_xcoor ? true : false;
    
    // 计算道路在主轴方向上的最大跨度
    float max_range_coor = road_direction_.use_ycoor ? 
                          (road_direction_.road_max_ycoor - road_direction_.road_min_ycoor) :
                          (road_direction_.road_max_xcoor - road_direction_.road_min_xcoor);
    
    // 根据网格高度计算需要划分的网格数量
    int grid_number = max_range_coor / direction_grid_height_;

    // 生成网格信息
    for(size_t gn = 0; gn < grid_number; gn++)
    {
        GridInfo temp_grid_info;
        if(road_direction_.use_ycoor == true)
        {
            // 如果使用y轴作为主轴，计算每个网格的y轴范围
            temp_grid_info.grid_down_edge = road_direction_.road_min_ycoor + gn * direction_grid_height_;
            temp_grid_info.grid_up_edge = road_direction_.road_min_ycoor + (gn + 1) * direction_grid_height_;
        }
        else
        {
            // 如果使用x轴作为主轴，计算每个网格的x轴范围
            temp_grid_info.grid_down_edge = road_direction_.road_min_xcoor + gn * direction_grid_height_;
            temp_grid_info.grid_up_edge = road_direction_.road_min_xcoor + (gn + 1) * direction_grid_height_;
        }
        
        // 初始化网格的方向统计信息
        temp_grid_info.direction_mad_mean = 0.0f;  // 平均绝对偏差均值
        temp_grid_info.direction_mad_std  = 0.0f;  // 标准差
        
        // 将网格信息添加到道路方向结构中
        road_direction_.grid_infos.push_back(temp_grid_info);
    }

    // 打印调试信息：输出网格划分的基本参数
    ZINFO("road_range_mesh_generation use_ycoor:%d max_range_coor:%.2f grid_number:%d direction_grid_height_:%.2f road_min_xcoor:%.2f road_max_xcoor:%.2f road_min_ycoor:%.2f road_max_ycoor:%.2f \n",
          road_direction_.use_ycoor,
          max_range_coor,
          grid_number,
          direction_grid_height_,
          road_direction_.road_min_xcoor,
          road_direction_.road_max_ycoor,
          road_direction_.road_min_ycoor,
          road_direction_.road_max_ycoor);

    // 生成用于打印的网格边界字符串
    std::string grid_down_edge_str = "";  // 存储所有网格的下边界
    std::string grid_up_edge_str = "";    // 存储所有网格的上边界
    
    // 遍历所有网格，格式化边界值用于打印
    for(size_t gi = 0; gi < road_direction_.grid_infos.size(); gi++)
    {
        // 格式化下边界值
        std::ostringstream grid_down_oss_center;
        grid_down_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].grid_down_edge);
        grid_down_edge_str += " " + grid_down_oss_center.str();

        // 格式化上边界值
        std::ostringstream grid_up_oss_center;
        grid_up_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].grid_up_edge);
        grid_up_edge_str += " " + grid_up_oss_center.str();
    }

    // 打印所有网格的边界信息
    ZINFO("road_range_mesh_generation grid_down_edge_str:%s grid_up_edge_str:%s \n",
          grid_down_edge_str.c_str(),
          grid_up_edge_str.c_str());

    // 网格生成完成，转入方向获取状态
    return RoadDirectionState::DIRECTION_ACQUISITION;
}

void get_min_max_coor(std::vector<cv::Point2f> lane_area,
                      bool use_ycoor,
                      float &min_coor,
                      float &max_coor)
{
    min_coor = FLT_MAX;
    max_coor = -FLT_MAX;
    for(const auto& point : lane_area)
    {
        if(use_ycoor == true)
        {
            if(point.y < min_coor)
            {
                min_coor = point.y; 
            }
            if(point.y > max_coor)
            {
                max_coor = point.y;
            }
        }
        else
        {
            if(point.x < min_coor)
            {
                min_coor = point.x; 
            }
            if(point.x > max_coor)
            {
                max_coor = point.x;
            }
        }
    }
}

/*
 * @Name: road_range_mesh_generation
 * @Description: 生成道路范围网格 从车道信息中获取
 * 将 DIRECTION_ACQUISITION和DIRECTION_CALC合并为一个状态 DIRECTION_END
 * 
 * @Input
 * road_direction_: 道路方向信息结构体，包含道路边界和网格信息
 * lane_infos: 车道信息
 *
 * @Output
 * RoadDirectionState: 返回下一个状态(DIRECTION_END) 
 */
RoadDirectionState RoadDirectionCacl::road_range_mesh_generation(RoadDirection &road_direction_,
                                                                const std::vector<LaneInfo> lane_infos)
{
    road_direction_.use_ycoor = road_direction_.road_max_ycoor - road_direction_.road_min_ycoor > 
                            road_direction_.road_max_xcoor - road_direction_.road_min_xcoor ? true : false;
    

    // 1. 遍历车道信息
    for(const auto& lane : lane_infos)
    {
        // 2. 遍历车道区域
        for(const auto& lane_area : lane.lane_areas)
        {
            // 赋值道路区域
            GridInfo temp_grid_info;
            // 获得该区域的最小值和最大值
            float min_coor, max_coor;
            get_min_max_coor(lane_area.lane_area, road_direction_.use_ycoor, min_coor, max_coor);
            temp_grid_info.grid_down_edge = min_coor;
            temp_grid_info.grid_up_edge = max_coor;
            // 设置方向 TODO
            temp_grid_info.direction_vec.push_back(static_cast<float>(lane_area.lane_direction));
            temp_grid_info.direction_mad_mean = static_cast<float>(lane_area.lane_direction);
            temp_grid_info.direction_mad_std  = 0.0f;
            road_direction_.grid_infos.push_back(temp_grid_info);
        }
    }
     //增加打印
    ZINFO("road_direction_calc_update grid_infos.size() size:%d use_ycoor:%d direction_grid_number:%.1f\n",
          road_direction_.grid_infos.size(),
          road_direction_.use_ycoor,
          direction_grid_number);
    std::string grid_down_edge_str = "";
    std::string grid_up_edge_str   = "";
    std::string grid_number_str   = "";
    std::string mad_mean_str       = "";
    std::string mad_std_str        = "";
    for(size_t gi = 0; gi < road_direction_.grid_infos.size(); gi++)
    {
        std::ostringstream grid_down_oss_center;
        grid_down_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].grid_down_edge);
        grid_down_edge_str += " " + grid_down_oss_center.str();

        std::ostringstream grid_up_oss_center;
        grid_up_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].grid_up_edge);
        grid_up_edge_str += " " + grid_up_oss_center.str();

        std::ostringstream grid_number_oss_center;
        grid_number_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].direction_vec[0]);
        grid_number_str += " " + grid_number_oss_center.str();

        std::ostringstream mad_mean_oss_center;
        mad_mean_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].direction_mad_mean);
        mad_mean_str += " " + mad_mean_oss_center.str();

        std::ostringstream mad_std_oss_center;
        mad_std_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].direction_mad_std);
        mad_std_str += " " + mad_std_oss_center.str();
    }
    ZINFO("road_direction_calc_update grid_down_edge_str:%s\n", grid_down_edge_str.c_str());
    ZINFO("road_direction_calc_update grid_up_edge_str:%s\n", grid_up_edge_str.c_str());
    ZINFO("road_direction_calc_update grid_number_str:%s\n", grid_number_str.c_str());
    ZINFO("road_direction_calc_update mad_mean_str:%s\n", mad_mean_str.c_str());
    ZINFO("road_direction_calc_update mad_std_str:%s\n", mad_std_str.c_str());
    ZINFO("\n \n");

    return RoadDirectionState::DIRECTION_END;
}


RoadDirectionState RoadDirectionCacl::road_direction_vec_update(RoadDirection &road_direction_,
                                                                std::vector<SplicingInputTarget> splicing_input_target_vec)
{
    for(size_t sit = 0; sit < splicing_input_target_vec.size(); sit++)
    {
        if(fabs(splicing_input_target_vec[sit].target_input.target.direction) < 1e-4)
        {
            continue;
        }
        for(size_t gi = 0; gi < road_direction_.grid_infos.size(); gi++)
        {
            //单台设备的某个网格内的目标方位数量满足要求
            std::cout << "grid_length: " << road_direction_.grid_infos[gi].direction_vec.size() << std::endl;
            if(road_direction_.grid_infos[gi].direction_vec.size() > direction_grid_number)
            {
                continue;
            }
           if(road_direction_.use_ycoor == true)
           {
               if(splicing_input_target_vec[sit].target_input.target_coor_in_base.y >= road_direction_.grid_infos[gi].grid_down_edge
               && splicing_input_target_vec[sit].target_input.target_coor_in_base.y < road_direction_.grid_infos[gi].grid_up_edge)
               {
                // 将目标方向角添加到网格内
                   road_direction_.grid_infos[gi].direction_vec.push_back(splicing_input_target_vec[sit].target_input.target.direction);
               }
           }
           else
           {
               if(splicing_input_target_vec[sit].target_input.target_coor_in_base.x >= road_direction_.grid_infos[gi].grid_down_edge
               && splicing_input_target_vec[sit].target_input.target_coor_in_base.x < road_direction_.grid_infos[gi].grid_up_edge)
               {
                   // 将目标方向角添加到网格内
                   road_direction_.grid_infos[gi].direction_vec.push_back(splicing_input_target_vec[sit].target_input.target.direction);
               }
           }
        }
    }

    float grid_number      = (float)road_direction_.grid_infos.size();
    float check_grid_count = 0.0f;
    for(size_t gi = 0; gi < road_direction_.grid_infos.size(); gi++)
    {
        if(road_direction_.grid_infos[gi].direction_vec.size() >= direction_grid_number)
        {
            check_grid_count ++;
        }
    }
    float check_grid_rate = check_grid_count / grid_number;
    ZINFO("road_direction_vec_update check_grid_rate:%.2f direction_grid_rate_threshold:%.2f \n",
                                     check_grid_rate, direction_grid_rate_threshold);
    if(check_grid_rate > direction_grid_rate_threshold)
    {
        ZINFO("RoadDirectionState::DIRECTION_CALC! \n");
        return RoadDirectionState::DIRECTION_CALC;
    }
    else
    {
        ZINFO("RoadDirectionState::DIRECTION_ACQUISITION! \n");
        return RoadDirectionState::DIRECTION_ACQUISITION;
    }
}




RoadDirectionState RoadDirectionCacl::road_direction_calc_update(RoadDirection &road_direction_,
                                                                 float direction_grid_number)
{
    //网格内的误差计算
    for(size_t gi = 0; gi < road_direction_.grid_infos.size(); gi++)
    {
        std::vector<float> grid_direction = road_direction_.grid_infos[gi].direction_vec;
        if(grid_direction.size() < direction_grid_number / 2)
        {
            road_direction_.grid_infos[gi].direction_mad_mean  = 0.0f;
            road_direction_.grid_infos[gi].direction_mad_std   = 0.0f;
        }
        else
        {
            road_direction_.grid_infos[gi].direction_mad_mean = cal_mad_mean(grid_direction, 3.0f);
            road_direction_.grid_infos[gi].direction_mad_std  = cal_vector_std(grid_direction);
        }
    }
    road_direction_.grid_infos[0].direction_mad_mean = 300.0f;
    //增加打印
    ZINFO("road_direction_calc_update grid_infos.size() size:%d use_ycoor:%d direction_grid_number:%.1f\n",
          road_direction_.grid_infos.size(),
          road_direction_.use_ycoor,
          direction_grid_number);
    std::string grid_down_edge_str = "";
    std::string grid_up_edge_str   = "";
    std::string grid_number_str   = "";
    std::string mad_mean_str       = "";
    std::string mad_std_str        = "";
    for(size_t gi = 0; gi < road_direction_.grid_infos.size(); gi++)
    {
        std::ostringstream grid_down_oss_center;
        grid_down_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].grid_down_edge);
        grid_down_edge_str += " " + grid_down_oss_center.str();

        std::ostringstream grid_up_oss_center;
        grid_up_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].grid_up_edge);
        grid_up_edge_str += " " + grid_up_oss_center.str();

        std::ostringstream grid_number_oss_center;
        grid_number_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].direction_vec.size());
        grid_number_str += " " + grid_number_oss_center.str();

        std::ostringstream mad_mean_oss_center;
        mad_mean_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].direction_mad_mean);
        mad_mean_str += " " + mad_mean_oss_center.str();

        std::ostringstream mad_std_oss_center;
        mad_std_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].direction_mad_std);
        mad_std_str += " " + mad_std_oss_center.str();
    }
    ZINFO("road_direction_calc_update grid_down_edge_str:%s\n", grid_down_edge_str.c_str());
    ZINFO("road_direction_calc_update grid_up_edge_str:%s\n", grid_up_edge_str.c_str());
    ZINFO("road_direction_calc_update grid_number_str:%s\n", grid_number_str.c_str());
    ZINFO("road_direction_calc_update mad_mean_str:%s\n", mad_mean_str.c_str());
    ZINFO("road_direction_calc_update mad_std_str:%s\n", mad_std_str.c_str());
    ZINFO("\n \n");



    //对道路信息降噪
    grid_vec_denoise(road_direction_.grid_infos, 5.0f);


    ZINFO("road_direction_calc_update denoise grid_infos.size() size:%d direction_grid_number:%.1f\n", road_direction_.grid_infos.size(), direction_grid_number);
    std::string denoise_mad_mean_str   = "";
    std::string denoise_mad_std_str    = "";
    for(size_t gi = 0; gi < road_direction_.grid_infos.size(); gi++)
    {
        std::ostringstream denoise_mad_mean_oss_center;
        denoise_mad_mean_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].direction_mad_mean);
        denoise_mad_mean_str += " " + denoise_mad_mean_oss_center.str();

        std::ostringstream denoise_mad_std_oss_center;
        denoise_mad_std_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].direction_mad_std);
        denoise_mad_std_str += " " + denoise_mad_std_oss_center.str();
    }
    ZINFO("road_direction_calc_update denoise_mad_mean_str:%s\n", denoise_mad_mean_str.c_str());
    ZINFO("road_direction_calc_update denoise_mad_std_str:%s\n", denoise_mad_std_str.c_str());
    ZINFO("\n");


    for(size_t gi = 0; gi < road_direction_.grid_infos.size(); gi++)
    {
        if(fabs(road_direction_.grid_infos[gi].direction_mad_mean) < 1e-4)
        {
            float min_coor_distance = FLT_MAX;
            unsigned int near_coor_index = 0;
            for(size_t gii = 0; gii < road_direction_.grid_infos.size(); gii++)
            {
                if(fabs(road_direction_.grid_infos[gii].direction_mad_mean) < 1e-4)
                {
                    continue;
                }
                if(fabs(road_direction_.grid_infos[gi].grid_down_edge - road_direction_.grid_infos[gii].grid_down_edge) < min_coor_distance)
                {
                    min_coor_distance = fabs(road_direction_.grid_infos[gi].grid_down_edge - road_direction_.grid_infos[gii].grid_down_edge);
                    near_coor_index = gii;
                }
            }
            if(min_coor_distance < FLT_MAX)
            {
                road_direction_.grid_infos[gi].direction_mad_mean = road_direction_.grid_infos[near_coor_index].direction_mad_mean;
            }
        }
    }

    for(size_t gi = 0; gi < road_direction_.grid_infos.size(); gi++)
    {
        if(fabs(road_direction_.grid_infos[gi].direction_mad_std) < 1e-4)
        {
            float min_coor_distance = FLT_MAX;
            unsigned int near_coor_index = 0;
            for(size_t gii = 0; gii < road_direction_.grid_infos.size(); gii++)
            {
                if(fabs(road_direction_.grid_infos[gii].direction_mad_std) < 1e-4)
                {
                    continue;
                }
                if(fabs(road_direction_.grid_infos[gi].grid_down_edge - road_direction_.grid_infos[gii].grid_down_edge) < min_coor_distance)
                {
                    min_coor_distance = fabs(road_direction_.grid_infos[gi].grid_down_edge - road_direction_.grid_infos[gii].grid_down_edge);
                    near_coor_index = gii;
                }
            }
            if(min_coor_distance < FLT_MAX)
            {
                road_direction_.grid_infos[gi].direction_mad_std = road_direction_.grid_infos[near_coor_index].direction_mad_std;
            }
        }
    }

    ZINFO("road_direction_calc_update padding grid_infos.size() size:%d direction_grid_number:%.1f\n", road_direction_.grid_infos.size(), direction_grid_number);
    std::string padding_mad_mean_str   = "";
    std::string padding_mad_std_str    = "";
    for(size_t gi = 0; gi < road_direction_.grid_infos.size(); gi++)
    {
        std::ostringstream padding_mad_mean_oss_center;
        padding_mad_mean_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].direction_mad_mean);
        padding_mad_mean_str += " " + padding_mad_mean_oss_center.str();

        std::ostringstream padding_mad_std_oss_center;
        padding_mad_std_oss_center << std::fixed << std::setprecision(1) << (road_direction_.grid_infos[gi].direction_mad_std);
        padding_mad_std_str += " " + padding_mad_std_oss_center.str();
    }
    ZINFO("road_direction_calc_update padding_mad_mean_str:%s\n", padding_mad_mean_str.c_str());
    ZINFO("road_direction_calc_update padding_mad_std_str:%s\n", padding_mad_std_str.c_str());
    ZINFO("\n");

    ZINFO("RoadDirectionState::DIRECTION_END!\n");
    return RoadDirectionState::DIRECTION_END;
}


void RoadDirectionCacl::grid_vec_denoise(std::vector<GridInfo> &grid_infos,
                                         float denoise_rate)
{
    std::vector<float> direction_vec;
    for(size_t gi = 0; gi < grid_infos.size(); gi++)
    {
        if(fabs(grid_infos[gi].direction_mad_mean) < 1e-4)
        {
            continue;
        }
        else
        {
            direction_vec.push_back(grid_infos[gi].direction_mad_mean);
        }
    }

    float mad       = cal_vector_mad(direction_vec);
    float data_mean = cal_vector_mean(direction_vec);

    for(size_t i = 0; i < grid_infos.size(); i++)
    {
        if(std::fabs(grid_infos[i].direction_mad_mean - data_mean) < denoise_rate * mad)
        {
            //不作处理
        }
        else
        {
            grid_infos[i].direction_mad_mean = 0.0f;
            grid_infos[i].direction_mad_std  = 0.0f;
        }
    }
}


void RoadDirectionCacl::road_direction_denoise(std::vector<float> &direction_vec,
                                               std::vector<float> &coor_vec,
                                               float denoise_rate)
{
    float mad       = cal_vector_mad(direction_vec);
    float data_mean = cal_vector_mean(direction_vec);

    std::vector<float> denoise_direction_vec;
    std::vector<float> denoise_coor_vec;
    for(size_t i = 0; i < direction_vec.size(); i++)
    {
        if(std::fabs(direction_vec[i] - data_mean) < denoise_rate * mad)
        {
            denoise_direction_vec.push_back(direction_vec[i]);
            denoise_coor_vec.push_back(coor_vec[i]);
        }
    }
    direction_vec.assign(denoise_direction_vec.begin(), denoise_direction_vec.end());
    coor_vec.assign(denoise_coor_vec.begin(), denoise_coor_vec.end());
}


float RoadDirectionCacl::cal_vector_mean(const std::vector<float>& data)
{
    if(data.size() == 0)
    {
        return 0.0f;
    }
    else
    {
        float data_sum =0.0f;
        for(size_t dt = 0; dt < data.size(); dt++)
        {
            data_sum += data[dt];
        }
        return data_sum / (float)data.size();
    }
}

// 计算平均绝对值偏差（MAD）
float RoadDirectionCacl::cal_vector_mad(const std::vector<float>& data)
{
    if (data.empty()) return 0.0;

    float sum = 0.0;
    for (float value : data) {
        sum += value;
    }
    float mean = sum / data.size();

    float madSum = 0.0;
    for (float value : data) {
        madSum += std::fabs(value - mean);
    }
    float mad = madSum / data.size();

    return mad;
}

float RoadDirectionCacl::cal_mad_mean(const std::vector<float>& data, float threshold_multiplier)
{
    float mad       = cal_vector_mad(data);
    float data_mean = cal_vector_mean(data);

    std::vector<float> inliers_data;
    for(size_t i = 0; i < data.size(); i++)
    {
        if(std::fabs(data[i] - data_mean) < threshold_multiplier * mad)
        {
            inliers_data.push_back(data[i]);
        }
    }
    if(inliers_data.size() > 0)
    {
        float data_mad_mean = cal_vector_mean(inliers_data);
        return data_mad_mean;
    }
    else
    {
        return data_mean;
    }
}

float RoadDirectionCacl::cal_vector_std(const std::vector<float>& data)
{
    float mean = std::accumulate(data.begin(), data.end(), 0.0) / data.size();
    float sumOfSquaresOfDifferences = 0.0;

    for (float num : data) {
        sumOfSquaresOfDifferences += (num - mean) * (num - mean);
    }

    float variance = sumOfSquaresOfDifferences / (data.size() - 1); // 使用 n-1 作为分母，计算样本标准差
    return std::sqrt(variance);
}