//
// Created by Administrator on 2024/12/31.
//

#include "utils.h"
#include "math.h"



/*
 * @Name:mercatorToLonLat
 * @Description: 将墨卡托坐标转换为经纬度坐标
 *
 * @Input
 * x 墨卡托坐标的x值
 * y 墨卡托坐标的y值
 * 包含转换后的经度lng和纬度lat的对象
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/9/27
 * Time: 16:46
 * Author: WangXing
 * Content: Create
*/
void Utils::mercatorToLonLat(double x, double y, double &longitude, double &latitude)
{
    // 墨卡托坐标到经纬度的转换公式
    // 注意：这里假设的缩放因子（20037508.34）适用于Web墨卡托投影（EPSG:3857）
    longitude = (x / 20037508.34) * 180.0;
    double mmy = (y / 20037508.34) * 180.0;
    latitude = (180.0 / M_PI) * (2 * atan(exp(mmy * M_PI / 180.0)) - M_PI / 2.0);
}




/*
 * @Name:lonlatToMercator
 * @Description:将经纬度坐标转换为墨卡托坐标
 *
 * @Input
 * lng 经度值
 * lat 纬度值
 * 墨卡托坐标对象，包含x和y属性
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/9/27
 * Time: 16:59
 * Author: WangXing
 * Content: Create
*/
void Utils::lonlatToMercator(double lng, double lat, double &x, double &y)
{
    const double earthRad = 6378137.0;
    x = lng * (M_PI / 180.0) * earthRad;
    double a = lat * (M_PI / 180.0);
    y = earthRad / 2.0 * log((1.0 + sin(a)) / (1.0 - sin(a)));
}