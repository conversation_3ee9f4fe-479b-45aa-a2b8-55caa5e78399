#ifndef DETECTRACK_SO_H
#define DETECTRACK_SO_H
#include <vector>
#include <cstdint>  // 添加这个头文件以支持int32_t类型
#include <string>
//fusion_id的最大值和最小值
#define MAX_FUSION_ID 999
#define MIN_FUSION_ID 1
//每个目标的轨迹的最大值
#define MAX_TRACK_INFO 256
// 每帧中目标数量的最大值
#define MAX_OBJECT_NUM     512
//目标检测的输入图像尺寸
#define MODEL_INPUT_WIDTH	640
#define MODEL_INPUT_HEIGHT	384
//目标检测的图像缩放尺寸
#define IMAGE_RESIZE_WIDTH 640
#define IMAGE_RESIZE_HEIGHT 360

//车牌的图像的输入尺寸
#define OCR_INPUT_WIDTH 176
#define OCR_INPUT_HEIGHT 48
//4k原图尺寸，用于车牌识别
#define PLATE_DETECT_IMAGE_WIDTH 3840
#define PLATE_DETECT_IMAGE_HEIGHT 2160
//车牌识别的图像的输出尺寸
#define OCR_OUTPUT_SIZE 22 * 82
//车牌颜色的分类个数
#define COLOR_OUTPUT_SIZE 5
//车牌号码的最大数
#define MAX_PLATE_TEXT 32
//雷达目标的最多个数
#define MAX_RADAR_TARGET 256
//手动标定最多的标定点个数
#define MAX_MATCH_POINT 32
//自动标定最多的关联区域个数
#define MAX_CALIB_AREAS 6
//最大车道个数
#define MAX_LANE_NUM 16

//最大视频流数
#define MAX_VEIDEO_NUM 2

//设备组的最大数量
#define DEV_GRP_NUM_MAX 4

//最大的匹配点个数
#define MAX_MATCH_NUM 12

/*设备组类型信息*/
typedef enum
{
    GRP_TYPE_BINOCULAR_RV = 0x00,                          //双目雷视
    GRP_TYPE_MONOCULAR_RV,                                 //单目雷视
    GRP_TYPE_BAYONET_CAMERA,                               //卡口相机
    GRP_TYPE_FISHEYE_CAMERA,                               //鱼眼相机
    GRP_TYPE_RADAR,                                        //雷达
} GroupType;

/*
坐标点
图像坐标点是归一化值，雷达坐标点的单位是米
*/
typedef struct
{
    float x;
    float y;
} CPoint;

/*
直线
*/
typedef struct
{
    CPoint p0;
    CPoint p1;
} CLine;


/*
矩形
*/
typedef struct
{
    float x;   //左上角坐标点横坐标
    float y;   //左上角坐标点纵坐标
    float width;   //矩形的宽度
    float height;   //矩形的高度
} CRect;

/*
多边形
*/
typedef struct
{
    int n;          //多边形编号
    CPoint p[16];   //最大支持16边形
} CPolygon;


/*
雷达目标与视频目标相匹配的区域
*/
typedef struct
{
    CPolygon video_region;
    CPolygon radar_region;
} RVPolygon;


/*
单车道信息
*/
typedef struct
{
    int lane_id;         //车道号，序号从左往右边
    CLine left_lane;     //左车道线  点从上到下
    CLine right_lane;    //右车道线  点从上到下
} LaneInfo;


/*
多车道信息
*/
typedef struct
{
    int lane_cnt;
    LaneInfo lane_infos[MAX_LANE_NUM];
} LaneInfos;


/*
匹配点
*/
typedef struct
{
    CPoint image_point;      //像素点
    CPoint radar_point;      //雷达点，单位米
} MatchPoint;

/*
返回结果状态
*/
typedef enum Result
{
    SUCCESS = 0,
    FAILED = -1
} Result;


/* 姿态信息 */
typedef struct
{
    float x;
    float y;
    float z;
} AttitudeInfo;


/*
车牌图片
*/
typedef struct
{
    int plate_crop_img_w;                 //车牌图片高度
    int plate_crop_img_h;                 //车牌图片宽度
    unsigned char * plate_crop_img_buf;   //车牌图片，格式RGB24
} PlateCrop;


/*
车牌颜色
*/
typedef enum
{
    PLATE_COLOR_UNKONWN = 0x00, //未知
    BLACK_COLOR, 				// black
    BLUE_COLOR, 				// blue
    GREEN_COLOR, 				// green
    WHITE_COLOR, 				// white
    YELLOW_COLOR, 				// yellow
} PlateColor;


/*
车身颜色
*/
typedef enum
{
    VEHICLE_COLOR_UNKONWN = 0x00,       //未知
    VEHICLE_BLACK_COLOR, 				// black
    VEHICLE_BLUE_COLOR, 				// blue
    VEHICLE_BROWN_COLOR, 				// brown
    VEHICLE_CYAN_COLOR,					// cyan
    VEHICLE_GOLDEN_COLOR,               // golden
    VEHICLE_GRAY_COLOR, 				// gray
    VEHICLE_GREEN_COLOR, 				// green
    VEHICLE_ORANGE_COLOR,               // orange
    VEHICLE_PINK_COLOR,                 // pink
    VEHICLE_PURPLE_COLOR,               // purple
    VEHICLE_RED_COLOR,                  // red
    VEHICLE_WHITE_COLOR,                // white
    VEHICLE_YELLOW_COLOR                // yellow
} VehicleColor;


/*
车辆类型
*/
typedef enum
{
    VEHICLE_TYPE_UNKONWN = 0X00,         //未知
    BOX_TRUCK,                           //集装箱卡车
    GARBAGE_TRUCK,                       //垃圾运输车
    CAR,                                 //轿车
    CLRAN_TRUCK,                         //环卫车
    COACH,                               //大客车
    CONCRETE_MIXER_TRUCK,                //混泥土搅拌车
    CRANE,                               //吊车
    MEDIUM_BUS,                          //中客车
    MINICAR,                             //微型车
    MPV,                                 //mpv
    POLICE_CAR,                          //警车
    LARGE_TRUCK,                         //大货车
    SCHOOL_BUS,                          //校车
    SUV,                                 //suv
    TAXI,                                //出租车
    TRACTOR,                             //拖拉机
    TRAILER,                             //平板拖车
    MEDIUM_TRUCK,                        //中货车
    VAN,                                 //面包车
    FIRE_TRUCK,                          //消防车
    TRICYCLE,                            //三轮车
    DOGCART,                             //两轮车
    LIGHT_PASSENGER,                     //轻客
    OIL_TANK_TRUCK,                      //罐装车
    PICKUP_TRUCK,                        //皮卡车
    SMALL_TRUCK,                         //小货车
    AMBULANCE,                           //救护车
    BULLDOZER,                           //推土车
    BUS,                                 //公交车
    DANGEROUS_GOODS_VEHICLE,             //危险品运输车
    POWDER_MATERIAL_TRANSPORT_VEHICLE,   //粉粒物料运输车
    FARM_TRUCK,                           //农用车
    ENGINEERING_TRUCK                    //工程车
} VehicleType;


/*
目标类型
*/
typedef enum
{
    TARGET_TYPE_UNKONWN = 0x00,  //未知
    TARGET_TYPE_PEDESTRIAN, // 行人
    TARGET_TYPE_DOGCART, 	//两轮车
    TARGET_TYPE_TRICYCLE, 	// 三轮车
    TARGET_TYPE_SMALL_VEHICLE, //小型车
    TARGET_TYPE_MEDIUM_VEHICLE, //中型车
    TARGET_TYPE_BIG_VEHICLE,//大型车
    TARGET_TYPE_MAX
} TargetType;


/*
能见度等级
*/
typedef enum
{
    FOG_UNKNOWN = 0x00,     //未知
    LEVEL_ONE_FOG,          //特强浓雾, 能见度低于50米
    LEVEL_TWO_FOG,          //强浓雾，能见度大于50米低于200米
    LEVEL_THREE_FOG         //浓雾，能见度大于200米低于500米
} FogType;


/* 雷达目标数据 */
typedef struct
{
    unsigned int id;  		//目标id
    unsigned int type; 		//目标类型
    unsigned int lane_id; 	//车道号
    float x_axes; 			//x轴坐标
    float y_axes; 			//y轴坐标
    float x_speed;          //x轴方向速度
    float y_speed;			//y轴方向速度

} RadarTarget;



typedef struct
{
    long long timestamp_ms;        //时间戳，单位毫秒
    int target_cnt;                //目标个数
    RadarTarget radar_target[MAX_RADAR_TARGET]; //目标数据
} RadarTargets;



/*
视频帧
*/
typedef struct
{
    long long timestamp_ms;			//时间戳，单位毫秒
    int detect_width;				//检测图片宽 （640x360）
    int detect_height;				//检测图片高
    unsigned int  detect_buf_len;   //检测图片大小
    unsigned char *detect_img_buf;  //检测图片数据，格式RGB24

    int plate_width;				//车牌图片宽 （4k）
    int plate_height;				//车牌图片高
    unsigned int  plate_buf_len;	//车牌图片大小
    unsigned char *plate_img_buf;   //车牌图片数据，格式RGB24

} VideoFrame;


/*
多路视频流帧数据
*/
typedef struct
{
    int stream_cnt;               //视频路数
    VideoFrame video_frame[MAX_VEIDEO_NUM];   //视频流数据，最多支持32路
} VideoFrames;


/*
融合类型
*/
typedef enum
{
    FUSION_TYPE_RADAR = 0x00,  //只有雷达目标
    FUSION_TYPE_VIDEO, //只有视频目标
    FUSION_TYPE_RADAR_VIDEO, //雷视融合目标
    FUSION_TYPE_MAX
} FusionType;


/*
雷达工作场景类型
*/
typedef enum
{
    ORDINARY = 0,       //0 为普通道路模式（路口）
    HOLOGRAPHY,         //1 为全息模式(路段)
    PARK                //2 为园区模式
} RadarMode;


/*
融合目标
*/
typedef struct
{
    FusionType  fusion_type;              //融合类型
    unsigned int stream_id;               //视频流id
    float score;			              //目标类别分数
    TargetType type;		              //目标类型
    int fusion_id;			              //融合id
    CRect fusion_box;		              //融合框


    int radar_id;			              //雷达目标id
    unsigned int lane_id; 	              //车道号
    float x_axes;   		              //x轴坐标，单位米
    float y_axes; 			              //y轴坐标，单位米
    float x_speed;			              //x轴速度，单位米/秒
    float y_speed;			              //y轴速度，单位米/秒
    float target_length;                  //目标的长度
    float target_width;                   //目标的宽度
    float target_height;                  //目标的高度

    char plate_text[MAX_PLATE_TEXT];      //车牌文字
    PlateColor plate_color;               //车牌颜色
    CRect plate_box;                      //车牌框
    CRect detect_box;                     //原始检测框
    VehicleColor vehicle_color;           //车身颜色
    VehicleType  vehicle_type;            //车辆类型

    PlateCrop plate_crop_img;             //扣车牌图片

    float plate_text_score;               //车牌文本得分
    float plate_color_score;              //车牌颜色得分
    float vehicle_type_score;             //车辆类型得分
    float vehicle_color_score;            //车辆颜色得分
    bool plate_cut_flag;                  //是否存在车牌
    bool spill_detect;		              //抛洒物检测
    bool no_vehicle_detect;               //非机动车入侵检测
    bool accident_detect; 	              //事故检测
    bool road_construction_detect;        //道路施工
    FogType visibility_level;             //能见度检测
    bool fog_fire_detect;                 //烟火检测
    bool capture_flag;                    //绊线抓拍标识
    int32_t plate_img_id;                 //车牌检测图编号


} FusionTarget;



typedef struct
{
    int fusion_cnt;                                      //融合目标数量
    FusionTarget fusion_target[MAX_OBJECT_NUM];          //最大支持512个目标
} FusionTargets;



/********************************************************
 * Description                  视频跟踪的结构体
 * @param fusion_type           目标融合的类型
 * @param x                     目标检测框左上角的x点位
 * @param y                     目标检测框左上角的y点位
 * @param w                     目标检测框的w
 * @param h                     目标检测框的h
 * @param type_score            目标检测框的类别得分
 * @param video_type            目标检测框的类型
 * @param time                  获得的时间
********************************************************/
typedef struct
{
    FusionType fusion_type;
    float rx = 0;
    float ry = 0;
    float rx_speed = 0;
    float ry_speed = 0;
    float radar_type = -1;
    float x = 0;
    float y = 0;
    float w = 0;
    float h = 0;
    float type_score = -1;
    int   video_type  = -1;

    int video_track_id = -1;
    int radar_track_id = -1;
    long long time = 0;
} trackbox;



/*
设备属性参数
*/
typedef struct
{
    int stream_id;                  //相机id, 0:近焦，1：远焦
    float install_height;           //安装高度,单位mm
    float cmos_w;                   //CMOS宽,单位mm
    float cmos_h;		            //CMOS高,单位mm
    float radar_stop_line;          //雷达停止线
    int origin_w;		            //原始图像宽
    int origin_h;		            //原始图像高
    int focus; 			            //镜头焦距，单位mm
    RadarMode radar_mode;           //工作场景类型
    LaneInfos dev_lanes;		    //设备绘制的车道信息
} DeviceAttribute;


/*
行人检测的类型
*/
typedef struct
{
    bool  pedestrain_type;     //行人类型
    bool  dogcart_type;        //二轮车类型
    bool  tricycle_type;       //三轮车类型
    bool  staff_filter;        //是否过滤工作人员
} PedestrainType;

/*
行人检测的方向
*/
typedef enum
{
    DIRECTION_ALL       = 0,    //检测所有的方向
    DIRECTION_ONLY_COME = 1,	//检测来向
    DIRECTION_ONLY_GO   = 2,	//检测去向
} PedestrainDirect;


/*
排队信息
*/
typedef struct
{
    float     dwell_time;		//目标的驻留时间  单位: s
    LaneInfos queue_lanes;		//排队区域       单位: m
} QueueLaneInfo;


/*
算法设置类型
*/
typedef enum
{
    ALG_TYPE_DEVICE_ATTRIIBUTE = 0x00,  //设备属性参数
    ALG_TYPE_MANUAL_CALIBRATION,  		//手动标定(点的个数要大于等于4)
    ALG_TYPE_AUTO_CALIBRATION,          //自动标定
    ALG_TYPE_PLATE_RECOGNITION,         //车牌识别(点的个数限制在4-10个点以内， 并遵循顺时针或逆时针)
    ALG_TYPE_PEDESTRAIN_DETECT,	        //行人检测(点的个数限制在4-10个点以内， 并遵循顺时针或逆时针)
    ALG_TYPE_SPILL_DETECT,				//抛洒物检测(点的个数限制在4-10个点以内， 并遵循顺时针或逆时针)
    ALG_TYPE_ACCIDENT_DETECT,			//事故检测(车道信息，并遵循顺时针或逆时针)
    ALG_TYPE_CALIBRESET,                //标定重置
    ALG_TYPE_QUEUE_DETECT,              //排队事件检测
    ALG_TYPE_COMMON_PARAM,              //通用参数设置
    ALG_TYPE_ALL_PARAMS,                //接受所有的参数，主要用于初始化
    ALG_TYPE_CONSTRUCTION_DETECT,       //施工检测
    ALG_TYPE_VISIBILITY_DETECT,         //能见度检测
    ALG_TYPE_CAPTURE_LINE,              //抓拍绊线
    ALG_TYPE_VISION_DETECT,             //视觉检测区域设置
    ALG_TYPE_FOG_DETECT,                //团雾检测设置
    ALG_TYPE_FIRE_DETECT,               //烟火检测设置
    ALG_TYPE_MAX

} AlgorithmSetType;



/*
标定重置参数
*/
typedef struct
{
    int stream_id;                //标定时分辨近焦/远焦, 0:近焦，1：远焦
    bool is_manual_reset;         //手动标定重置
    bool is_auto_reset;           //自动标定重置
} ResetCalibParams;


/*
自动标定参数
*/
typedef struct
{
    int stream_id;                                    //标定时分辨近焦/远焦, 0:近焦，1：远焦
    bool auto_calib_enbale;                           //自动标定操作，true开启标定；false：停止或终止标定;
    int  match_area_num;		                      //匹配区域个数
    int match_num;				                      //匹配点数
    MatchPoint match_point[MAX_MATCH_POINT];          //匹配点
    RVPolygon calib_areas[MAX_CALIB_AREAS];           //标定匹配区域
} AutoCalibParams;


/*
手动标定参数
*/
typedef struct
{
    //手动标定
    int stream_id;                                           //标定时分辨近焦/远焦, 0:近焦，1：远焦
    int match_num;				                             //匹配点数
    MatchPoint match_point[MAX_MATCH_POINT];                 //匹配点
} ManualCalibParams;


/*
公共参数
*/
typedef struct
{
    int visual_calib_enabled;               //长短焦之间的标定使能, 0:不标定， 1：标定
} CommonParams;


/*
车牌识别参数
*/
typedef struct
{
    int stream_id;                            //标定时分辨近焦/远焦, 0:近焦，1：远焦
    bool plate_enable;                        //车牌检测使能
    int area_count;                           //车牌检测区域个数
    bool plate_enhance;                       //车牌增强使能
    std::vector<CPolygon> plate_areas;        //车牌检测区域,支持多个区域
    std::string administrative_area;          //设备安装所在行政区域
} PlateRecognizeParams;



/*
行人入侵参数
*/
typedef struct
{
    int stream_id;                            //标定时分辨近焦/远焦, 0:近焦，1：远焦
    bool pedestrain_enable;	                  //行人检测的使能
    PedestrainDirect pedestrain_direct;		  //行人检测的方向
    PedestrainType   pedestrain_detect_type;  //行人检测的类型
    int area_count;                           //行人检测区域的个数
    std::vector<CPolygon> pedestrain_areas;   //行人检测的区域,支持多个区域
} PedestrainParams;


/*
抛撒物参数
*/
typedef struct
{
    int stream_id;                            //标定时分辨近焦/远焦, 0:近焦，1：远焦
    bool spill_enable;    	                  //抛撒物检测使能
    int area_count;                           //抛撒物区域个数
    int sensitivity_level;                    //抛洒物事件的灵敏度等级 范围为:1 - 100
    std::vector<CPolygon> spill_areas;	      //抛撒物检测区域，支持多个区域
} SpillParams;



/*
交通事故参数
*/
typedef struct
{
    int stream_id;                            //标定时分辨近焦/远焦, 0:近焦，1：远焦
    bool accident_enable; 	                  //事故检测使能
    int area_count;                           //事故区域个数
    std::vector<CPolygon> accident_areas;     //事故检测区域,支持多个区域
    int accident_duration;                    //事故持续时间,单位s
} AccidentParams;



/*
道路施工参数
*/
typedef struct
{
    int stream_id;                            //0:近焦，1:远焦
    bool construction_enable;                 //施工检测使能
    int area_count;                           //施工检测区域个数
    std::vector<CPolygon> construction_areas; //施工检测区域，支持多个区域
} ConstructionParams;


/*
能见度检测参数
*/
typedef struct
{
    int stream_id;                            //0:近焦，1：远焦
    bool visibility_enable;                   //能见度检测使能
} VisibilityParams;


/*
抓拍绊线
*/
typedef struct
{
    int stream_id;                            //0:近焦，1：远焦
    bool capture_enable;                      //绊线抓拍使能
    CPoint capture_line[2];                   //抓拍绊线
} CaptureLineParams;


/*
视频检测区域
*/
typedef struct
{
    int stream_id;                             //0:近焦，1:远焦
    int area_count;                            //检测区域个数
    std::vector<CPolygon> vision_detect_areas; //视觉检测区域
} VisionDetectParams;



/*
团雾检测参数设置
*/
typedef struct
{
    int stream_id;                             //0:近焦，1:远焦
    bool fog_enable;                           //团雾检测使能
    int islight;                               //白天或者晚上的值  0:白天 1:晚上
    int sensitivity_level;                     //团雾事件的灵敏度等级 范围为:1 - 100
} FogDetectParams;

/*
烟火检测参数设置
*/
typedef struct
{
    int stream_id;                             //0:近焦，1:远焦
    bool fire_enable;                          //烟火检测使能
    int islight;                               //白天或者晚上的值  0:白天 1:晚上
    int sensitivity_level;                     //烟火事件的灵敏度等级 范围为:1 - 100
    int area_count;                            //检测区域个数
    std::vector<CPolygon> fire_detect_areas;   //烟火检测区域
} FireDetectParams;

/*
团雾检测的结果结构体
*/
typedef struct
{
    int stream_id;                                  //0:近焦，1:远焦
    bool exist_fog;                                 //近焦 true: 存在团雾 false:不存在团雾
} FogRet;


/*
团雾检测的结果结构体
*/
typedef struct
{
    int stream_cnt;
    FogRet fog_rets[MAX_VEIDEO_NUM];           //近焦团雾的检测结果
} FogDetectRets;

//算法事件回调，如自动标定过程进度通知，标定点的偏差
typedef void (*algorithm_event_cb)(AlgorithmSetType type, int calib_process_near, int calib_process_far, float calib_errors_near[12], float calib_errors_far[12], void *user_ptr);


//视觉标定(长短焦画面标定)事件回调
typedef void (*visual_calib_cb)(char *visual_calib_process, CPoint *map_points, void *user_ptr);            //四个点为归一化后的值，从左上角点开始按顺时针排列


//视觉标定数据传输, 返回 0：成功，-1：失败
int visual_calib_detect(int32_t dev_grp_index, VideoFrames &video_Frames);


/*
获取分类学习的状态
*/
bool get_vehicle_cls_status(int32_t dev_grp_index);


/*
基于非目标的事件结果获取  0:获取成功 -1:获取失败
*/
int get_events_result(int32_t dev_grp_index, AlgorithmSetType alg_set_type, void *alg_rets);

/*
算法版本，返回0成功；返回-1失败；
*/
int algorithm_version(char *version);

//坐标转换 返回 0:成功; -1:失败
//rv_flag: 1  雷达坐标点向像素坐标系转换
//rv_flag: 0 像素坐标系向雷达坐标点转换
//stream_id:
int rvcoor_convert(int32_t dev_grp_index, MatchPoint *coor, bool rv_flag, int stream_id);

//acl初始化，返回 0:成功; -1:失败
int acl_init();

//acl去初始化，返回 0:成功; -1:失败
int acl_deinit();

//算法初始化，返回 0:成功; -1:失败
int algorithm_init(int32_t dev_grp_index, int stream_cnt,  algorithm_event_cb event_cb,  visual_calib_cb vis_calib_event_cb, void *user_ptr);


//算法参数设置，返回 0:成功; -1:失败
int algorithm_set_param(int32_t dev_grp_index, AlgorithmSetType alg_set_type,  void *alg_param);


//算法检测，返回 0:成功; -1:失败
int algorithm_detect(int32_t dev_grp_index, VideoFrames &video_Frames, RadarTargets *radar_targets, FusionTargets *fusion_targets);


//算法库去初始化，返回 0:成功; -1:失败
int algorithm_deinit(int32_t dev_grp_index);





/***************************** 以下是盒子类型和接口定义 *******************************/
//配置：基准设备信息，融合标定数据
//输入：原始目标id数据（多设备，多线程持续输入数据，每路10hz）
//输出：融合目标id数据（一路持续输出10hz）

/* 算法回调类型 */
typedef enum
{
    CALLBACK_TYPE_TARGETS = 0x00,                           //输出目标
}AlgCallbackType;

/* 标定设备组间的状态 */
typedef enum
{
    GRPS_NOT_CALIB = 0x00,							        //设备组间未标定
    GRPS_CALIB_SUCCESS,							            //设备组间标定成功
    GRPS_CALIB_FAILURE,							            //设备组间标定失败
    GRPS_MAP_SUCCESS,							            //设备组间映射到地图成功
    GRPS_MAP_FAILURE,							            //设备组间映射到地图失败
}GroupCalibStatus;


/*算法设置参数类型（设备间的标定）*/
typedef enum
{
    TECU_ALG_TYPE_GRP_LIST = 0x00,                               //设备组的编号列表
    TECU_ALG_TYPE_BASE_GRP,									    //基准设备组信息
    TECU_ALG_TYPE_CALIB_MATCH,		                            //设备组标定
    TECU_ALG_TYPE_MATCH_LIST,									//全息映射
    TECU_ALG_TYPE_CALIB_HDM,                                     //经纬度的标定（High-Definition Map, HDM）
    TECU_ALG_TYPE_RC_CALIB,                                      //相机与雷视产品的标定 卡口相机与雷视设备组的标定
    TECU_ALG_TYPE_EDGEBOX_DEVICE,                                //边缘智能盒的编号
    TECU_ALG_TYPE_ROAD_CONFIG,                                   //道路配置信息
}TecuAlgorithmSetType;



/* 目标数据 */
typedef struct
{
    int32_t grp_id;                                         //设备组ID
    int32_t stream_id;                                      //0表示远焦，1表示近焦
    unsigned int id;  		                                //目标id     (1 ~ MAX_OBJECT_NUM)
    unsigned int source_type;                               //目标的融合类型
    TargetType target_type;		                            //目标类型
    float      tt_score;                                    //目标分数

    float x; 			                                    //x轴坐标
    float y; 			                                    //y轴坐标
    float angle;                                            //目标图标角度 0-360°
    float speed;			                                //速度 km/h
    double longitude;                                       //目标经度 小数点精确到8位
    double latitude;                                        //目标维度 小数点精确到8位

    char number_plate[MAX_PLATE_TEXT];                      //车牌文字
    float np_score;                                         //车牌文字分数
    PlateColor plate_color;                                 //车牌颜色
    float      pc_score;                                    //车牌颜色分数
    VehicleType vehicle_type;                               //车辆类型
    float       vt_score;                                   //车辆类型分数
    VehicleColor vehicle_color;                             //车身颜色
    float       vc_score;                                   //车身颜色分数

    double direction;                                       //方向角 0-360° pengge edit
    double distance;                                        //距离  pengge edit

    float radar_coord_dist;                                 //目标到雷达坐标系原点的距离
    float radar_coord_angle;                                //目标和雷达坐标系原点的连线与雷达法线的夹角
    long long timestamp_ms;                                 //目标的时间戳
}   TecuTarget;

/* 拼接目标数据 */
typedef struct
{
    unsigned int splice_id;  		                        //拼接id的范围
    unsigned int grp_id;                                    //设备组的id
    long long timestamp_ms;                                 //设备的时间戳 主要用于传递给下一个盒子的数据
    float        splicing_x;                                //基准设备坐标系下的x轴坐标
    float        splicing_y;                                //基准设备坐标系下的y轴坐标
    TecuTarget       splice_target;                         //拼接后的目标信息
}OutputTarget;

/*卡口相机检测的视觉目标*/
typedef struct
{
    unsigned int grp_id;                                    //设备组编号 卡口相机的设备组编号
    bool exist_box;                                         //存在目标框信息的标志位,   true:存在, false:不存在
    CRect target_box;                                       //目标框 (归一化后的目标)
    bool exist_plate;                                       //存在车牌号码信息的标志位， true:存在, false:不存在
    char plate_text[MAX_PLATE_TEXT];                        //车牌文字
}BVisionTarget;


/* 单设备目标信息 */
typedef struct
{
    unsigned int grp_id;                                             //设备组编号 pengge edit
    GroupType   grp_type;                                            //设备的类型，存在雷视一体机和卡口相机
    unsigned int target_cnt;  		                                 //设备检测的目标数量 pengge edit
    long long timestamp_ms;                                          // 每个设备的时间戳
    union
    {
        TecuTarget        target_devices[MAX_OBJECT_NUM];                //设备检测的目标信息
        BVisionTarget bayonet_vision_target[MAX_OBJECT_NUM];         //卡口相机探测的目标信息
    }targets;
} InputTargets;

/* 多设备目标信息 */
typedef struct
{
    unsigned int device_cnt;                                        // 设备数量
    InputTargets device_input[DEV_GRP_NUM_MAX];                     // 设备目标信息
} MultiDeviceTargets;

/*多设备融合目标信息输出*/
//传递给平台的信息
typedef struct
{
    unsigned int target_cnt;						                //输出目标数量
    OutputTarget fusion_targets[MAX_OBJECT_NUM*DEV_GRP_NUM_MAX];    //输出目标信息
}OutputTargets;

// 平台接力模式输入结构体
typedef struct
{
    MultiDeviceTargets multi_device_targets;                       // 多设备目标信息
    OutputTargets last_output_target;                              // 最后一个目标信息
}MultiBoxTargets;





/*设备信息*/
typedef struct
{
    unsigned int	 grp_id;                                //设备组编号

    double           grp_longitude;                         //设备组的经度 小数点精确到8位 单位:度
    double           grp_latitude;                          //设备组的维度 小数点精确到8位 单位:度
    double           grp_direction;                         //方位角 pengge edit      单位:度（0~360度，顺时针旋转）
    float            x;                                     //web墨卡托坐标系下的x轴坐标
    float            y;                                     //web墨卡托坐标系下的y轴坐标
    bool             grp_enable;            		   	    //设备组启用标志, true:启用, false:不启用 pengge edit
} GroupInfo;

/*卡口相机信息*/
typedef struct
{
    unsigned int grp_id;                                    //卡口相机设备组编号
    float height;                                           //卡口相机安装高度,单位mm
    float cmos_width;                                       //CMOS宽,单位mm
    float cmos_height;		                                //CMOS高,单位mm
    int image_width;		                                //原始图像宽
    int image_height;		                                //原始图像高
    int focus; 			                                    //镜头焦距，单位mm
}BCameraInfo;





/*标定点*/
typedef struct
{
    float        x;
    float        y;
} CalibPoint;


/*标定点组*/
typedef struct
{
    CalibPoint point1;
    CalibPoint point2;
} MatchPoints;

/*两设备间的绑定*/
typedef struct
{
    unsigned int grp1_id;							        //设备组1的标定点
    unsigned int grp2_id;							        //设备组2的标定点
    unsigned int calib_match_cnt;                           //设备组间标定点的数量
    MatchPoints  match_points[MAX_MATCH_NUM];	            //设备组1的标定点
    GroupCalibStatus calib_status;                          //设备组间的标定状态
    bool        calib_enable;                               //设备标定组是否启用的标志, true:启用, false:不启用
}CalibMatch;


/*卡口相机与雷达(雷视)设备的标定点组*/
typedef struct
{
    CalibPoint image_point;                                 //单位:像素 归一化后的像素点(0~1)
    CalibPoint radar_point;                                 //单位:mm
} RcMatchPoints;

/*雷视设备与卡口相机的标定*/
typedef struct
{
    GroupInfo     rv_grp_info;							        //雷视设备组信息
    BCameraInfo   bc_grp_info;                                  //卡口相机的设备组信息
    unsigned int  calib_match_cnt;                              //设备组间标定点的数量
    RcMatchPoints rc_calib_points[MAX_MATCH_NUM];	            //设备组1的标定点
    float         calib_errors[MAX_MATCH_NUM];                  //标定的误差值数组
    GroupCalibStatus   calib_status;                            //设备组间的标定状态
    bool          calib_enable;                                 //设备组标定是否启用的标志, true:启用, false:不启用
}RcCalibMatch;



struct CoordinateAlg
{
    double longitude;                                       // 经度 保留8位小数
    double latitude;                                        // 纬度 保留8位小数

    CoordinateAlg()
    {
        longitude = 0;
        latitude  = 0;
    }
};

struct LaneAlg {
    std::string lane_id;                                   // 车道id
    uint32_t lane_type;                                    // 车道类型 1 虚线 2直线
    uint32_t direction;                                    // 车道方向 0：来向；1：去向；2双向
    uint32_t trun_type;                                    // 转向类型 0：直行；1：左转；2：右转；3：直行+左转；4：直行+右转；5：掉头；6：掉头+左转；
    uint32_t lane_index;                                   //车道索引
    std::vector<CoordinateAlg> coordinate;

    LaneAlg() {
        lane_id = "";
        lane_type = 1;
        direction = 0;
        trun_type = 0;
    }
};

struct RoadConfigAlg {
    std::string road_id;                                    // 道路id guid
    std::string road_name;                                  // 道路名称 guid
    double road_width;                                      // 道路宽度 精度0.01
    double road_lane_width;                                 // 车道宽度 精度0.01
    std::vector<CoordinateAlg> coordinate_vec;
    std::vector<LaneAlg>       lane_vec;                    //车道信息
    std::vector<GroupInfo>     grp_info_vec;                //车道内的雷视一体机设备信息

    RoadConfigAlg() {
        road_id = "";
        road_name = "";
        road_width = 0;
        road_lane_width = 0;
    };
};


/*
 * @Name: algorithm_output_cb
 * @Description: 算法输出回调函数指针类型，用于传递算法处理后输出的当前目标数据以及下一帧的目标数据。
 *               算法在完成数据处理后会调用此回调函数，用户可通过该函数获取算法输出的结果。
 *
 * @Input
 * callbackType: 算法回调类型，标识回调的数据种类
 * channel_id: 通道ID，标识回调所属的通道
 * output_targets: 发送给当前盒子的数据
 * output_targets_next: 发送下一个盒子的数据(平台接力模式)
 * user_ptr: 用户自定义数据指针，用于传递上下文信息
 *
 * @Output
 * 返回值: 整型值，用户可通过返回结果判断是否成功处理回调数据
 *
 * @Edit History
 * Date: 2025/02/07
 * Time: 10:00
 * Author: JIATAO
 * Content:
 */
// typedef int(*algorithm_output_cb)(AlgCallbackType callbackType, int cshannel_id, OutputTargets *output_targets, void *user_ptr);
typedef int(*algorithm_output_cb)(AlgCallbackType callbackType, int channel_id, OutputTargets *output_targets, OutputTargets *output_targets_next, void *user_ptr);

//算法初始化，返回 0:成功; -1:失败；
int tecu_algorithm_init(char *version);

//使用自定义配置文件初始化算法，返回 0:成功; -1:失败；
int tecu_algorithm_init_with_config(char *version, const char *config_path);


//算法创建通道，失败返回:-1；成功返回channel_id：>=0;
int tecu_algorithm_open_channel(algorithm_output_cb output_cb, void *user_ptr);


//算法参数设置(标定/重置/传参)，返回 0:成功; -1:失败
// 参数示例
//	Deivce[]              device_list;                           //设备信息数组                                           ALG_TYPE_DEVICE_LIST
//	int                   base_grp_id;                           //基准设备组id                                            ALG_TYPE_BASE_GRP
//	CalibMatch            calib_match;				             //设备间的标定匹配点                                      ALG_TYPE_CALIB_MATCH
//	CalibMatch[]          calib_match_list;				         //全息融合                                              ALG_TYPE_MATCH_LIST
//  RcCalibMatch          rc_calib_match;                        //雷达(雷视)与卡口相机的标定                               ALG_TYPE_RC_CALIB
//  Deivce[]              device_list;                           //基于经纬度的标定                                        ALG_TYPE_CALIB_HDM
// int                    edgebox_id;                            //边缘智能盒的编号（唯一,该边缘智能盒子编号用于生成ID的起始范围） ALG_TYPE_EDGEBOX_DEVICE 边缘智能盒从0开始计算
// RoadConfigAlg          road_config;                           //道路配置信息传输                                        ALG_TYPE_ROAD_CONFIG
int tecu_algorithm_set_param(int channel_id, TecuAlgorithmSetType alg_set_type, void *alg_param, unsigned int cnt);


//算法启动，返回 0:成功; -1:失败；启用新配置, 调用algorithm_target_input必须调用algorithm_start接口
int tecu_algorithm_start(int channel_id);


//算法目标输入，返回 0:成功; -1:失败；algorithm_start成功后可输入数据
/*
    输入参数：
        channel_id: 通道id
        multi_device_targets: 多设备目标信息
        timestamp_ms: 时间戳 代表发送数据时的时间戳，不一定与每个设备的时间戳相同
*/
int tecu_algorithm_target_input(int channel_id, MultiDeviceTargets *multi_device_targets, long long timestamp_ms);

/*
    输入参数：
        channel_id: 通道id
        multi_box_targets: 多设备目标信息
        timestamp_ms: 时间戳 代表发送数据时的时间戳，不一定与每个设备的时间戳相同
*/
int tecu_algorithm_target_input_multi_box(int channel_id,
                                          MultiBoxTargets *multi_box_targets,
                                          long long timestamp_ms);


//算法关闭通道，返回 0:成功; -1:失败；
int tecu_algorithm_close_channel(int channel_id);


//算法库去初始化，返回 0:成功;- 1:失败
int tecu_algorithm_deinit();

#endif