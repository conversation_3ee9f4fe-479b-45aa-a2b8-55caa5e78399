// algorithm_lib.cpp : 此文件包含 "main" 函数。程序执行将在此处开始并结束。
//
#include <string.h>
#include <vector>
#include "zlogger.h"
#include "tecu1000_algorithm_header.h"
#include "algorithm_header.h"
#include <typeinfo>


ZLOGGER_HEADER_DEFINE("ALG_LIB")

// 结构体复制函数声明
void copy_CRect(CRect* dst, const CRect* src);
// void copy_EventState(EventState* dst, const EventState* src);
// void copy_Target(Target* dst, const Target* src);
void copy_OutputTarget(OutputTarget* dst, const OutputTarget* src);
void copy_BVisionTarget(BVisionTarget* dst, const BVisionTarget* src);
void copy_InputTargets(InputTargets* dst, const InputTargets* src);
void copy_MultiDeviceTargets(MultiDeviceTargets* dst, const MultiDeviceTargets* src);
void copy_OutputTargets(OutputTargets* dst, const OutputTargets* src);
void copy_MultiBoxTargets(MultiBoxTargets* dst, const MultiBoxTargets* src);
void copy_DeivceInfo(GroupInfo* dst, const GroupInfo* src);
void copy_BCameraInfo(BCameraInfo* dst, const BCameraInfo* src);
void copy_CalibPoint(CalibPoint* dst, const CalibPoint* src);
void copy_MatchPoints(MatchPoints* dst, const MatchPoints* src);
void copy_CalibMatch(CalibMatch* dst, const CalibMatch* src);
void copy_RcMatchPoints(RcMatchPoints* dst, const RcMatchPoints* src);
void copy_RcCalibMatch(RcCalibMatch* dst, const RcCalibMatch* src);
void copy_CoordinateAlg(CoordinateAlg* dst, const CoordinateAlg* src);
void copy_LaneAlg(LaneAlg* dst, const LaneAlg* src);
void copy_RoadConfigAlg(RoadConfigAlg* dst, const RoadConfigAlg* src);

// 命名空间间类型转换函数声明
void convert_CRect(tecu1000_alg::CRect* dst, const CRect* src);
void convert_Target(tecu1000_alg::Target* dst, const TecuTarget* src);
// void convert_EventState(tecu1000_alg::EventState* dst, const EventState* src);
void convert_BVisionTarget(tecu1000_alg::BVisionTarget* dst, const BVisionTarget* src);
void convert_InputTargets(tecu1000_alg::InputTargets* dst, const InputTargets* src);
void convert_MultiDeviceTargets(tecu1000_alg::MultiDeviceTargets* dst, const MultiDeviceTargets* src);
void convert_MultiBoxTargets(tecu1000_alg::MultiBoxTargets* dst, const MultiBoxTargets* src);

// 反向转换函数声明 - 从tecu1000_alg命名空间转换到普通命名空间
void convert_Target_reverse(TecuTarget* dst, const tecu1000_alg::Target* src);
void convert_OutputTarget_reverse(OutputTarget* dst, const tecu1000_alg::OutputTarget* src);
void convert_OutputTargets_reverse(OutputTargets* dst, const tecu1000_alg::OutputTargets* src);

// 添加类型转换函数
tecu1000_alg::AlgorithmSetType convertAlgSetType(TecuAlgorithmSetType type) {
    // 确保两个枚举值匹配
    switch (type) {
        case TECU_ALG_TYPE_GRP_LIST: return tecu1000_alg::ALG_TYPE_DEVICE_LIST;
        case TECU_ALG_TYPE_BASE_GRP: return tecu1000_alg::ALG_TYPE_BASE_DEVICE;
        case TECU_ALG_TYPE_CALIB_MATCH: return tecu1000_alg::ALG_TYPE_CALIB_MATCH;
        case TECU_ALG_TYPE_MATCH_LIST: return tecu1000_alg::ALG_TYPE_MATCH_LIST;
        case TECU_ALG_TYPE_CALIB_HDM: return tecu1000_alg::ALG_TYPE_CALIB_HDM;
        case TECU_ALG_TYPE_RC_CALIB: return tecu1000_alg::ALG_TYPE_RC_CALIB;
        case TECU_ALG_TYPE_EDGEBOX_DEVICE: return tecu1000_alg::ALG_TYPE_EDGEBOX_DEVICE;
        case TECU_ALG_TYPE_ROAD_CONFIG: return tecu1000_alg::ALG_TYPE_ROAD_CONFIG;
        case ALG_TYPE_MAX: return tecu1000_alg::ALG_TYPE_MAX;
        default: return tecu1000_alg::ALG_TYPE_DEVICE_LIST; // 默认值
    }
}

// 创建一个全局变量存储用户的回调函数和上下文
static algorithm_output_cb g_user_callback = nullptr;
static void* g_user_context = nullptr;

// 回调函数适配器 - 将tecu1000_alg命名空间的类型转换为普通命名空间的类型
int callback_adapter(tecu1000_alg::AlgCallbackType callbackType, int channel_id,
                    tecu1000_alg::OutputTargets* output_targets,
                    tecu1000_alg::OutputTargets* output_targets_next,
                    void* user_ptr) {
    // ZINFO("callback_adapter: output_targets.target_cnt:%d output_targets_next.target_cnt:%d \n",
    //         output_targets->target_cnt, output_targets_next->target_cnt);
    if (!g_user_callback) {
        // ZERROR("callback_adapter g_user_callback is nullptr! \n");
        return -1;
    }else{
        // ZINFO("callback_adapter ok");
    }

    // 转换回调类型
    AlgCallbackType callback_type = static_cast<AlgCallbackType>(callbackType);

    // 创建临时OutputTargets对象用于存储转换后的数据
    OutputTargets converted_output_targets_obj;
    OutputTargets converted_output_targets_next_obj;

    // 使用显式的属性复制操作替代指针类型转换
    if (output_targets != nullptr) {
        // 使用反向转换函数将tecu1000_alg::OutputTargets转换为OutputTargets
        convert_OutputTargets_reverse(&converted_output_targets_obj, output_targets);
    }

    if (output_targets_next != nullptr) {
        // 使用反向转换函数将tecu1000_alg::OutputTargets转换为OutputTargets
        convert_OutputTargets_reverse(&converted_output_targets_next_obj, output_targets_next);
    }

    // 获取临时对象的指针
    OutputTargets converted_output_targets = converted_output_targets_obj;
    OutputTargets converted_output_targets_next = converted_output_targets_next_obj;

    // ZINFO("callback_output: converted_output_targets.target_cnt:%d converted_output_targets_next.target_cnt:%d \n",
    //         converted_output_targets.target_cnt, converted_output_targets_next.target_cnt);
    // // 简单打印 融合id和经纬度
    // for (size_t tosv = 0; tosv < converted_output_targets.target_cnt; tosv++)
    // {
    //     ZINFO("converted_output_targets: splice_id:%d (%.7f,%.7f) \n",
    //             converted_output_targets.fusion_targets[tosv].splice_id,
    //             converted_output_targets.fusion_targets[tosv].splice_target.longitude,
    //             converted_output_targets.fusion_targets[tosv].splice_target.latitude);
    // }
    // 调用用户回调函数
    return g_user_callback(callback_type, channel_id, &converted_output_targets,
                         &converted_output_targets_next, g_user_context);
}

// 添加结构体复制函数
// CRect结构体复制函数
void copy_CRect(CRect* dst, const CRect* src) {
    if (dst && src) {
        dst->x = src->x;
        dst->y = src->y;
        dst->width = src->width;
        dst->height = src->height;
    }
}

// Target结构体复制函数
void copy_Target(TecuTarget* dst, const TecuTarget* src) {
    if (dst && src) {
        dst->grp_id = src->grp_id;
        dst->id = src->id;
        dst->source_type = src->source_type;
        dst->target_type = src->target_type;
        dst->tt_score = src->tt_score;

        dst->x = src->x;
        dst->y = src->y;
        dst->angle = src->angle;
        dst->speed = src->speed;
        dst->longitude = src->longitude;
        dst->latitude = src->latitude;

        memcpy(dst->number_plate, src->number_plate, MAX_PLATE_TEXT);
        dst->np_score = src->np_score;
        dst->plate_color = src->plate_color;
        dst->pc_score = src->pc_score;
        dst->vehicle_type = src->vehicle_type;
        dst->vt_score = src->vt_score;
        dst->vehicle_color = src->vehicle_color;
        dst->vc_score = src->vc_score;

        dst->direction = src->direction;
        dst->distance = src->distance;

        dst->radar_coord_dist = src->radar_coord_dist;
        dst->radar_coord_angle = src->radar_coord_angle;
        dst->timestamp_ms = src->timestamp_ms;
    }
}

// OutputTarget结构体复制函数
void copy_OutputTarget(OutputTarget* dst, const OutputTarget* src) {
    if (dst && src) {
        dst->splice_id = src->splice_id;
        dst->grp_id = src->grp_id;
        dst->timestamp_ms = src->timestamp_ms;
        dst->splicing_x = src->splicing_x;
        dst->splicing_y = src->splicing_y;

        copy_Target(&dst->splice_target, &src->splice_target);

        // dst->longitude_input = src->longitude_input;
        // dst->latitude_input = src->latitude_input;
    }
}

// BVisionTarget结构体复制函数
void copy_BVisionTarget(BVisionTarget* dst, const BVisionTarget* src) {
    if (dst && src) {
        dst->grp_id = src->grp_id;
        dst->exist_box = src->exist_box;

        copy_CRect(&dst->target_box, &src->target_box);

        dst->exist_plate = src->exist_plate;
        memcpy(dst->plate_text, src->plate_text, MAX_PLATE_TEXT);
    }
}

// InputTargets结构体复制函数
void copy_InputTargets(InputTargets* dst, const InputTargets* src) {
    if (dst && src) {
        dst->grp_id = src->grp_id;
        dst->grp_type = src->grp_type;
        dst->target_cnt = src->target_cnt;
        dst->timestamp_ms = src->timestamp_ms;

        if (src->grp_type == GRP_TYPE_BINOCULAR_RV) {
            // 如果是雷视一体机，复制Target数据
            for (unsigned int i = 0; i < src->target_cnt && i < MAX_OBJECT_NUM; i++) {
                copy_Target(&dst->targets.target_devices[i], &src->targets.target_devices[i]);
            }
        } else if (src->grp_type == GRP_TYPE_BAYONET_CAMERA) {
            // 如果是卡口相机，复制BVisionTarget数据
            for (unsigned int i = 0; i < src->target_cnt && i < MAX_OBJECT_NUM; i++) {
                copy_BVisionTarget(&dst->targets.bayonet_vision_target[i], &src->targets.bayonet_vision_target[i]);
            }
        }
    }
}

// MultiDeviceTargets结构体复制函数
void copy_MultiDeviceTargets(MultiDeviceTargets* dst, const MultiDeviceTargets* src) {
    if (dst && src) {
        dst->device_cnt = src->device_cnt;

        // 复制每个设备的目标信息
        for (unsigned int i = 0; i < src->device_cnt && i < DEVICE_MAX_NUM; i++) {
            copy_InputTargets(&dst->device_input[i], &src->device_input[i]);
        }
    }
}

// OutputTargets结构体复制函数
void copy_OutputTargets(OutputTargets* dst, const OutputTargets* src) {
    if (dst && src) {
        dst->target_cnt = src->target_cnt;

        // 复制每个输出目标
        for (unsigned int i = 0; i < src->target_cnt && i < MAX_OBJECT_NUM * DEVICE_MAX_NUM; i++) {
            copy_OutputTarget(&dst->fusion_targets[i], &src->fusion_targets[i]);
        }
    }
}

// MultiBoxTargets结构体复制函数
void copy_MultiBoxTargets(MultiBoxTargets* dst, const MultiBoxTargets* src) {
    if (dst && src) {
        // 复制多设备目标信息
        copy_MultiDeviceTargets(&dst->multi_device_targets, &src->multi_device_targets);

        // 复制最后一个目标信息
        copy_OutputTargets(&dst->last_output_target, &src->last_output_target);
    }
}

// DeivceInfo结构体复制函数
void copy_DeivceInfo(GroupInfo* dst, const GroupInfo* src) {
    if (dst && src) {
        dst->grp_id = src->grp_id;
        dst->grp_longitude = src->grp_longitude;
        dst->grp_latitude = src->grp_latitude;
        dst->grp_direction = src->grp_direction;
        dst->x = src->x;
        dst->y = src->y;
        dst->grp_enable = src->grp_enable;
    }
}

// BCameraInfo结构体复制函数
void copy_BCameraInfo(BCameraInfo* dst, const BCameraInfo* src) {
    if (dst && src) {
        dst->grp_id = src->grp_id;
        dst->height = src->height;
        dst->cmos_width = src->cmos_width;
        dst->cmos_height = src->cmos_height;
        dst->image_width = src->image_width;
        dst->image_height = src->image_height;
        dst->focus = src->focus;
    }
}

// CalibPoint结构体复制函数
void copy_CalibPoint(CalibPoint* dst, const CalibPoint* src) {
    if (dst && src) {
        dst->x = src->x;
        dst->y = src->y;
    }
}

// MatchPoints结构体复制函数
void copy_MatchPoints(MatchPoints* dst, const MatchPoints* src) {
    if (dst && src) {
        copy_CalibPoint(&dst->point1, &src->point1);
        copy_CalibPoint(&dst->point2, &src->point2);
    }
}

// CalibMatch结构体复制函数
void copy_CalibMatch(CalibMatch* dst, const CalibMatch* src) {
    if (dst && src) {
        dst->grp1_id = src->grp1_id;
        dst->grp2_id = src->grp2_id;
        dst->calib_match_cnt = src->calib_match_cnt;

        // 复制标定点数组
        for (unsigned int i = 0; i < src->calib_match_cnt && i < MAX_MATCH_NUM; i++) {
            copy_MatchPoints(&dst->match_points[i], &src->match_points[i]);
        }

        dst->calib_status = src->calib_status;
        dst->calib_enable = src->calib_enable;
    }
}

// RcMatchPoints结构体复制函数
void copy_RcMatchPoints(RcMatchPoints* dst, const RcMatchPoints* src) {
    if (dst && src) {
        copy_CalibPoint(&dst->image_point, &src->image_point);
        copy_CalibPoint(&dst->radar_point, &src->radar_point);
    }
}

// RcCalibMatch结构体复制函数
void copy_RcCalibMatch(RcCalibMatch* dst, const RcCalibMatch* src) {
    if (dst && src) {
        // 复制设备信息
        copy_DeivceInfo(&dst->rv_grp_info, &src->rv_grp_info);
        copy_BCameraInfo(&dst->bc_grp_info, &src->bc_grp_info);

        dst->calib_match_cnt = src->calib_match_cnt;

        // 复制标定点数组
        for (unsigned int i = 0; i < src->calib_match_cnt && i < MAX_MATCH_NUM; i++) {
            copy_RcMatchPoints(&dst->rc_calib_points[i], &src->rc_calib_points[i]);
            dst->calib_errors[i] = src->calib_errors[i];
        }

        dst->calib_status = src->calib_status;
        dst->calib_enable = src->calib_enable;
    }
}

// CoordinateAlg结构体复制函数
void copy_CoordinateAlg(CoordinateAlg* dst, const CoordinateAlg* src) {
    if (dst && src) {
        dst->longitude = src->longitude;
        dst->latitude = src->latitude;
    }
}

// LaneAlg结构体复制函数
void copy_LaneAlg(LaneAlg* dst, const LaneAlg* src) {
    if (dst && src) {
        dst->lane_id = src->lane_id;
        dst->lane_type = src->lane_type;
        dst->direction = src->direction;
        dst->trun_type = src->trun_type;
        dst->lane_index = src->lane_index;

        // 复制坐标数组
        dst->coordinate.clear();
        dst->coordinate.reserve(src->coordinate.size());
        for (size_t i = 0; i < src->coordinate.size(); i++) {
            CoordinateAlg coord;
            copy_CoordinateAlg(&coord, &src->coordinate[i]);
            dst->coordinate.push_back(coord);
        }
    }
}

// RoadConfigAlg结构体复制函数
void copy_RoadConfigAlg(RoadConfigAlg* dst, const RoadConfigAlg* src) {
    if (dst && src) {
        dst->road_id = src->road_id;
        dst->road_name = src->road_name;
        dst->road_width = src->road_width;
        dst->road_lane_width = src->road_lane_width;

        // 复制坐标数组
        dst->coordinate_vec.clear();
        dst->coordinate_vec.reserve(src->coordinate_vec.size());
        for (size_t i = 0; i < src->coordinate_vec.size(); i++) {
            CoordinateAlg coord;
            copy_CoordinateAlg(&coord, &src->coordinate_vec[i]);
            dst->coordinate_vec.push_back(coord);
        }

        // 复制车道数组
        dst->lane_vec.clear();
        dst->lane_vec.reserve(src->lane_vec.size());
        for (size_t i = 0; i < src->lane_vec.size(); i++) {
            LaneAlg lane;
            copy_LaneAlg(&lane, &src->lane_vec[i]);
            dst->lane_vec.push_back(lane);
        }

        // 复制设备数组
        dst->grp_info_vec.clear();
        dst->grp_info_vec.reserve(src->grp_info_vec.size());
        for (size_t i = 0; i < src->grp_info_vec.size(); i++) {
            GroupInfo device;
            copy_DeivceInfo(&device, &src->grp_info_vec[i]);
            dst->grp_info_vec.push_back(device);
        }
    }
}

// 命名空间间的结构体转换函数实现
// GroupType转DeviceType
tecu1000_alg::DeviceType convertGroupType(GroupType type) {
    // 确保两个枚举值匹配
    switch (type) {
        case GRP_TYPE_BINOCULAR_RV: return tecu1000_alg::Radar_Vision_Device;
        case GRP_TYPE_MONOCULAR_RV: return tecu1000_alg::Bayonet_Camera_Device;
        case GRP_TYPE_BAYONET_CAMERA: return tecu1000_alg::Radar_Vision_Device;
        case GRP_TYPE_FISHEYE_CAMERA: return tecu1000_alg::Radar_Vision_Device;
        case GRP_TYPE_RADAR: return tecu1000_alg::Radar_Vision_Device;
        default: return tecu1000_alg::Radar_Vision_Device; // 默认值
    }
}


// CRect结构体转换函数
void convert_CRect(tecu1000_alg::CRect* dst, const CRect* src) {
    if (dst && src) {
        dst->x = src->x;
        dst->y = src->y;
        dst->width = src->width;
        dst->height = src->height;
    }
}

// Target结构体转换函数
void convert_Target(tecu1000_alg::Target* dst, const TecuTarget* src) {
    if (dst && src) {
        // 将设备ID(int32_t)直接转换为字符串
        snprintf(dst->device_ip, MAX_DEVICE_IP, "%d", src->grp_id);
        dst->id = src->id;
        dst->source_type = src->source_type;
        dst->target_type = static_cast<tecu1000_alg::TargetType>(src->target_type);
        dst->tt_score = src->tt_score;

        dst->x = src->x;
        dst->y = src->y;
        dst->angle = src->angle;
        dst->speed = src->speed;
        dst->longitude = src->longitude;
        dst->latitude = src->latitude;

        memcpy(dst->number_plate, src->number_plate, MAX_PLATE_TEXT);
        dst->np_score = src->np_score;
        dst->plate_color = static_cast<tecu1000_alg::PlateColor>(src->plate_color);
        dst->pc_score = src->pc_score;
        dst->vehicle_type = static_cast<tecu1000_alg::VehicleType>(src->vehicle_type);
        dst->vt_score = src->vt_score;
        dst->vehicle_color = static_cast<tecu1000_alg::VehicleColor>(src->vehicle_color);
        dst->vc_score = src->vc_score;

        dst->direction = src->direction;
        dst->distance = src->distance;

        dst->radar_coord_dist = src->radar_coord_dist;
        dst->radar_coord_angle = src->radar_coord_angle;
        dst->timestamp_ms = src->timestamp_ms;
    }
}

// BVisionTarget结构体转换函数
void convert_BVisionTarget(tecu1000_alg::BVisionTarget* dst, const BVisionTarget* src) {
    if (dst && src) {
        dst->device_id = src->grp_id;
        dst->exist_box = src->exist_box;

        convert_CRect(&dst->target_box, &src->target_box);

        dst->exist_plate = src->exist_plate;
        memcpy(dst->plate_text, src->plate_text, MAX_PLATE_TEXT);
    }
}

// InputTargets结构体转换函数
void convert_InputTargets(tecu1000_alg::InputTargets* dst, const InputTargets* src) {
    if (dst && src) {
        dst->device_id = src->grp_id;
        dst->device_type = convertGroupType(src->grp_type);
        dst->target_cnt = src->target_cnt;
        dst->timestamp_ms = src->timestamp_ms;

        if (src->grp_type == GRP_TYPE_BINOCULAR_RV) {
            // 如果是雷视一体机，转换Target数据
            for (unsigned int i = 0; i < src->target_cnt && i < MAX_OBJECT_NUM; i++) {
                convert_Target(&dst->targets.target_devices[i], &src->targets.target_devices[i]);
            }
        } else if (src->grp_type == GRP_TYPE_BAYONET_CAMERA) {
            // 如果是卡口相机，转换BVisionTarget数据
            for (unsigned int i = 0; i < src->target_cnt && i < MAX_OBJECT_NUM; i++) {
                convert_BVisionTarget(&dst->targets.bayonet_vision_target[i], &src->targets.bayonet_vision_target[i]);
            }
        }
    }
}

// MultiDeviceTargets结构体转换函数
void convert_MultiDeviceTargets(tecu1000_alg::MultiDeviceTargets* dst, const MultiDeviceTargets* src) {
    if (dst && src) {
        dst->device_cnt = src->device_cnt;

        // 转换每个设备的目标信息
        for (unsigned int i = 0; i < src->device_cnt && i < DEVICE_MAX_NUM; i++) {
            convert_InputTargets(&dst->device_input[i], &src->device_input[i]);
        }
    }
}

// OutputTarget结构体转换函数
void convert_OutputTarget(tecu1000_alg::OutputTarget* dst, const OutputTarget* src) {
    if (dst && src) {
        dst->splice_id = src->splice_id;
        dst->device_id = src->grp_id;
        dst->timestamp_ms = src->timestamp_ms;
        dst->splicing_x = src->splicing_x;
        dst->splicing_y = src->splicing_y;

        // 转换目标信息
        convert_Target(&dst->splice_target, &src->splice_target);

        dst->longitude_input = 0;
        dst->latitude_input = 0;
    }
}

// OutputTargets结构体转换函数
void convert_OutputTargets(tecu1000_alg::OutputTargets* dst, const OutputTargets* src) {
    if (dst && src) {
        dst->target_cnt = src->target_cnt;

        // 转换所有目标
        for (unsigned int i = 0; i < src->target_cnt && i < MAX_OBJECT_NUM * DEVICE_MAX_NUM; i++) {
            convert_OutputTarget(&dst->fusion_targets[i], &src->fusion_targets[i]);
        }
    }
}

// MultiBoxTargets结构体转换函数
void convert_MultiBoxTargets(tecu1000_alg::MultiBoxTargets* dst, const MultiBoxTargets* src) {
    if (dst && src) {
        // 转换多设备目标信息
        convert_MultiDeviceTargets(&dst->multi_device_targets, &src->multi_device_targets);

        // 转换最后一个目标信息
        convert_OutputTargets(&dst->last_output_target, &src->last_output_target);
    }
}

// 算法创建通道
int tecu_algorithm_open_channel(algorithm_output_cb output_cb, void* user_ptr) {
    // 保存用户回调函数和上下文
    g_user_callback = output_cb;
    g_user_context = user_ptr;
    // 打印地址
    ZINFO("g_user_callback:%p, g_user_context:%p \n", g_user_callback, g_user_context);

    return tecu1000_alg::algorithm_open_channel_(callback_adapter, nullptr);
}

// 声明外部C函数 - 使用全局命名空间的函数
// extern "C" {
//     extern int algorithm_init(char* version);
// }

int tecu_algorithm_init(char *version)
{
    // 调用库中的全局命名空间函数，而非命名空间中的函数
    return tecu1000_alg::algorithm_init_(version);
}

/*
 * @Name: tecu_algorithm_init_with_config
 * @Description: 使用自定义配置文件初始化算法
 *
 * @Input
 * version: 算法版本信息输出缓冲区
 * config_path: 自定义配置文件路径
 *
 * @Output
 * 0: 成功
 * -1: 失败
 *
 * @Edit History
 * Date: 2024/12/18
 * Time: 10:00
 * Author: JiaTao
 * Content: 添加支持自定义配置文件的初始化函数
 */
int tecu_algorithm_init_with_config(char *version, const char *config_path)
{
    return tecu1000_alg::algorithm_init_with_config_(version, config_path);
}

//算法关闭通道，返回 0:成功; -1:失败；
int tecu_algorithm_close_channel(int channel_id)
{
    return tecu1000_alg::algorithm_close_channel_(channel_id);
}

//算法参数设置(标定/重置/传参)，返回 0:成功; -1:失败
int tecu_algorithm_set_param(int channel_id, TecuAlgorithmSetType alg_set_type, void *alg_param, unsigned int cnt)
{
    // 使用类型转换函数将 alg_set_type 转换为 tecu1000_alg::AlgorithmSetType
    tecu1000_alg::AlgorithmSetType alg_set_type_ = convertAlgSetType(alg_set_type);
    return tecu1000_alg::algorithm_set_param_(channel_id, alg_set_type_, alg_param, cnt);
}


//算法启动，返回 0:成功; -1:失败; 启用新配置
int tecu_algorithm_start(int channel_id)
{
    return tecu1000_alg::algorithm_start_(channel_id);
}

//算法目标输入，返回 0:成功; -1:失败；algorithm_start成功后可输入数据
int tecu_algorithm_target_input(int channel_id, MultiDeviceTargets *multi_device_targets, long long timestamp_ms)
{
    // 类型转换
    tecu1000_alg::MultiDeviceTargets multi_device_targets_;
    convert_MultiDeviceTargets(&multi_device_targets_, multi_device_targets);
    return tecu1000_alg::algorithm_target_input_(channel_id, &multi_device_targets_, timestamp_ms);
}

//算法目标输入，返回 0:成功; -1:失败；algorithm_start成功后可输入数据
int tecu_algorithm_target_input_multi_box(int channel_id,
                            MultiBoxTargets *multi_box_targets,
                            long long timestamp_ms)
{
    // 空指针检查
    if (!multi_box_targets) {
        printf("错误: multi_box_targets为NULL\n");
        return -1;
    }

    try {
        // 在堆上分配内存，确保内存在函数调用期间有效
        tecu1000_alg::MultiBoxTargets* multi_box_targets_ = new tecu1000_alg::MultiBoxTargets();

        // 类型转换
        convert_MultiBoxTargets(multi_box_targets_, multi_box_targets);

        // 调用命名空间函数
        int result = tecu1000_alg::algorithm_target_input_multi_box_(channel_id, multi_box_targets_, timestamp_ms);

        // 释放内存
        delete multi_box_targets_;

        return result;
    } catch (const std::exception& e) {
        printf("异常: %s\n", e.what());
        return -1;
    } catch (...) {
        printf("未知异常\n");
        return -1;
    }
}

//算法库去初始化，返回 0:成功;- 1:失败
int tecu_algorithm_deinit()
{
    return tecu1000_alg::algorithm_deinit_();
}

// 反向转换函数实现 - 从tecu1000_alg命名空间转换到普通命名空间

// Target反向转换函数
void convert_Target_reverse(TecuTarget* dst, const tecu1000_alg::Target* src) {
    if (dst && src) {
        dst->grp_id = atoi(src->device_ip);
        dst->id = src->id;
        dst->source_type = src->source_type;
        dst->target_type = static_cast<TargetType>(src->target_type);
        dst->tt_score = src->tt_score;

        dst->x = src->x;
        dst->y = src->y;
        dst->angle = src->angle;
        dst->speed = src->speed;
        dst->longitude = src->longitude;
        dst->latitude = src->latitude;

        memcpy(dst->number_plate, src->number_plate, MAX_PLATE_TEXT);
        dst->np_score = src->np_score;
        dst->plate_color = static_cast<PlateColor>(src->plate_color);
        dst->pc_score = src->pc_score;
        dst->vehicle_type = static_cast<VehicleType>(src->vehicle_type);
        dst->vt_score = src->vt_score;
        dst->vehicle_color = static_cast<VehicleColor>(src->vehicle_color);
        dst->vc_score = src->vc_score;

        dst->direction = src->direction;
        dst->distance = src->distance;

        dst->radar_coord_dist = src->radar_coord_dist;
        dst->radar_coord_angle = src->radar_coord_angle;
        dst->timestamp_ms = src->timestamp_ms;
    }
}

// OutputTarget反向转换函数
void convert_OutputTarget_reverse(OutputTarget* dst, const tecu1000_alg::OutputTarget* src) {
    if (dst && src) {
        dst->splice_id = src->splice_id;
        dst->grp_id = src->device_id;
        dst->timestamp_ms = src->timestamp_ms;
        dst->splicing_x = src->splicing_x;
        dst->splicing_y = src->splicing_y;

        // 转换目标信息
        convert_Target_reverse(&dst->splice_target, &src->splice_target);
    }
}

// OutputTargets反向转换函数
void convert_OutputTargets_reverse(OutputTargets* dst, const tecu1000_alg::OutputTargets* src) {
    if (dst && src) {
        dst->target_cnt = src->target_cnt;

        // 转换所有目标
        for (unsigned int i = 0; i < src->target_cnt && i < MAX_OBJECT_NUM * DEVICE_MAX_NUM; i++) {
            convert_OutputTarget_reverse(&dst->fusion_targets[i], &src->fusion_targets[i]);
        }
    }
}



