#pragma once
// 处理指令,防止由于重复包含同一个头文件而导致的重复定义问题;
#include <vector>
#include <string>
// 车牌号码的最大数
#define MAX_PLATE_TEXT 64
// 每帧中目标数量的最大值
#define MAX_OBJECT_NUM 512
// 设备的最大数量
#define DEVICE_MAX_NUM 12
// 最大的匹配点个数
#define MAX_MATCH_NUM 12
// 版本号的最大字节数
#define MAX_VERSION_LENGTH 20
// 设备IP最大字节数
#define MAX_DEVICE_IP 16

// 配置：基准设备信息，融合标定数据
// 输入：原始目标id数据（多设备，多线程持续输入数据，每路10hz）
// 输出：融合目标id数据（一路持续输出10hz）
#pragma region 枚举

// 增加命名空间
namespace tecu1000_alg
{

    /* 矩形 */
    typedef struct
    {
        float x;      // 左上角顶点坐标x
        float y;      // 左上角顶点坐标y
        float width;  // 宽
        float height; // 高
    } CRect;

    /* 目标类型 */
    typedef enum
    {
        TARGET_TYPE_UNKNOWN = 0x00, // 未知
        TARGET_TYPE_PEDESTRIAN,     // 行人
        TARGET_TYPE_DOGCART,        // 两轮车
        TARGET_TYPE_TRICYCLE,       // 三轮车
        TARGET_TYPE_SMALL_VEHICLE,  // 小型车
        TARGET_TYPE_MEDIUM_VEHICLE, // 中型车
        TARGET_TYPE_BIG_VEHICLE,    // 大型车
        TARGET_TYPE_MAX
    } TargetType;

    /* 车牌颜色类型 */
    typedef enum
    {
        PLATE_COLOR_UNKNOWN = 0x00, // 未知
        BLACK_COLOR,                // black
        BLUE_COLOR,                 // blue
        GREEN_COLOR,                // green
        WHITE_COLOR,                // white
        YELLOW_COLOR,               // yellow
    } PlateColor;

    /* 车辆类型 */
    typedef enum
    {
        VEHICLE_TYPE_UNKNOWN = 0X00,       // 未知
        BOX_TRUCK,                         // 集装箱卡车
        GARBAGE_TRUCK,                     // 垃圾运输车
        CAR,                               // 轿车
        CLRAN_TRUCK,                       // 环卫车
        COACH,                             // 大客车
        CONCRETE_MIXER_TRUCK,              // 混泥土搅拌车
        CRANE,                             // 吊车
        MEDIUM_BUS,                        // 中客车
        MINICAR,                           // 微型车
        MPV,                               // mpv
        POLICE_CAR,                        // 警车
        LARGE_TRUCK,                       // 大货车
        SCHOOL_BUS,                        // 校车
        SUV,                               // suv
        TAXI,                              // 出租车
        TRACTOR,                           // 拖拉机
        TRAILER,                           // 平板拖车
        MEDIUM_TRUCK,                      // 中货车
        VAN,                               // 面包车
        FIRE_TRUCK,                        // 消防车
        TRICYCLE,                          // 三轮车
        DOGCART,                           // 两轮车
        LIGHT_PASSENGER,                   // 轻客
        OIL_TANK_TRUCK,                    // 罐装车
        PICKUP_TRUCK,                      // 皮卡车
        SMALL_TRUCK,                       // 小货车
        AMBULANCE,                         // 救护车
        BULLDOZER,                         // 推土车
        BUS,                               // 公交车
        DANGEROUS_GOODS_VEHICLE,           // 危险品运输车
        POWDER_MATERIAL_TRANSPORT_VEHICLE, // 粉粒物料运输车
        FARM_TRUCK                         // 农用车
    } VehicleType;

    /* 车身颜色类型 */
    typedef enum
    {
        VEHICLE_COLOR_UNKNOWN = 0x00, // 未知
        VEHICLE_BLACK_COLOR,          // black
        VEHICLE_BLUE_COLOR,           // blue
        VEHICLE_BROWN_COLOR,          // brown
        VEHICLE_CYAN_COLOR,           // cyan
        VEHICLE_GOLDEN_COLOR,         // golden
        VEHICLE_GRAY_COLOR,           // gray
        VEHICLE_GREEN_COLOR,          // green
        VEHICLE_ORANGE_COLOR,         // orange
        VEHICLE_PINK_COLOR,           // pink
        VEHICLE_PURPLE_COLOR,         // purple
        VEHICLE_RED_COLOR,            // red
        VEHICLE_WHITE_COLOR,          // white
        VEHICLE_YELLOW_COLOR          // yellow
    } VehicleColor;

    /* 事件类型 */
    typedef struct
    {
        unsigned int trigger_count; // 当前目标触发的事件计数
        bool b_illegal_change;      // 非法变道
        bool b_over_speed;          // 超速
        bool b_below_speed;         // 低速
        bool b_retrograde;          // 逆行
        bool b_illegal_parking;     // 非法停车
        bool b_intrusion;           // 入侵
        bool b_accident;            // 事故
        bool b_drag_racing;         // 飙车
        bool b_dist_no_maintain;    // 未保持车距
        bool b_abnormal_parking;    // 异常停车
        bool b_occ_emer;            // 占用应急车道
        bool b_snake_change;        // 蛇形变道
    } EventState;

    /*设备类型信息*/
    typedef enum
    {
        Radar_Vision_Device = 0x00, // 设备类型为雷视一体机
        Bayonet_Camera_Device       // 设备类型为卡口相机
    } DeviceType;

    /* 算法回调类型 */
    typedef enum
    {
        CALLBACK_TYPE_TARGETS = 0x00, // 输出目标
    } AlgCallbackType;

    /* 标定设备间的状态 */
    typedef enum
    {
        DEVICES_NOT_CALIB = 0x00, // 设备间未标定
        DEVICES_CALIB_SUCCESS,    // 设备间标定成功
        DEVICES_CALIB_FAILURE,    // 设备间标定成功
        DEVICES_MAP_SUCCESS,      // 设备间映射到地图成功
        DEVICES_MAP_FAILURE,      // 设备间映射到地图失败
    } CalibStatus;

    /*算法设置参数类型（设备间的标定）*/
    typedef enum
    {
        ALG_TYPE_DEVICE_LIST = 0x00, // 设备的编号列表
        ALG_TYPE_BASE_DEVICE,        // 基准设备信息
        ALG_TYPE_CALIB_MATCH,        // 设备标定
        ALG_TYPE_MATCH_LIST,         // 全息映射
        ALG_TYPE_CALIB_HDM,          // 经纬度的标定（High-Definition Map, HDM）
        ALG_TYPE_RC_CALIB,           // 相机与雷视产品的标定 卡口相机与雷视设备的标定
        ALG_TYPE_EDGEBOX_DEVICE,     // 边缘智能盒的编号
        ALG_TYPE_ROAD_CONFIG,        // 道路配置信息
        ALG_TYPE_MAX
    } AlgorithmSetType;

#pragma endregion

#pragma region 目标类型
    /* 目标数据 */
    typedef struct
    {
        char device_ip[MAX_DEVICE_IP]; // 设备IP
        unsigned int id;               // 目标id     (1 ~ MAX_OBJECT_NUM)
        unsigned int source_type;      // 目标的融合类型
        TargetType target_type;        // 目标类型
        float tt_score;                // 目标分数

        float x;          // x轴坐标
        float y;          // y轴坐标
        float angle;      // 目标图标角度 0-360°
        float speed;      // 速度 km/h
        double longitude; // 目标经度 小数点精确到8位
        double latitude;  // 目标维度 小数点精确到8位

        char number_plate[MAX_PLATE_TEXT]; // 车牌文字
        float np_score;                    // 车牌文字分数
        PlateColor plate_color;            // 车牌颜色
        float pc_score;                    // 车牌颜色分数
        VehicleType vehicle_type;          // 车辆类型
        float vt_score;                    // 车辆类型分数
        VehicleColor vehicle_color;        // 车身颜色
        float vc_score;                    // 车身颜色分数
        EventState event_state;            // 目标的事件触发状态

        double direction; // 方向角 0-360° pengge edit
        double distance;  // 距离  pengge edit

        float radar_coord_dist;  // 目标到雷达坐标系原点的距离
        float radar_coord_angle; // 目标和雷达坐标系原点的连线与雷达法线的夹角
        long long timestamp_ms;  // 目标的时间戳
    } Target;

    /* 拼接目标数据 */
    typedef struct
    {
        unsigned int splice_id; // 拼接id的范围
        unsigned int device_id; // 设备的id
        long long timestamp_ms; // 设备的时间戳 主要用于传递给下一个盒子的数据
        float splicing_x;       // 基准设备坐标系下的x轴坐标
        float splicing_y;       // 基准设备坐标系下的y轴坐标
        Target splice_target;   // 拼接后的目标信息
        double longitude_input; // 目标经度 小数点精确到8位
        double latitude_input;  // 目标维度 小数点精确到8位
    } OutputTarget;

    /*卡口相机检测的视觉目标*/
    typedef struct
    {
        unsigned int device_id;          // 设备编号 卡口相机的设备编号
        bool exist_box;                  // 存在目标框信息的标志位,   true:存在, false:不存在
        CRect target_box;                // 目标框 (归一化后的目标)
        bool exist_plate;                // 存在车牌号码信息的标志位， true:存在, false:不存在
        char plate_text[MAX_PLATE_TEXT]; // 车牌文字
    } BVisionTarget;

    /* 单设备目标信息 */
    typedef struct
    {
        unsigned int device_id;  // 设备编号 pengge edit
        DeviceType device_type;  // 设备的类型，存在雷视一体机和卡口相机
        unsigned int target_cnt; // 设备检测的目标数量 pengge edit
        long long timestamp_ms;  // 每个设备的时间戳
        union
        {
            Target target_devices[MAX_OBJECT_NUM];               // 设备检测的目标信息
            BVisionTarget bayonet_vision_target[MAX_OBJECT_NUM]; // 卡口相机探测的目标信息
        } targets;
    } InputTargets;

    /* 多设备目标信息 */
    typedef struct
    {
        unsigned int device_cnt;                   // 设备数量
        InputTargets device_input[DEVICE_MAX_NUM]; // 设备目标信息
    } MultiDeviceTargets;

    /*多设备融合目标信息输出*/
    // 传递给平台的信息
    typedef struct
    {
        unsigned int target_cnt;                                      // 输出目标数量
        OutputTarget fusion_targets[MAX_OBJECT_NUM * DEVICE_MAX_NUM]; // 输出目标信息
    } OutputTargets;

    // 平台接力模式输入结构体
    typedef struct
    {
        MultiDeviceTargets multi_device_targets; // 多设备目标信息
        OutputTargets last_output_target;        // 最后一个目标信息
    } MultiBoxTargets;

#pragma endregion

#pragma region 设备信息
    /*设备信息*/
    typedef struct
    {
        unsigned int device_id; // 设备编号

        double device_longitude; // 设备的经度 小数点精确到8位 单位:度
        double device_latitude;  // 设备的维度 小数点精确到8位 单位:度
        double device_direction; // 方位角 pengge edit      单位:度（0~360度，顺时针旋转）
        float x;                 // web墨卡托坐标系下的x轴坐标
        float y;                 // web墨卡托坐标系下的y轴坐标
        bool device_enable;      // 设备启用标志, true:启用, false:不启用 pengge edit
    } DeivceInfo;

    /*卡口相机信息*/
    typedef struct
    {
        unsigned int device_id; // 卡口相机设备编号
        float device_height;    // 卡口相机安装高度,单位mm
        float cmos_width;       // CMOS宽,单位mm
        float cmos_height;      // CMOS高,单位mm
        int image_width;        // 原始图像宽
        int image_height;       // 原始图像高
        int focus;              // 镜头焦距，单位mm
    } BCameraInfo;

#pragma endregion

#pragma region 标定信息

    /*标定点*/
    typedef struct
    {
        float x;
        float y;
    } CalibPoint;

    /*标定点组*/
    typedef struct
    {
        CalibPoint device_point1;
        CalibPoint device_point2;
    } MatchPoints;

    /*两设备间的绑定*/
    typedef struct
    {
        unsigned int device1_id;                 // 设备1的标定点
        unsigned int device2_id;                 // 设备2的标定点
        unsigned int calib_match_cnt;            // 设备间标定点的数量
        MatchPoints match_points[MAX_MATCH_NUM]; // 设备1的标定点
        CalibStatus calib_status;                // 设备间的标定状态
        bool calib_enable;                       // 设备标定组是否启用的标志, true:启用, false:不启用
    } CalibMatch;

    /*卡口相机与雷达(雷视)设备的标定点组*/
    typedef struct
    {
        CalibPoint image_point; // 单位:像素 归一化后的像素点(0~1)
        CalibPoint radar_point; // 单位:mm
    } RcMatchPoints;

    /*雷视设备与卡口相机的标定*/
    typedef struct
    {
        DeivceInfo rc_device;                         // 雷视设备信息
        BCameraInfo bc_device;                        // 卡口相机的设备信息
        unsigned int calib_match_cnt;                 // 设备间标定点的数量
        RcMatchPoints rc_calib_points[MAX_MATCH_NUM]; // 设备1的标定点
        float calib_errors[MAX_MATCH_NUM];            // 标定的误差值数组
        CalibStatus calib_status;                     // 设备间的标定状态
        bool calib_enable;                            // 设备标定组是否启用的标志, true:启用, false:不启用
    } RcCalibMatch;

#pragma endregion

#pragma region 路段配置结构体
    struct CoordinateAlg
    {
        double longitude; // 经度 保留8位小数
        double latitude;  // 纬度 保留8位小数

        CoordinateAlg()
        {
            longitude = 0;
            latitude = 0;
        }
    };

    struct LaneAlg
    {
        std::string lane_id; // 车道id
        uint32_t lane_type;  // 车道类型 1 虚线 2直线
        uint32_t direction;  // 车道方向 0：来向；1：去向；2双向
        uint32_t trun_type;  // 转向类型 0：直行；1：左转；2：右转；3：直行+左转；4：直行+右转；5：掉头；6：掉头+左转；
        uint32_t lane_index; // 车道索引
        std::vector<CoordinateAlg> coordinate;

        LaneAlg()
        {
            lane_id = "";
            lane_type = 1;
            direction = 0;
            trun_type = 0;
        }
    };

    struct RoadConfigAlg
    {
        std::string road_id;    // 道路id guid
        std::string road_name;  // 道路名称 guid
        double road_width;      // 道路宽度 精度0.01
        double road_lane_width; // 车道宽度 精度0.01
        std::vector<CoordinateAlg> coordinate_vec;
        std::vector<LaneAlg> lane_vec;      // 车道信息
        std::vector<DeivceInfo> device_vec; // 车道内的雷视一体机设备信息

        RoadConfigAlg()
        {
            road_id = "";
            road_name = "";
            road_width = 0;
            road_lane_width = 0;
        };
    };
#pragma endregion

    /*
     * @Name: algorithm_output_cb
     * @Description: 算法输出回调函数指针类型，用于传递算法处理后输出的当前目标数据以及下一帧的目标数据。
     *               算法在完成数据处理后会调用此回调函数，用户可通过该函数获取算法输出的结果。
     *
     * @Input
     * callbackType: 算法回调类型，标识回调的数据种类
     * channel_id: 通道ID，标识回调所属的通道
     * output_targets: 发送给当前盒子的数据
     * output_targets_next: 发送下一个盒子的数据(平台接力模式)
     * user_ptr: 用户自定义数据指针，用于传递上下文信息
     *
     * @Output
     * 返回值: 整型值，用户可通过返回结果判断是否成功处理回调数据
     *
     * @Edit History
     * Date: 2025/02/07
     * Time: 10:00
     * Author: JIATAO
     * Content:
     */
    // typedef int(*algorithm_output_cb)(AlgCallbackType callbackType, int cshannel_id, OutputTargets *output_targets, void *user_ptr);
    typedef int (*algorithm_output_cb)(AlgCallbackType callbackType, int channel_id, OutputTargets *output_targets, OutputTargets *output_targets_next, void *user_ptr);

    // 算法初始化，返回 0:成功; -1:失败；
    int algorithm_init_(char *version);

    // 使用自定义配置文件初始化算法，返回 0:成功; -1:失败；
    int algorithm_init_with_config_(char *version, const char *config_path);

    // 算法创建通道，失败返回:-1；成功返回channel_id：>=0;
    int algorithm_open_channel_(algorithm_output_cb output_cb, void *user_ptr);

    // 算法参数设置(标定/重置/传参)，返回 0:成功; -1:失败
    //  参数示例
    //	Deivce[]              device_list;                           //设备信息数组                                           ALG_TYPE_DEVICE_LIST
    //	int                   base_device_id;                        //基准设备id                                            ALG_TYPE_BASE_DEVICE
    //	CalibMatch            calib_match;				             //设备间的标定匹配点                                      ALG_TYPE_CALIB_MATCH
    //	CalibMatch[]          calib_match_list;				         //全息融合                                              ALG_TYPE_MATCH_LIST
    //   RcCalibMatch          rc_calib_match;                        //雷达(雷视)与卡口相机的标定                               ALG_TYPE_RC_CALIB
    //   Deivce[]              device_list;                           //基于经纬度的标定                                        ALG_TYPE_CALIB_HDM
    //  int                    edgebox_id;                            //边缘智能盒的编号（唯一,该边缘智能盒子编号用于生成ID的起始范围） ALG_TYPE_EDGEBOX_DEVICE 边缘智能盒从0开始计算
    //  RoadConfigAlg          road_config;                           //道路配置信息传输                                        ALG_TYPE_ROAD_CONFIG
    int algorithm_set_param_(int channel_id, AlgorithmSetType alg_set_type, void *alg_param, unsigned int cnt);

    // 算法启动，返回 0:成功; -1:失败；启用新配置, 调用algorithm_target_input必须调用algorithm_start接口
    int algorithm_start_(int channel_id);

    // 算法目标输入，返回 0:成功; -1:失败；algorithm_start成功后可输入数据
    /*
        输入参数：
            channel_id: 通道id
            multi_device_targets: 多设备目标信息
            timestamp_ms: 时间戳 代表发送数据时的时间戳，不一定与每个设备的时间戳相同
    */
    int algorithm_target_input_(int channel_id, MultiDeviceTargets *multi_device_targets, long long timestamp_ms);

    /*
        输入参数：
            channel_id: 通道id
            multi_box_targets: 多设备目标信息
            timestamp_ms: 时间戳 代表发送数据时的时间戳，不一定与每个设备的时间戳相同
    */
    int algorithm_target_input_multi_box_(int channel_id,
                                         MultiBoxTargets *multi_box_targets,
                                         long long timestamp_ms);

    // 算法关闭通道，返回 0:成功; -1:失败；
    int algorithm_close_channel_(int channel_id);

    // 算法库去初始化，返回 0:成功;- 1:失败
    int algorithm_deinit_();
}