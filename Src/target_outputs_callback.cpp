#include "target_outputs_callback.h"
#include "zlogger.h"
#include "utils.h"
#include <vector>
#include <iomanip>
#include <unordered_map>
#include <pthread.h>

ZLOGGER_HEADER_DEFINE("ALG_TARGET_OUTPUT")

using namespace tecu_r1000_0_algorithm;

TargetOutputCallback::TargetOutputCallback(algorithm_output_cb output_cb, void *user_ptr, int tranfet_channel_id, int tranfer_callback_fps)
{
    // 初始化
    thread_output_targets.target_cnt = 0;
    channel_id = tranfet_channel_id;
    callback_fps = tranfer_callback_fps;

    event_cb_local = output_cb;
    user_ptr_local = user_ptr;

    thread_is_open = false;
    data_ready = false;
    ZINFO("channel id:%d TargetOutputCallback create success,callback_fps:%d init thread_is_open:%d\n",
          channel_id, callback_fps, thread_is_open);
}

TargetOutputCallback::~TargetOutputCallback()
{
    if (nullptr != user_ptr_local)
    {
        delete user_ptr_local;
        user_ptr_local = nullptr;
        ZINFO("channel id:%d user_ptr_local release! \n",
              channel_id);
    }
    std::vector<TargetOutputSmooth>().swap(target_output_smooth_vec);
}

void TargetOutputCallback::thread_open()
{
    thread_is_open = true;
    thread_output_callback = new std::thread(&TargetOutputCallback::algorithm_fusion_output_local, this);

    // 设置线程名称
    char thread_name[16];  // pthread限制名称长度为16字节(包含结束符)
    snprintf(thread_name, sizeof(thread_name), "TargOut_%d", channel_id);

#ifdef __linux__
    // Linux平台设置线程名称
    pthread_setname_np(thread_output_callback->native_handle(), thread_name);
#elif defined(_WIN32)
    // Windows平台可以使用SetThreadDescription (Windows 10+)
    // 需要包含额外的Windows头文件
#endif

    ZINFO("TargetOutputCallback channel id:%d thread_open thread_is_open:%d\n",
          channel_id, thread_is_open);
}

void TargetOutputCallback::thread_close()
{
    thread_is_open = false;

    // 通知等待中的线程退出
    {
        std::lock_guard<std::mutex> lock(callback_mutex);
        data_ready = true;
    }
    callback_condition.notify_one();

    ZINFO("TargetOutputCallback channel id:%d thread_close thread_is_open:%d\n",
          channel_id, thread_is_open);
    if (nullptr != thread_output_callback)
    {
        thread_output_callback->join();
        delete thread_output_callback;
        thread_output_callback = nullptr;
        ZINFO("channel id:%d thread_output_callback release! \n",
              channel_id);
    }
    std::vector<TargetOutputSmooth>().swap(target_output_smooth_vec);
    ZINFO("channel id:%d user_ptr_local release! \n",
          channel_id);
}

/*
 * @Name: notify_data_ready
 * @Description: 通知回调线程数据已准备就绪，触发回调执行
 *
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2025/01/21
 * Time: 14:30
 * Author: JiaTao
 * Content: 添加生产者-消费者模式的事件通知机制
 */
void TargetOutputCallback::notify_data_ready()
{
    {
        std::lock_guard<std::mutex> lock(callback_mutex);
        data_ready = true;
    }
    callback_condition.notify_one();
}

void print_smooth(const OutputTargets thread_output_targets, const long time_ms, std::unordered_map<int, std::vector<std::string>> &log_str)
{
    for (size_t tosv = 0; tosv < thread_output_targets.target_cnt; tosv++)
    {
        auto target = thread_output_targets.fusion_targets[tosv];
        auto &log_entry = log_str[target.splice_id];

        if (log_entry.empty()) {
            log_entry.push_back(std::to_string(time_ms));
        }
        char buf[32];
        snprintf(buf, sizeof(buf), "%.8f", target.splicing_x);
        log_entry.push_back(buf);
        snprintf(buf, sizeof(buf), "%.8f", target.splicing_y);
        log_entry.push_back(buf);
    }
}


/*
 * @Name: algorithm_fusion_output_local
 * @Description: 回调线程主函数，采用生产者-消费者模式等待数据就绪事件
 *
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2025/01/21
 * Time: 14:30
 * Author: JiaTao
 * Content: 将时间间隔控制模式改为事件驱动的生产者-消费者模式
 */
void TargetOutputCallback::algorithm_fusion_output_local()
{
    while (thread_is_open)
    {
        // 等待数据就绪事件
        std::unique_lock<std::mutex> lock(callback_mutex);
        callback_condition.wait(lock, [this] { return data_ready || !thread_is_open; });

        // 检查线程是否应该退出
        if (!thread_is_open)
        {
            break;
        }

        // 重置数据就绪标志
        data_ready = false;
        lock.unlock();

        // 执行回调
        if (event_cb_local != nullptr)
        {
#ifdef DEBUG_DATA_OUTPUT
            ZINFO("callback_output: thread_output_targets.target_cnt:%d thread_output_targets_next.target_cnt:%d \n",
                  thread_output_targets.target_cnt, thread_output_targets_next.target_cnt);
            // 简单打印 融合id和经纬度
            for (size_t tosv = 0; tosv < thread_output_targets.target_cnt; tosv++)
            {
                ZINFO("thread_output_targets: splice_id:%d (%.7f,%.7f) \n",
                      thread_output_targets.fusion_targets[tosv].splice_id,
                      thread_output_targets.fusion_targets[tosv].splice_target.longitude,
                      thread_output_targets.fusion_targets[tosv].splice_target.latitude);
            }
#endif
            event_cb_local(AlgCallbackType::CALLBACK_TYPE_TARGETS, channel_id, &thread_output_targets, &thread_output_targets_next, user_ptr_local);
        }
    }
}

// void TargetOutputCallback::algorithm_fusion_output_local()
// {
//     struct timespec sys_run_time = {0, 0};
//     clock_gettime(CLOCK_MONOTONIC, &sys_run_time);
//     long long callback_time_ms = sys_run_time.tv_sec * 1000 + sys_run_time.tv_nsec / 1000000;
//     long long time_ms = callback_time_ms;
//     while (thread_is_open)
//     {
//         clock_gettime(CLOCK_MONOTONIC, &sys_run_time);
//         time_ms = sys_run_time.tv_sec * 1000 + sys_run_time.tv_nsec / 1000000;
//         bool callback_output = false;
//         if (time_ms - callback_time_ms > 1000 / callback_fps)
//         {
//             callback_output = true;
//             callback_time_ms = time_ms;
//         }
//         if (event_cb_local != nullptr && callback_output == true)
//         {
// #ifdef DEBUG_DATA_OUTPUT
//             ZINFO("callback_output: thread_output_targets.target_cnt:%d thread_output_targets_next.target_cnt:%d \n",
//                   thread_output_targets.target_cnt, thread_output_targets_next.target_cnt);
//             // 简单打印 融合id和经纬度
//             for (size_t tosv = 0; tosv < thread_output_targets.target_cnt; tosv++)
//             {
//                 ZINFO("thread_output_targets: splice_id:%d (%.7f,%.7f) \n",
//                       thread_output_targets.fusion_targets[tosv].splice_id,
//                       thread_output_targets.fusion_targets[tosv].splice_target.longitude,
//                       thread_output_targets.fusion_targets[tosv].splice_target.latitude);
//             }
// #endif
//             event_cb_local(AlgCallbackType::CALLBACK_TYPE_TARGETS, channel_id, &thread_output_targets, &thread_output_targets_next, user_ptr_local);
//         }else{
//             // 等待10ms
//             std::this_thread::sleep_for(std::chrono::milliseconds(5));

//         }
//     }
// }


// void TargetOutputCallback::algorithm_fusion_output_local()
// {
//     struct timespec sys_run_time = {0, 0};
//     clock_gettime(CLOCK_MONOTONIC, &sys_run_time);
//     long long callback_time_ms = sys_run_time.tv_sec * 1000 + sys_run_time.tv_nsec / 1000000;
//     long long time_ms = callback_time_ms;

//     float target_speed = 50;
//     OutputTargets thread_output_targets_test;
//     // 修改为3个目标
//     thread_output_targets_test.target_cnt = 3;
//     long long time_s = 0;

//     while (thread_is_open)
//     {
//         clock_gettime(CLOCK_MONOTONIC, &sys_run_time);
//         time_ms = sys_run_time.tv_sec * 1000 + sys_run_time.tv_nsec / 1000000;
//         bool callback_output = false;

//         if (time_ms - callback_time_ms > 1000 / callback_fps)
//         {
//             callback_output = true;
//             callback_time_ms = time_ms;
//         }
//         if (event_cb_local != nullptr && callback_output == true)
//         {
//             time_s += 50;
//             if (time_s > 20000)
//             {
//                 time_s = 0;
//             }

//             // 生成3个目标的模拟数据
//             for (int i = 0; i < thread_output_targets_test.target_cnt; i++)
//             {
//                 thread_output_targets_test.fusion_targets[i].splice_id = 10001 + i;
//                 thread_output_targets_test.fusion_targets[i].device_id = 1;

//                 // 初始位置在y轴方向上错开,x轴相同
//                 thread_output_targets_test.fusion_targets[i].splicing_x = thread_first_device.x - 3 * (time_s / 50); // 向西移动,x坐标减小
//                 thread_output_targets_test.fusion_targets[i].splicing_y = thread_first_device.y - (7 * i); // 每个目标间隔7米

//                 // 基本运动参数
//                 thread_output_targets_test.fusion_targets[i].splice_target.angle = 270; // 向西的角度
//                 thread_output_targets_test.fusion_targets[i].splice_target.speed = target_speed;
//                 thread_output_targets_test.fusion_targets[i].splice_target.direction = 270;
//                 thread_output_targets_test.fusion_targets[i].splice_target.distance = 0;
//                 thread_output_targets_test.fusion_targets[i].splice_target.radar_coord_dist = 0;
//                 thread_output_targets_test.fusion_targets[i].splice_target.radar_coord_angle = 0;

//                 // 车辆信息
//                 snprintf(thread_output_targets_test.fusion_targets[i].splice_target.number_plate,
//                         MAX_PLATE_TEXT, "TEST%d", i);
//                 thread_output_targets_test.fusion_targets[i].splice_target.np_score = 0.95;
//                 thread_output_targets_test.fusion_targets[i].splice_target.plate_color = PlateColor::BLUE_COLOR;
//                 thread_output_targets_test.fusion_targets[i].splice_target.pc_score = 0.9;
//                 thread_output_targets_test.fusion_targets[i].splice_target.vehicle_type = VehicleType::BUS;
//                 thread_output_targets_test.fusion_targets[i].splice_target.vt_score = 0.85;
//                 thread_output_targets_test.fusion_targets[i].splice_target.vehicle_color = VehicleColor::VEHICLE_WHITE_COLOR;
//                 thread_output_targets_test.fusion_targets[i].splice_target.vc_score = 0.8;
//                 thread_output_targets_test.fusion_targets[i].splice_target.event_state.b_snake_change = true;

//                 // 转换为经纬度
//                 Utils::mercatorToLonLat(thread_output_targets_test.fusion_targets[i].splicing_x,
//                                       thread_output_targets_test.fusion_targets[i].splicing_y,
//                                       thread_output_targets_test.fusion_targets[i].splice_target.longitude,
//                                       thread_output_targets_test.fusion_targets[i].splice_target.latitude);
//             }
//             thread_output_targets = thread_output_targets_test;
//             for (int i = 0; i < thread_output_targets.target_cnt; i++)
//             {
//                 printf("callback: target %d longitude:%f latitude:%f x:%f y:%f timestamp_ms:%lld \n",
//                  i, thread_output_targets.fusion_targets[i].splice_target.longitude, thread_output_targets.fusion_targets[i].splice_target.latitude,
//                  thread_output_targets.fusion_targets[i].splicing_x, thread_output_targets.fusion_targets[i].splicing_y,
//                  input_data_timestamp_ms);
//             }
//             event_cb_local(AlgCallbackType::CALLBACK_TYPE_TARGETS, channel_id, &thread_output_targets, &thread_output_targets_next, user_ptr_local);


//         }
//     }
// }
