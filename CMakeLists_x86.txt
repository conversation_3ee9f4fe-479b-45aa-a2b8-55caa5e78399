# 本地x86编译配置，用于调试
cmake_minimum_required(VERSION 3.5.1)

# 项目信息
project(TECU1000_DEBUG)
add_compile_options(-Wno-narrowing)
# 设置 C++ 标准为 14
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED True)
set(CMAKE_BUILD_TYPE Debug)
# 调试模式设置
# set(CMAKE_CXX_FLAGS_DEBUG "-fPIC -O0 -g -Wall")
# set(CMAKE_CXX_FLAGS_RELEASE "-fPIC -O2 -Wall -s")
# set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -pthread -Wall -g")

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/../")
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib")

# 查找 OpenCV 包
find_package(OpenCV REQUIRED)
include_directories(${OpenCV_INCLUDE_DIRS})
# 输出 OpenCV 路径
message(STATUS "OpenCV_INCLUDE_DIRS: ${OpenCV_INCLUDE_DIRS}")

include_directories(
    ${PROJECT_SOURCE_DIR}/
    ${PROJECT_SOURCE_DIR}/Eigen/
    ${PROJECT_SOURCE_DIR}/Calib/
    ${PROJECT_SOURCE_DIR}/Config/
    ${PROJECT_SOURCE_DIR}/DataSave/
    ${PROJECT_SOURCE_DIR}/KalmanFilter/
    ${PROJECT_SOURCE_DIR}/Src/
    ${PROJECT_SOURCE_DIR}/TracksSplicing/
    ${PROJECT_SOURCE_DIR}/LaneSmoothing/
    ${PROJECT_SOURCE_DIR}/CollisionDetection/
    ${PROJECT_SOURCE_DIR}/utils_debug/
    ${PROJECT_SOURCE_DIR}/jsoncpp/
)

file(GLOB Source_Files ${PROJECT_SOURCE_DIR}/Src/*.cpp)
file(GLOB Calib_SRC ${PROJECT_SOURCE_DIR}/Calib/*.cpp)
file(GLOB Config_SRC ${PROJECT_SOURCE_DIR}/Config/*.cpp)
file(GLOB DataSave_SRC ${PROJECT_SOURCE_DIR}/DataSave/*.cpp)
file(GLOB KalmanFilter_SRC ${PROJECT_SOURCE_DIR}/KalmanFilter/*.cpp)
file(GLOB TracksSplicing_SRC ${PROJECT_SOURCE_DIR}/TracksSplicing/*.cpp)
file(GLOB LaneSmoothing_SRC ${PROJECT_SOURCE_DIR}/LaneSmoothing/*.cpp)
file(GLOB CollisionDetection ${PROJECT_SOURCE_DIR}/CollisionDetection/*.cpp)
file(GLOB utils_debug ${PROJECT_SOURCE_DIR}/utils_debug/*.cpp)
file(GLOB jsoncpp ${PROJECT_SOURCE_DIR}/jsoncpp/*.cpp)

# 添加可执行文件
add_executable(tecu1000_debug tecu1000_0_generate_moving_target_oo_test.cpp ${Source_Files} ${Calib_SRC} ${Config_SRC}
        ${DataSave_SRC} ${KalmanFilter_SRC} ${TracksSplicing_SRC} ${LaneSmoothing_SRC} ${CollisionDetection} ${utils_debug} ${jsoncpp})

# 链接库
target_link_libraries(tecu1000_debug 
    stdc++
    pthread
    ${OpenCV_LIBS}
)

# 添加编译定义，用于本地调试
add_definitions(-DLOCAL_DEBUG)

# 打印信息
message(STATUS "配置本地x86调试环境")

# 添加多盒子接力测试可执行文件
add_executable(tecu1000_multi_box_relay_test_v2
    ${PROJECT_SOURCE_DIR}/tecu1000_multi_box_relay_test_v2.cpp
    ${Source_Files}
    ${Calib_SRC}
    ${Config_SRC}
    ${DataSave_SRC}
    ${KalmanFilter_SRC}
    ${TracksSplicing_SRC}
    ${LaneSmoothing_SRC}
    ${CollisionDetection}
    ${utils_debug}
    ${jsoncpp}
)


# 链接库
target_link_libraries(tecu1000_multi_box_relay_test_v2 
    stdc++
    pthread
    ${OpenCV_LIBS}
)
