#save_data 
save_data 0            #算法运行时，是否保存数据的标志 0:不保存 1:保存
save_simulation 1      #算法运行时，保存仿真数据的标志 0:不保存 1:保存
save_match_error 1	   #算法运行时，保存重叠区域匹配误差的标志 0:不保存 1:保存

# devices calib config 
calib_min_cnt 3        #设备两两标定时，最小保存数量
calib_compute_cnt 2    #设备两两标定时，计算的标定矩阵的点的数量
calib_xerror_threshold 1.5 #设备两两标定时，x方向的误差阈值 单位:m
calib_yerror_threshold 2 #设备两两标定时，y方向的误差阈值 单位:m

#devices map config
device_map_min_cnt 1   #设备全息映射时，最少的映射组数

#auto devices map config 
auto_devices_map 0	#
rrcalib_path_number 11  #自动标定时的最少目标数量

# version
config_version 20241217.1 #校验算法配置参数的是否一致

#tracks_splicing_config
using_kalman_filter 1      #预测时使用卡尔曼滤波器 0:不开启 1:开启

max_detect_distance 500.1  #雷达探测的最远探测距离 单位:m
min_detect_distance 20.1    #雷达探测的最近探测距离 单位:m
match_xcoor_distance_theshold 3.85 #重叠区域，目标匹配起批成功后，x方向的关联误差阈值
match_ycoor_distance_theshold 40 #重叠区域，目标匹配起批成功后，y方向的关联误差阈值
batch_xcoor_distance_theshold 3.85 #重叠区域，目标匹配未起批，y方向的关联误差阈值
batch_ycoor_distance_theshold 30 #重叠区域，目标匹配未起批，y方向的关联误差阈值

lose_xcoor_distance_threshold 3.85 #丢失时刻x轴的再关联的阈值
lose_ycoor_distance_threshold 70.0 #丢失时刻y轴的误差阈值

second_lose_xcoor_distance_threshold 3.85
second_lose_ycoor_distance_threshold 70

use_input_noise_filter 1  # 输入噪声消除
use_output_result_smooth 0  # 输出结果平滑
use_collision_detection_correction 0

is_platform_relay_mode 0  # 盒子接力模式 0:第一个盒子  1：中间盒子 2：最后盒子

log_info_level 2  # 0:不打印日志 1:打印简单日志 2: 打印完整日志
