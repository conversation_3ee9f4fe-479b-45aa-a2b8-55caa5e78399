// Copyright (c) 2020 PaddlePaddle Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.


#include "config.h"
#include "zlogger.h"

ZLOGGER_HEADER_DEFINE("ALG_CONFIG")

std::vector<std::string> Config::split(const std::string &str,const std::string &delim)
{
  std::vector<std::string> res;
  if ("" == str)
    return res;
  char *strs = new char[str.length() + 1];
  std::strcpy(strs, str.c_str());

  char *d = new char[delim.length() + 1];
  std::strcpy(d, delim.c_str());

  char *p = std::strtok(strs, d);
  while (p)
  {
    std::string s = p;
    res.emplace_back(s);
    p = std::strtok(NULL, d);
  }
  delete [] strs;
  delete [] d;
  return res;
}

std::map<std::string, std::string> Config::LoadConfig(const std::string &config_path)
{
  auto config = Utility::ReadDict(config_path);
  std::map<std::string, std::string> dict;
  for (int i = 0; i < config.size(); i++)
  {
    // pass for empty line or comment
    if (config[i].size() <= 1 || config[i][0] == '#')
    {
		continue;
    }
    std::vector<std::string> res = split(config[i], " ");
    dict[res[0]] = res[1];
  }
  return dict;
}

void Config::print_config_info()
{
  for (auto iter = config_map_.begin(); iter != config_map_.end(); iter++)
  {
    std::string  str_log = iter->first + " : "  + iter->second;
    ZINFO("config_algorithm log info:%s \n",str_log.c_str());

  }
}