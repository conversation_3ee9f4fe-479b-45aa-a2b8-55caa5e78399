// Copyright (c) 2020 PaddlePaddle Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "utility.h"
#include "zlogger.h"

ZLOGGER_HEADER_DEFINE("ALG_CONFIG")

std::vector<std::string> Utility::ReadDict(const std::string &path) {
	std::ifstream in(path);
	std::string line;
	std::vector<std::string> m_vec;
	if (in) 
	{
		while (std::getline(in, line)) 
		{
		m_vec.emplace_back(line);
		}
	}
	else
	{
        ZERROR("no such path %s config file, exit the program...\n",path.c_str());
		std::cout << "no such label file: " << path << ", exit the program..."<< std::endl;
		exit(1);
	}
	return m_vec;
}


