#pragma once

#include <iomanip>
#include <iostream>
#include <map>
#include <ostream>
#include <string>
#include <vector>
#include "utility.h"



class Config
{
public:
    Config(const std::string &config_file)
	{
		config_map_ = LoadConfig(config_file);
		std::string temp_version = config_map_["config_version"];
		std::cout << "temp_version:" << temp_version << " config_version:" << this->config_version << std::endl;
		if (temp_version != this->config_version)
		{
//            ZERROR("配置文件版本不匹配, 加载的配置文件版本为:%s 需要的配置文件版本为:%s \n",
//                                    temp_version.c_str(), this->config_version.c_str());
			std::cout << "The configuration file version does not match. Loaded version: " << temp_version << ",The required configuration file version is: " << this->config_version << std::endl;
			exit(1);
		}
		try
		{
			this->save_data = bool(stoi(config_map_["save_data"]));
			this->save_simulation = bool(stoi(config_map_["save_simulation"]));
			this->save_match_error = bool(stoi(config_map_["save_match_error"]));

			this->using_kalman_filter = bool(stoi(config_map_["using_kalman_filter"]));
			
			this->calib_min_cnt     = stoi(config_map_["calib_min_cnt"]);
			this->calib_compute_cnt = stoi(config_map_["calib_compute_cnt"]);
			this->calib_xerror_threshold = stof(config_map_["calib_xerror_threshold"]);
			this->calib_yerror_threshold = stof(config_map_["calib_yerror_threshold"]);

			this->device_map_min_cnt = stoi(config_map_["device_map_min_cnt"]);

            this->rrcalib_path_number = stoi(config_map_["rrcalib_path_number"]);
            this->auto_devices_map = stoi(config_map_["auto_devices_map"]);

			this->max_detect_distance = stof(config_map_["max_detect_distance"]);
			this->min_detect_distance = stof(config_map_["min_detect_distance"]);

			this->match_xcoor_distance_theshold = stof(config_map_["match_xcoor_distance_theshold"]);
			this->match_ycoor_distance_theshold = stof(config_map_["match_ycoor_distance_theshold"]);

			this->batch_xcoor_distance_theshold = stof(config_map_["batch_xcoor_distance_theshold"]);
			this->batch_ycoor_distance_theshold = stof(config_map_["batch_ycoor_distance_theshold"]);

            this->lose_xcoor_distance_threshold = stof(config_map_["lose_xcoor_distance_threshold"]);
            this->lose_ycoor_distance_threshold = stof(config_map_["lose_ycoor_distance_threshold"]);

            this->second_lose_xcoor_distance_threshold = stof(config_map_["second_lose_xcoor_distance_threshold"]);
            this->second_lose_ycoor_distance_threshold = stof(config_map_["second_lose_ycoor_distance_threshold"]);

			this->use_input_noise_filter = bool(stoi(config_map_["use_input_noise_filter"]));
			this->use_output_result_smooth = bool(stoi(config_map_["use_output_result_smooth"]));
			this->use_collision_detection_correction = bool(stoi(config_map_["use_collision_detection_correction"]));

			this->is_platform_relay_mode = 	stof(config_map_["is_platform_relay_mode"]);

			this->log_info_level = stoi(config_map_["log_info_level"]);

		}
		catch (const std::exception& e)
		{
//            ZERROR("配置文件与算法版本不匹配...... \n");
			std::cout << "The configuration file version does not match......" << std::endl;
			exit(1);
		}
	}
    std::string config_version = "20250603.1";

    std::string algor_version = "1.5.250603.0";

	uint64_t input_data_max_write_size = 4221225472;

	std::string files_dir_path = "algorithm/data/";

	//保存数据的标志位
	bool save_data = false;
	//保存仿真数据的标志位
	bool save_simulation = false;
	//保存匹配误差的标志位
	bool save_match_error = true;
	//kalman 开启的标志位
	bool using_kalman_filter = false;

	//输出数据的参数
	int   callback_fps = 5;
	unsigned int   target_output_init_cnt = 0;

    //雷达与卡口相机标定的参数
    unsigned int rv_calib_min_cnt = 6;
    float   focal_error           = 0.1f;
    float   error_gap             = 0.1f;
    float   calib_radar_thre      = 2000.f;             //卡口相机与雷达的标定误差 单位:mm
    float   error_xrate           = 0.5f;


    // devices calib config
    unsigned int calib_min_cnt     = 3;
	unsigned int calib_compute_cnt = 2;
	float calib_xerror_threshold = 1;
	float calib_yerror_threshold = 2;

	//devices map config
	unsigned int  device_map_min_cnt = 1;

    //auto devices map config
    bool auto_devices_map = false; //是否开启自动雷达与雷达的标定
    int rrcalib_path_number = 5;

    // init device config
	unsigned int init_base_device_id = 0;

	

	// channel id config
	int channel_id_max              = 100;
	int channel_id_min              = 0;
	int channel_id_init             = -1;
    
	//tracks_splicing config
	float max_detect_distance = 250;           //雷达探测的最远探测距离
	float min_detect_distance = 0;             //雷达探测的最近探测距离


	float match_xcoor_distance_theshold = 2.0f;       //重叠区域，目标匹配起批成功后，x方向的关联误差阈值
	float match_ycoor_distance_theshold = 5.0f;       //重叠区域，目标匹配起批成功后，y方向的关联误差阈值

	float batch_xcoor_distance_theshold = 1.5f;       //重叠区域，目标匹配未起批，y方向的关联误差阈值
	float batch_ycoor_distance_theshold = 4.0f;       //重叠区域，目标匹配未起批，y方向的关联误差阈值

    float lose_xcoor_distance_threshold = 1.5f;        //丢失时刻x轴的再关联的阈值
    float lose_ycoor_distance_threshold = 20.0f;       //丢失时刻y轴的误差阈值

    float second_lose_xcoor_distance_threshold = 1.5f;        //丢失时刻x轴的再关联的阈值
    float second_lose_ycoor_distance_threshold = 20.0f;       //丢失时刻y轴的误差阈值

	bool use_input_noise_filter = false; // 是否开启噪声消除
	bool use_output_result_smooth = false; // 是否开启输出结果平滑
	bool use_collision_detection_correction = false; // 是否开启碰撞检测修正
	unsigned int is_platform_relay_mode = 0; // 是否开启平台接力模式

	int log_info_level = 0; // 日志信息等级 0:不打印日志 1:打印简单日志 2: 打印完整日志
    void print_config_info();

private:
    // Load configuration
    std::map<std::string, std::string> LoadConfig(const std::string &config_file);
    std::vector<std::string> split(const std::string &str,const std::string &delim);
    std::map<std::string, std::string> config_map_;
};
