# 多盒子接力测试说明

## 概述

本测试脚本用于验证TECU1000雷视一体机的多盒子接力功能。通过模拟两个边缘智能盒子的协同工作，验证目标轨迹在盒子间的连续传递和融合效果。

## 功能特点

### 1. 双盒子架构
- **第一个盒子（Box1）**：处理设备ID为1和2的数据，配置为第一个盒子模式（`is_platform_relay_mode = 0`）
- **第二个盒子（Box2）**：处理设备ID为3和4的数据，配置为中间盒子模式（`is_platform_relay_mode = 1`）

### 2. 数据流设计
```
原始数据 -> Box1(设备1,2) -> 输出融合结果 -> Box2(设备3,4 + Box1输出) -> 最终融合结果
```

### 3. 配置文件管理
- `algorithm/config_algorithm_box1.txt`：第一个盒子配置
- `algorithm/config_algorithm_box2.txt`：第二个盒子配置

## 文件结构

```
├── tecu1000_multi_box_relay_test.cpp     # 主测试程序
├── algorithm/
│   ├── config_algorithm_box1.txt         # 第一个盒子配置
│   └── config_algorithm_box2.txt         # 第二个盒子配置
├── build_multi_box_test.sh               # 编译脚本
└── 多盒子接力测试说明.md                  # 本说明文档
```

## 编译方法

### 1. 使用编译脚本（推荐）
```bash
./build_multi_box_test.sh
```

### 2. 手动编译
```bash
g++ -std=c++11 -O2 -Wall -g \
    -I. -ISrc -IConfig -ITracksSplicing -IKalmanFilter -Iutils_debug \
    tecu1000_multi_box_relay_test.cpp \
    Src/algorithm_lib.cpp \
    Src/algorithm_header_lib.cpp \
    Src/pipeline_track_fusion.cpp \
    Config/config.cpp \
    TracksSplicing/tracks_splicing.cpp \
    TracksSplicing/radar_vision_fusion.cpp \
    utils_debug/utils_debug.cpp \
    -lpthread -ljsoncpp \
    -o tecu1000_multi_box_relay_test
```

## 使用方法

### 命令行格式
```bash
./tecu1000_multi_box_relay_test <data_dir_path> <input_data_timems>
```

### 参数说明
- `data_dir_path`：数据目录路径，包含以下文件：
  - `<input_data_timems>.dat`：目标数据文件
  - `<input_data_timems>.txt`：时间戳文件
  - `devicelist.dat`：设备列表文件
- `input_data_timems`：数据文件的时间戳标识

### 使用示例
```bash
./tecu1000_multi_box_relay_test /path/to/data/channel_id_0 2024_05_23_15_32_49
```

## 核心功能验证

### 1. 目标ID连续性验证
- 检查目标在盒子间传递时ID是否保持连续
- 验证目标轨迹的完整性

### 2. 数据流验证
- Box1处理设备1、2的数据，生成融合结果
- Box2接收Box1的输出，结合设备3、4的数据进行二次融合

### 3. 配置验证
- 验证不同盒子使用不同配置文件的正确性
- 确认接力模式参数的有效性

## 调试输出

### 日志级别
程序提供详细的调试日志，包括：
- 盒子初始化状态
- 数据处理进度
- 目标传递信息
- 错误诊断信息

### 关键日志示例
```
Box[1] 初始化成功! channel_id:0 config_path:algorithm/config_algorithm_box1.txt
Box[2] 初始化成功! channel_id:1 config_path:algorithm/config_algorithm_box2.txt
Box[1] 处理帧 0: 设备数量=2, 时间戳=1684567890000
Box[2] 接收到来自Box[1]的 5 个目标
Box[2] 处理帧 0: 设备数量=2, 接力目标数量=5, 时间戳=1684567890000
```

## 预期结果

### 成功指标
1. **初始化成功**：两个盒子都能正确初始化并加载各自的配置文件
2. **数据处理正常**：能够正确读取和处理数据文件
3. **目标传递有效**：Box2能够接收到Box1的输出目标
4. **融合结果合理**：最终输出的融合结果包含所有设备的目标信息

### 验证要点
1. **配置隔离**：确认两个盒子使用不同的配置参数
2. **设备分离**：验证每个盒子只处理指定的设备数据
3. **接力机制**：确认目标在盒子间的正确传递
4. **性能表现**：监控处理速度和资源使用情况

## 故障排除

### 常见问题
1. **编译错误**：检查依赖库是否安装完整
2. **配置文件错误**：确认配置文件路径和格式正确
3. **数据文件错误**：验证数据文件格式和完整性
4. **权限问题**：确保程序有读取数据文件的权限

### 调试建议
1. 查看详细日志输出
2. 检查配置文件参数
3. 验证数据文件格式
4. 使用较小的数据集进行测试

## 扩展功能

### 支持更多盒子
可以通过修改代码支持3个或更多盒子的接力：
1. 添加新的配置文件
2. 创建新的BoxInstance实例
3. 修改数据过滤逻辑

### 自定义设备分配
可以通过修改`box_devices`向量来自定义每个盒子处理的设备列表。

## 注意事项

1. **内存管理**：程序会自动管理内存，但大数据集可能需要调整缓冲区大小
2. **线程安全**：使用了互斥锁保护共享数据
3. **错误处理**：程序包含完整的错误检查和恢复机制
4. **资源清理**：程序结束时会自动清理所有资源
