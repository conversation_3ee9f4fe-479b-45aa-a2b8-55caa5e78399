# 创建容器
# docker run -d \
#   --name tecu1000_container \
#   -v //home/<USER>/project/TECU1000/TECU1000_0:/project \
#   ubuntu:18.04 \
#   tail -f /dev/null

# 进入容器
docker start tecu1000_container
docker exec -it tecu1000_container /bin/bash -c "cd /project/tecu1000_0_algorithm && ./run_3403.sh -r"
sudo chmod +x build/libtecu_1000_0.so

# 上传到192.168.53.120 root-3403 的/root/data/jiatao/TECU1000_0/tecu1000_0_testdir的目录下
# scp build/libtecu_1000_0.so root@192.168.53.120:/root/data/jiatao/TECU1000_0/tecu1000_0_testdir
