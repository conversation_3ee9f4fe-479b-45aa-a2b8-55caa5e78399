#pragma once
#include "tracks_splicing_header.h"

namespace tecu_r1000_0_algorithm
{

    /**
     * 碰撞类型
     */
    typedef enum
    {
        CollisionType_None,  // 正常
        CollisionType_Front,  // 追尾碰撞
        CollisionType_Back,   // 被追尾碰撞
    }CollisionType;

    /**
     * 
     */
    typedef struct
    {
        long long timestamp_ms;
        Eigen::VectorXd kf_update_res;
    }TargetPoint;

    /*
     * @Name: SpeedVector
     * @Description: 表示速度向量的结构体，包含x/y方向速度分量
     */
    struct SpeedVector {
        float x;  // X轴方向速度分量
        float y;  // Y轴方向速度分量

        /*
         * @Name: operator-
         * @Description: 重载减法运算符实现速度向量差值计算
         * @Input
         * other: 要减去的速度向量
         * 
         * @Output
         * 返回新的SpeedVector实例，包含计算后的速度差值
         */
        SpeedVector operator-(const SpeedVector& other) const {
            return {x - other.x, y - other.y};
        }
    };

    /**
     * 车辆碰撞检测结构体
     * splicing_id: 拼接ID
     * update_target_point: 更新目标点
     * amend_target_point: 纠正目标点
     * target_output_path: 目标点路径
     * target_output_path_amend: 纠正目标点路径
     * collision_type: 碰撞类型
     * lane_id: 车道ID
     * target_timestamp_ms: 目标点时间戳
     * target_start_timestamp_ms: 目标点开始时间戳
     */
    typedef struct
    {
        int splicing_id;
        std::vector<TargetPoint> target_output_path;
        CollisionType collision_type;
        SplicingTrackState splicing_track_state;
        Eigen::VectorXd kf_update_res;
        int lane_id;
        long long target_timestamp_ms;
        long long target_start_timestamp_ms;
        int device_id;
        float dt;
    }VehicleCollisionDetection;
}

