#include "collisionDetection.h"
#include "zlogger.h"

using namespace tecu_r1000_0_algorithm;

ZLOGGER_HEADER_DEFINE("ALG_COLLISION_DETECTION")

CollisionDetection::CollisionDetection()
{
    vehicle_collision_detection_vec_clear();
}

CollisionDetection::~CollisionDetection()
{
    vehicle_collision_detection_vec_clear();
}

void CollisionDetection::vehicle_collision_detection_vec_clear()
{
    for (size_t li = 0; li < vehicle_collision_detection_vec.size(); li++)
    {
        // 先清空内部的target_output_path向量
        std::vector<TargetPoint>().swap(vehicle_collision_detection_vec[li].target_output_path);

        // 确保kf_update_res被正确释放
        if (vehicle_collision_detection_vec[li].kf_update_res.size() > 0) {
            // 创建一个空的VectorXd并交换，确保内存被释放
            Eigen::VectorXd empty_vector;
            vehicle_collision_detection_vec[li].kf_update_res.swap(empty_vector);
        }
    }

    // 最后清空整个向量
    std::vector<VehicleCollisionDetection>().swap(vehicle_collision_detection_vec);
}

void CollisionDetection::vehicle_collision_detection_and_correction(std::vector<SplicingTarget> &splicing_output_targets_vecs,
                                                                   long long time_step)
{
    // 车辆碰撞检测容器按照lane_id分组
    std::map<int, std::vector<VehicleCollisionDetection>> vehicle_collision_detection_vec_map;

    // vehicle_collision_detection_vec_容器的更新和删除
    vehicle_collision_detection_vec_update(splicing_output_targets_vecs,
                                          vehicle_collision_detection_vec,
                                          VEHICLE_COLLISION_DETECTION_PATH_MAX_DURATION,
                                          time_step);

    // 车辆碰撞检测容器按照lane_id分组
    vehicle_collision_detection_vec_group_by_lane_id(vehicle_collision_detection_vec,
                                                    vehicle_collision_detection_vec_map);

    // 车辆碰撞检测
    vehicle_collision_detection_vec_collision_detection(vehicle_collision_detection_vec_map);

    // 碰撞检测后的修正
    vehicle_collision_detection_vec_correction(vehicle_collision_detection_vec_map);

    // 更新拼接目标容器
    update_splicing_output_targets_vec(vehicle_collision_detection_vec_map,
                                      splicing_output_targets_vecs);

    // 函数结束前清理map中的数据，避免内存泄漏和重复释放
    for (auto &lane_pair : vehicle_collision_detection_vec_map) {
        for (auto &vehicle : lane_pair.second) {
            // 清理内部vector
            std::vector<TargetPoint>().swap(vehicle.target_output_path);

            // 清理Eigen::VectorXd
            if (vehicle.kf_update_res.size() > 0) {
                Eigen::VectorXd empty_vector;
                vehicle.kf_update_res.swap(empty_vector);
            }
        }
        // 清理每个车道的车辆向量
        std::vector<VehicleCollisionDetection>().swap(lane_pair.second);
    }
    // 清空整个map
    vehicle_collision_detection_vec_map.clear();
}

void CollisionDetection::vehicle_collision_detection_vec_update(const std::vector<SplicingTarget> &splicing_output_targets_vec,
                                                                std::vector<VehicleCollisionDetection> &vehicle_collision_detection_vec_,
                                                                long long path_max_duration,
                                                                long long time_step)
{
    // 更新 vehicle_collision_detection_vec_容器的target_output_path
    for (size_t sotv = 0; sotv < splicing_output_targets_vec.size(); sotv++)
    {
        if (splicing_output_targets_vec[sotv].target_output.fusion_id == 0)
        {
            continue;
        }


        // 创建TargetPoint结构
        TargetPoint target_point_with_time = {
            time_step,
            splicing_output_targets_vec[sotv].target_kalman->kf_update_res
        };

        // 检查目标是否已经存在于容器中
        bool exist_target = false;
        unsigned int exist_index = 0;
        for (size_t vcdv = 0; vcdv < vehicle_collision_detection_vec_.size(); vcdv++)
        {
            if (splicing_output_targets_vec[sotv].target_output.fusion_id == vehicle_collision_detection_vec_[vcdv].splicing_id)
            {
                exist_target = true;
                exist_index = vcdv;
                break;
            }
        }

        // 更新或添加目标信息
        if (exist_target)
        {
            // 现有目标，更新轨迹
            vehicle_collision_detection_vec_[exist_index].lane_id = splicing_output_targets_vec[sotv].lane_id;
            vehicle_collision_detection_vec_[exist_index].splicing_track_state = splicing_output_targets_vec[sotv].target_output.splicing_state;
            vehicle_collision_detection_vec_[exist_index].target_output_path.push_back(target_point_with_time);
            vehicle_collision_detection_vec_[exist_index].target_timestamp_ms = time_step;
            vehicle_collision_detection_vec_[exist_index].device_id = splicing_output_targets_vec[sotv].target_output.device_id;
            vehicle_collision_detection_vec_[exist_index].kf_update_res = splicing_output_targets_vec[sotv].target_kalman->kf_update_res;
            vehicle_collision_detection_vec_[exist_index].dt = splicing_output_targets_vec[sotv].target_kalman->get_dt();
        }
        else
        {
            // 新目标，创建新条目
            VehicleCollisionDetection new_target;
            new_target.splicing_id = splicing_output_targets_vec[sotv].target_output.fusion_id;
            new_target.target_output_path.push_back(target_point_with_time);
            new_target.lane_id = splicing_output_targets_vec[sotv].lane_id;
            new_target.splicing_track_state = splicing_output_targets_vec[sotv].target_output.splicing_state;
            new_target.collision_type = CollisionType_None;
            new_target.target_timestamp_ms = time_step;
            new_target.target_start_timestamp_ms = time_step;
            new_target.device_id = splicing_output_targets_vec[sotv].target_output.device_id;
            new_target.kf_update_res = splicing_output_targets_vec[sotv].target_kalman->kf_update_res;
            new_target.dt = splicing_output_targets_vec[sotv].target_kalman->get_dt();
            vehicle_collision_detection_vec_.push_back(new_target);
        }
    }

    // 删除不再存在于输入目标中的目标
    std::vector<VehicleCollisionDetection>::iterator it = vehicle_collision_detection_vec_.begin();
    while (it != vehicle_collision_detection_vec_.end())
    {
        bool exist_target = false;
        for (size_t sotv = 0; sotv < splicing_output_targets_vec.size(); sotv++)
        {
            if (it->splicing_id == splicing_output_targets_vec[sotv].target_output.fusion_id)
            {
                exist_target = true;
                break;
            }
        }

        if (!exist_target)
        {
            it = vehicle_collision_detection_vec_.erase(it);
        }
        else
        {
            ++it;
        }
    }

    // 限制每个目标的轨迹长度，删除过旧的轨迹点
    for (size_t vcdv = 0; vcdv < vehicle_collision_detection_vec_.size(); vcdv++)
    {
        // 循环删除过旧点，直到满足时间要求
        while (true)
        {
            size_t path_length = vehicle_collision_detection_vec_[vcdv].target_output_path.size();
            if (path_length <= 2)
            {
                break;  // 保留至少2个点
            }

            // 计算时间差
            long long current_time = vehicle_collision_detection_vec_[vcdv].target_timestamp_ms;
            long long path_time = current_time - vehicle_collision_detection_vec_[vcdv].target_start_timestamp_ms;

            if (path_time <= path_max_duration)
            {
                break;  // 时间差在允许范围内，不需要删除
            }

            // 删除最旧的点
            vehicle_collision_detection_vec_[vcdv].target_output_path.erase(
                vehicle_collision_detection_vec_[vcdv].target_output_path.begin());

            // 更新起始时间戳为第一个点的时间戳
            if (!vehicle_collision_detection_vec_[vcdv].target_output_path.empty())
            {
                vehicle_collision_detection_vec_[vcdv].target_start_timestamp_ms =
                    vehicle_collision_detection_vec_[vcdv].target_output_path[0].timestamp_ms;
            }
        }
    }
}

// 车辆碰撞检测
void CollisionDetection::vehicle_collision_detection_vec_collision_detection(std::map<int, std::vector<VehicleCollisionDetection>> &vehicle_collision_detection_vec_map)
{
    // 遍历车辆碰撞检测容器
    for (auto &lane_pair : vehicle_collision_detection_vec_map)
    {
        int lane_id = lane_pair.first;                        // 获取键（车道ID）
        auto &vehicles = lane_pair.second;                    // 获取值（车辆向量）

        if(vehicles.size() <= 1)
        {
            continue;
        }

        // 遍历该车道的车辆
        for (size_t vcdv = 0; vcdv < vehicles.size() - 1; vcdv++)
        {
            // 如果轨迹长度不满足时间要求，则跳过
            if (vehicles[vcdv].target_timestamp_ms - vehicles[vcdv].target_start_timestamp_ms < (VEHICLE_COLLISION_DETECTION_PATH_MAX_DURATION / 2) ||
                vehicles[vcdv + 1].target_timestamp_ms - vehicles[vcdv + 1].target_start_timestamp_ms < (VEHICLE_COLLISION_DETECTION_PATH_MAX_DURATION / 2))
            {
                continue;
            }
            // 依次检测车辆是否与后车发生碰撞
            VehicleCollisionDetection &current_vehicle = vehicles[vcdv];
            VehicleCollisionDetection &next_vehicle = vehicles[vcdv + 1];
            if(is_vehicle_collision(current_vehicle, next_vehicle))
            {
                // 如果两个车辆都是跟踪状态，则认为是异常情况
                if(current_vehicle.splicing_track_state == SplicingTrackState::Tracked && next_vehicle.splicing_track_state == SplicingTrackState::Tracked)
                {
#ifdef DEBUG_VEHICLE_COLLISION_DETECTION
                    ZINFO("collision detection: current_vehicle continue: %d, state: %d, device_id: %d, kf_update_res: (%f, %f, %f, %f), next_vehicle: %d, state: %d, device_id: %d, kf_update_res: (%f, %f, %f, %f) \n",
                    current_vehicle.splicing_id, current_vehicle.splicing_track_state, current_vehicle.device_id,
                    current_vehicle.kf_update_res[0], current_vehicle.kf_update_res[1], current_vehicle.kf_update_res[2], current_vehicle.kf_update_res[3],
                    next_vehicle.splicing_id, next_vehicle.splicing_track_state, next_vehicle.device_id,
                    next_vehicle.kf_update_res[0], next_vehicle.kf_update_res[1], next_vehicle.kf_update_res[2], next_vehicle.kf_update_res[3]);
#endif
                    continue;
                }
                current_vehicle.collision_type = CollisionType_Front;
                next_vehicle.collision_type = CollisionType_Back;
#ifdef DEBUG_VEHICLE_COLLISION_DETECTION
                ZINFO("collision detection: current_vehicle: %d, state: %d, device_id: %d, kf_update_res: (%f, %f, %f, %f), next_vehicle: %d, state: %d, device_id: %d, kf_update_res: (%f, %f, %f, %f) \n",
                current_vehicle.splicing_id, current_vehicle.splicing_track_state, current_vehicle.device_id,
                current_vehicle.kf_update_res[0], current_vehicle.kf_update_res[1], current_vehicle.kf_update_res[2], current_vehicle.kf_update_res[3],
                next_vehicle.splicing_id, next_vehicle.splicing_track_state, next_vehicle.device_id,
                next_vehicle.kf_update_res[0], next_vehicle.kf_update_res[1], next_vehicle.kf_update_res[2], next_vehicle.kf_update_res[3]);
#endif
            }
        }
    }
}
// 将vehicle_collision_detection_vec_按照lane_id分组 并且按照x坐标从小到大排序
void CollisionDetection::vehicle_collision_detection_vec_group_by_lane_id(const std::vector<VehicleCollisionDetection> &vehicle_collision_detection_vec_,
                                                                          std::map<int, std::vector<VehicleCollisionDetection>> &vehicle_collision_detection_vec_map)
{
    // 先清空目标map，避免重复添加
    vehicle_collision_detection_vec_map.clear();

    // 遍历车辆碰撞检测容器
    for (size_t vcdv = 0; vcdv < vehicle_collision_detection_vec_.size(); vcdv++)
    {
        // 确保进行深拷贝
        VehicleCollisionDetection vehicle_copy = vehicle_collision_detection_vec_[vcdv];
        // 确保kf_update_res是深拷贝
        if (vehicle_collision_detection_vec_[vcdv].kf_update_res.size() > 0) {
            vehicle_copy.kf_update_res = vehicle_collision_detection_vec_[vcdv].kf_update_res;
        }
        vehicle_collision_detection_vec_map[vehicle_collision_detection_vec_[vcdv].lane_id].push_back(vehicle_copy);
    }

    // 对每个lane_id下的车辆碰撞检测容器进行排序
    for (auto &lane_id : vehicle_collision_detection_vec_map)
    {
        std::sort(lane_id.second.begin(), lane_id.second.end(), [](const VehicleCollisionDetection &a, const VehicleCollisionDetection &b) {
            return a.kf_update_res[0] < b.kf_update_res[0];
        });
    }
}

void print_vehicle_collision_detection_vec(const std::map<int, std::vector<VehicleCollisionDetection>> &vehicle_collision_detection_vec_map)
{
    for (auto &lane_pair : vehicle_collision_detection_vec_map)
    {
        ZINFO("print_vehicle_collision_detection_vec lane_id: %d", lane_pair.first);
        for (auto &vehicle : lane_pair.second)
        {
            ZINFO("splicing_id: %d, lane_id: %d, collision_type: %d x: %f y: %f", vehicle.splicing_id, vehicle.lane_id, vehicle.collision_type, vehicle.kf_update_res[0], vehicle.kf_update_res[1]);
        }
    }
}

bool CollisionDetection::is_vehicle_collision(const VehicleCollisionDetection& current_vehicle,
                                             const VehicleCollisionDetection& next_vehicle)
{
    // 获取当前时刻两车位置
    const auto& p1 = current_vehicle.kf_update_res;
    const auto& p2 = next_vehicle.kf_update_res;

    // 获取两车速度矢量，直接从kf_update_res获取
    float v1_x = p1[2];
    float v1_y = p1[3];
    float v2_x = p2[2];
    float v2_y = p2[3];

    // 计算相对运动参数
    const float delta_x = p2[0] - p1[0];
    const float delta_y = p2[1] - p1[1];
    const float relative_speed_x = v2_x - v1_x;  // 后车相对前车的速度
    const float relative_speed_y = v2_y - v1_y;

    // 立即碰撞检测（当前距离）
    const float current_distance_sq = delta_x*delta_x + delta_y*delta_y;
    if (current_distance_sq < VEHICLE_COLLISION_DETECTION_DISTANCE_THRESHOLD*VEHICLE_COLLISION_DETECTION_DISTANCE_THRESHOLD) {
#ifdef DEBUG_VEHICLE_COLLISION_DETECTION
        ZINFO("collision detection: current_vehicle now: %d, state: %d, device_id: %d, kf_update_res: (%f, %f, %f, %f), next_vehicle: %d, state: %d, device_id: %d, kf_update_res: (%f, %f, %f, %f) \n",
        current_vehicle.splicing_id, current_vehicle.splicing_track_state, current_vehicle.device_id,
        current_vehicle.kf_update_res[0], current_vehicle.kf_update_res[1], current_vehicle.kf_update_res[2], current_vehicle.kf_update_res[3],
        next_vehicle.splicing_id, next_vehicle.splicing_track_state, next_vehicle.device_id,
        next_vehicle.kf_update_res[0], next_vehicle.kf_update_res[1], next_vehicle.kf_update_res[2], next_vehicle.kf_update_res[3]);
#endif
        return true;
    }

    // 运动学碰撞预测
    const float time_step = current_vehicle.dt;
    const float future_delta_x = delta_x + relative_speed_x * VEHICLE_COLLISION_DETECTION_TIME_STEP * time_step;
    const float future_delta_y = delta_y + relative_speed_y * VEHICLE_COLLISION_DETECTION_TIME_STEP * time_step;
    const float future_distance_sq = future_delta_x*future_delta_x + future_delta_y*future_delta_y;

    // 检测是否发生了前后位置关系变化（超越行为）
    bool position_reversal_x = (delta_x * future_delta_x < 0);
    bool position_reversal_y = (delta_y * future_delta_y < 0);

    // 位置关系变化导致的碰撞检测
    if (position_reversal_x || position_reversal_y) {
#ifdef DEBUG_VEHICLE_COLLISION_DETECTION
        ZINFO("collision detection: current_vehicle future: %d, state: %d, device_id: %d, kf_update_res: (%f, %f, %f, %f), next_vehicle: %d, state: %d, device_id: %d, kf_update_res: (%f, %f, %f, %f) \n",
        current_vehicle.splicing_id, current_vehicle.splicing_track_state, current_vehicle.device_id,
        current_vehicle.kf_update_res[0], current_vehicle.kf_update_res[1], current_vehicle.kf_update_res[2], current_vehicle.kf_update_res[3],
        next_vehicle.splicing_id, next_vehicle.splicing_track_state, next_vehicle.device_id,
        next_vehicle.kf_update_res[0], next_vehicle.kf_update_res[1], next_vehicle.kf_update_res[2], next_vehicle.kf_update_res[3]);
#endif
        return true;
    }

    return false;
}

float CollisionDetection::calculate_displacement(const std::vector<TargetPoint> &target_output_path)
{
    // 处理边界情况
    if (target_output_path.size() <= 1) {
        return 0.0f;
    }

    // 获取最后两帧的位置
    const size_t path_size = target_output_path.size();
    const auto& current_point = target_output_path[path_size - 1];
    const auto& previous_point = target_output_path[path_size - 2];

    // 计算位移
    const float dx = current_point.kf_update_res[0] - previous_point.kf_update_res[0];
    const float dy = current_point.kf_update_res[1] - previous_point.kf_update_res[1];

    // 返回欧几里得距离
    return sqrt(dx*dx + dy*dy);
}

/*
 * @Name: vehicle_collision_detection_vec_correction
 * @Description: 车辆碰撞检测容器修正
 * @param vehicle_collision_detection_vec_map: 车辆修正容器按照lane_id分组
 */
void CollisionDetection::vehicle_collision_detection_vec_correction(std::map<int, std::vector<VehicleCollisionDetection>> &vehicle_collision_detection_vec_map)
{
    // 遍历车辆碰撞检测容器
    for (auto &lane_pair : vehicle_collision_detection_vec_map)
    {
        int lane_id = lane_pair.first;
        auto &vehicles = lane_pair.second;
        if (vehicles.size() <= 1)
        {
            continue;
        }
        // 修正车辆碰撞检测容器
        for (size_t vcdv = 0; vcdv < vehicles.size() - 1; vcdv++)
        {
            // 判断是否是碰撞状态 - 只处理已检测到碰撞的车辆
            if (vehicles[vcdv].collision_type == CollisionType_None)
            {
                continue;
            }

            // 根据追尾状态计算另外一辆车的index
            int another_vehicle_index = -1;
            if (vehicles[vcdv].collision_type == CollisionType_Front && vcdv + 1 < vehicles.size())
            {
                another_vehicle_index = vcdv + 1;
            }
            else if (vehicles[vcdv].collision_type == CollisionType_Back && vcdv > 0)
            {
                another_vehicle_index = vcdv - 1;
            }
            else
            {
                // 索引无效，跳过
                continue;
            }

            // 修正预测状态车辆的位置
            int correction_vehicle_index = -1, track_vehicle_index = -1;
            if ((vehicles[vcdv].splicing_track_state == SplicingTrackState::Lost)
                && (vehicles[another_vehicle_index].splicing_track_state == SplicingTrackState::Tracked))
            {
                correction_vehicle_index = vcdv;
                track_vehicle_index = another_vehicle_index;
            }
            else if ((vehicles[vcdv].splicing_track_state == SplicingTrackState::Tracked)
                     && (vehicles[another_vehicle_index].splicing_track_state == SplicingTrackState::Lost))
            {
                correction_vehicle_index = another_vehicle_index;
                track_vehicle_index = vcdv;
            }
            else if ((vehicles[vcdv].splicing_track_state == SplicingTrackState::Lost)
                     && (vehicles[another_vehicle_index].splicing_track_state == SplicingTrackState::Lost))
            {
                // TODO 如果都是丢失状态，应该怎么处理
                continue;
            }
            else if ((vehicles[vcdv].splicing_track_state == SplicingTrackState::Tracked)
                     && (vehicles[another_vehicle_index].splicing_track_state == SplicingTrackState::Tracked))
            {
                continue;
            }
            else
            {
                // 安全检查 - 确保不使用未初始化变量
                ZERROR("vehicle_collision_detection_vec_correction: vcdv: %d, another_vehicle_index: %d, splicing_track_state: %d, %d \n",
                vcdv, another_vehicle_index, vehicles[vcdv].splicing_track_state, vehicles[another_vehicle_index].splicing_track_state);
                continue;
            }

            // 安全检查 - 确保轨迹点足够
            int length = vehicles[track_vehicle_index].target_output_path.size();
            if (length < 2)
            {
                ZINFO("轨迹点不足，无法计算时间差：%d", length);
                continue;
            }

            // 最后两帧的时间差
            long long time_diff = vehicles[track_vehicle_index].target_output_path[length - 1].timestamp_ms - vehicles[track_vehicle_index].target_output_path[length - 2].timestamp_ms;

            // 确保correction_vehicle_index的轨迹至少有一个点
            if (vehicles[correction_vehicle_index].target_output_path.size() < 1)
            {
                ZINFO("待修正车辆轨迹为空");
                continue;
            }

            // 修改状态
            vehicles[track_vehicle_index].collision_type = CollisionType::CollisionType_None;
            vehicles[correction_vehicle_index].collision_type = CollisionType::CollisionType_None;

#ifdef DEBUG_VEHICLE_COLLISION_DETECTION
            ZINFO("vehicle_collision_detection_vec_correction: correction_vehicle_index: %d, track_vehicle_index: %d, time_diff: %lld, kf_update_res: (%f,%f,%f,%f) -> (%f,%f,%f,%f) \n",
            correction_vehicle_index, track_vehicle_index, time_diff, vehicles[correction_vehicle_index].kf_update_res[0], vehicles[correction_vehicle_index].kf_update_res[1], vehicles[correction_vehicle_index].kf_update_res[2], vehicles[correction_vehicle_index].kf_update_res[3],
            vehicles[track_vehicle_index].kf_update_res[0], vehicles[track_vehicle_index].kf_update_res[1], vehicles[track_vehicle_index].kf_update_res[2], vehicles[track_vehicle_index].kf_update_res[3]);
#endif
            // 修正correction_vehicle_index的位置
            vehicles[correction_vehicle_index].kf_update_res = vehicles[track_vehicle_index].kf_update_res;

            // 替换轨迹最后一个点 - 确保索引有效
            if (vehicles[correction_vehicle_index].target_output_path.size() > 0)
            {
                size_t last_index = vehicles[correction_vehicle_index].target_output_path.size() - 1;
                vehicles[correction_vehicle_index].target_output_path[last_index].kf_update_res = vehicles[track_vehicle_index].target_output_path[last_index].kf_update_res;
            }
        }
    }
}

void CollisionDetection::update_splicing_output_targets_vec(const std::map<int, std::vector<VehicleCollisionDetection>> &vehicle_collision_detection_vec_map,
                                                          std::vector<SplicingTarget> &splicing_output_targets_vec_)
{
    // 创建哈希表，建立fusion_id到目标索引的映射
    std::unordered_map<int, size_t> id_to_index_map;
    id_to_index_map.reserve(splicing_output_targets_vec_.size());

    // 第一次遍历，建立ID到索引的映射
    for (size_t sotv = 0; sotv < splicing_output_targets_vec_.size(); sotv++)
    {
        int fusion_id = splicing_output_targets_vec_[sotv].target_output.fusion_id;
        if (fusion_id != 0)  // 排除无效ID
        {
            id_to_index_map[fusion_id] = sotv;
        }
    }

    // 遍历车辆碰撞检测容器
    for (const auto &lane_pair : vehicle_collision_detection_vec_map)
    {
        // 直接遍历车辆，无需使用车道ID
        for (const auto &vehicle : lane_pair.second)
        {
            // 使用哈希表O(1)时间查找对应目标
            auto it = id_to_index_map.find(vehicle.splicing_id);
            if (it != id_to_index_map.end())
            {
                // 直接更新目标位置
                size_t target_index = it->second;
                if (splicing_output_targets_vec_[target_index].target_kalman->kf_update_res != vehicle.kf_update_res)
                {
#ifdef DEBUG_VEHICLE_COLLISION_DETECTION
                ZINFO("update_splicing_output_targets_vec: target_index: %d, splicing_id: %d, kf_update_res: (%f,%f,%f,%f) -> (%f,%f,%f,%f) \n",
                target_index, vehicle.splicing_id, splicing_output_targets_vec_[target_index].target_kalman->kf_update_res[0], splicing_output_targets_vec_[target_index].target_kalman->kf_update_res[1], splicing_output_targets_vec_[target_index].target_kalman->kf_update_res[2], splicing_output_targets_vec_[target_index].target_kalman->kf_update_res[3],
                vehicle.kf_update_res[0], vehicle.kf_update_res[1], vehicle.kf_update_res[2], vehicle.kf_update_res[3]);
#endif
                    splicing_output_targets_vec_[target_index].target_kalman->update_speed_from_other_filter(vehicle.kf_update_res[2],vehicle.kf_update_res[3], 1.0f,true,false);
                }
            }
        }
    }
}