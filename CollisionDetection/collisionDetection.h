#pragma once
#include "collisionDetectionheader.h"
#include "tracks_splicing_header.h"

#define VEHICLE_COLLISION_DETECTION_PATH_MAX_DURATION 2000       //车辆碰撞检测轨迹缓存的时长
#define VEHICLE_COLLISION_DETECTION_DISTANCE_THRESHOLD 1.0f    //车辆碰撞检测距离阈值
#define VEHICLE_COLLISION_DETECTION_TIME_STEP 5            //车辆碰撞检测时间步长
#define DEBUG_VEHICLE_COLLISION_DETECTION
namespace tecu_r1000_0_algorithm
{
    class CollisionDetection
    {
    public:
        CollisionDetection();
        ~CollisionDetection();
        /*
         * @Name: vehicle_collision_detection_and_correction
         * @Description: 车辆碰撞检测与纠正
         * @param splicing_output_targets_vec: 拼接目标容器
         */
        void vehicle_collision_detection_and_correction(std::vector<SplicingTarget> &splicing_output_targets_vec,
                                                        long long time_step);

    private:
        // 车辆修正容器
        std::vector<VehicleCollisionDetection> vehicle_collision_detection_vec;

        /*
         * @Name: is_vehicle_collision
         * @Description: 判断两个车辆是否发生碰撞
         * @param current_vehicle: 当前车辆
         * @param next_vehicle: 下一个车辆
         * @return: 是否发生碰撞
         */
        bool is_vehicle_collision(const VehicleCollisionDetection &current_vehicle,
                                  const VehicleCollisionDetection &next_vehicle);

        void vehicle_collision_detection_vec_clear();


        /*
         * @Name: vehicle_collision_detection_vec_update
         * @Description: 车辆修正容器更新
         * @param splicing_output_targets_vec: 拼接目标容器
         * @param vehicle_collision_detection_vec_(output): 车辆修正容器
         * @param path_max_duration: 路径最大时长
         * @param time_step: 时间戳
         */
        void vehicle_collision_detection_vec_update(const std::vector<SplicingTarget> &splicing_output_targets_vec,
                                                    std::vector<VehicleCollisionDetection> &vehicle_collision_detection_vec_,
                                                    long long path_max_duration,
                                                    long long time_step);


        /*
         * @Name: update_splicing_output_targets_vec
         * @Description: 更新拼接目标容器
         * @param vehicle_collision_detection_vec_: 车辆修正容器
         * @param splicing_output_targets_vec_(output): 拼接目标容器
         */
        void update_splicing_output_targets_vec(const std::vector<VehicleCollisionDetection> &vehicle_collision_detection_vec_,
                                                std::vector<SplicingTarget> &splicing_output_targets_vec_);

        /*
         * @Name: vehicle_collision_detection_vec_collision_detection
         * @Description: 车辆碰撞检测
         * @param vehicle_collision_detection_vec_map: 车辆修正容器按照lane_id分组
         */
        void vehicle_collision_detection_vec_collision_detection(std::map<int, std::vector<VehicleCollisionDetection>> &vehicle_collision_detection_vec_map);

        /*
         * @Name: vehicle_collision_detection_vec_group_by_lane_id
         * @Description: 车辆碰撞检测容器按照lane_id分组 并且按照位置前后排序
         * @param vehicle_collision_detection_vec_: 车辆修正容器
         * @param vehicle_collision_detection_vec_map_(output): 车辆修正容器按照lane_id分组
         */
        void vehicle_collision_detection_vec_group_by_lane_id(const std::vector<VehicleCollisionDetection> &vehicle_collision_detection_vec_,
                                                             std::map<int, std::vector<VehicleCollisionDetection>> &vehicle_collision_detection_vec_map);

        /*
         * @Name: cal_speed
         * @Description: 计算车辆速度
         * @param target_output_path: 车辆轨迹
         * @return: 速度
         */
        SpeedVector cal_speed(const std::vector<TargetPoint> &target_output_path);

        /*
         * @Name: vehicle_collision_detection_vec_correction
         * @Description: 车辆碰撞检测容器修正
         * @param vehicle_collision_detection_vec_map: 车辆修正容器按照lane_id分组
         */
        void vehicle_collision_detection_vec_correction(std::map<int, std::vector<VehicleCollisionDetection>> &vehicle_collision_detection_vec_map);

        /*
         * @Name: update_splicing_output_targets_vec
         * @Description: 更新拼接目标容器
         * @param vehicle_collision_detection_vec_map: 车辆修正容器按照lane_id分组
         * @param splicing_output_targets_vec_(output): 拼接目标容器
         */
        void update_splicing_output_targets_vec(const std::map<int, std::vector<VehicleCollisionDetection>> &vehicle_collision_detection_vec_map,
                                                std::vector<SplicingTarget> &splicing_output_targets_vec_);

        /*
         * @Name: calculate_displacement
         * @Description: 计算车辆位移
         * @param target_output_path: 车辆轨迹
         * @return: 位移
         */
        float calculate_displacement(const std::vector<TargetPoint> &target_output_path);
    };
}