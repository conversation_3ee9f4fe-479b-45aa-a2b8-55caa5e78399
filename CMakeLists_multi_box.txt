# 多盒子接力测试专用CMake配置
cmake_minimum_required(VERSION 3.5.1)

# 项目信息
project(TECU1000_MULTI_BOX_RELAY_TEST)
add_compile_options(-Wno-narrowing)

# 设置 C++ 标准为 11
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED True)
set(CMAKE_BUILD_TYPE Debug)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/../")
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib")

# 包含目录
include_directories(
    ${PROJECT_SOURCE_DIR}/
    ${PROJECT_SOURCE_DIR}/Eigen/
    ${PROJECT_SOURCE_DIR}/Calib/
    ${PROJECT_SOURCE_DIR}/Config/
    ${PROJECT_SOURCE_DIR}/DataSave/
    ${PROJECT_SOURCE_DIR}/KalmanFilter/
    ${PROJECT_SOURCE_DIR}/Src/
    ${PROJECT_SOURCE_DIR}/TracksSplicing/
    ${PROJECT_SOURCE_DIR}/LaneSmoothing/
    ${PROJECT_SOURCE_DIR}/CollisionDetection/
    ${PROJECT_SOURCE_DIR}/utils_debug/
    ${PROJECT_SOURCE_DIR}/jsoncpp/
)

# 收集源文件（排除需要OpenCV的文件）
file(GLOB Source_Files
    ${PROJECT_SOURCE_DIR}/Src/algorithm_lib.cpp
    ${PROJECT_SOURCE_DIR}/Src/algorithm_header_lib.cpp
    ${PROJECT_SOURCE_DIR}/Src/pipeline_track_fusion.cpp
    ${PROJECT_SOURCE_DIR}/Src/target_outputs_callback.cpp
    ${PROJECT_SOURCE_DIR}/Src/utils.cpp
    ${PROJECT_SOURCE_DIR}/Src/RoadDirectionCacl.cpp
)

file(GLOB Config_SRC ${PROJECT_SOURCE_DIR}/Config/*.cpp)
file(GLOB DataSave_SRC ${PROJECT_SOURCE_DIR}/DataSave/*.cpp)
file(GLOB KalmanFilter_SRC ${PROJECT_SOURCE_DIR}/KalmanFilter/*.cpp)
file(GLOB TracksSplicing_SRC ${PROJECT_SOURCE_DIR}/TracksSplicing/*.cpp)
file(GLOB LaneSmoothing_SRC ${PROJECT_SOURCE_DIR}/LaneSmoothing/*.cpp)
file(GLOB CollisionDetection ${PROJECT_SOURCE_DIR}/CollisionDetection/*.cpp)
file(GLOB utils_debug ${PROJECT_SOURCE_DIR}/utils_debug/*.cpp)
file(GLOB jsoncpp ${PROJECT_SOURCE_DIR}/jsoncpp/*.cpp)

# 排除需要OpenCV的标定文件
set(Calib_SRC
    ${PROJECT_SOURCE_DIR}/Calib/calib_radar_radar.cpp
    ${PROJECT_SOURCE_DIR}/Calib/calib_hologram_mapping.cpp
)

# 添加多盒子接力测试可执行文件
add_executable(tecu1000_multi_box_relay_test
    ${PROJECT_SOURCE_DIR}/tecu1000_multi_box_relay_test.cpp
    ${Source_Files}
    ${Calib_SRC}
    ${Config_SRC}
    ${DataSave_SRC}
    ${KalmanFilter_SRC}
    ${TracksSplicing_SRC}
    ${LaneSmoothing_SRC}
    ${CollisionDetection}
    ${utils_debug}
    ${jsoncpp}
)

# 链接库 - 不包含OpenCV
target_link_libraries(tecu1000_multi_box_relay_test
    stdc++
    pthread
)

# 添加编译定义，用于本地调试
add_definitions(-DLOCAL_DEBUG)

# 打印信息
message(STATUS "配置多盒子接力测试环境（不依赖OpenCV）")
