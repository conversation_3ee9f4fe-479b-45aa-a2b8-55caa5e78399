#include "data_save.h"
#include "pipeline_track_fusion.h"
#include "zlogger.h"

ZLOGGER_HEADER_DEFINE("ALG_DATA_SAVE")

using namespace tecu_r1000_0_algorithm;

DataSave::DataSave(int channel_id, std::string file_dir_path, uint64_t input_data_max_write_size, bool save_simulation, bool save_match_error)
{
	save_channel_id = channel_id;
	save_file_dir_path = file_dir_path;
	input_data_file_write_count = 0;
	svae_input_data_max_size = input_data_max_write_size;
	config_save_simulation = save_simulation;
	config_save_match_error = save_match_error;
	file_init();
    ZINFO("DataSave channel_id:%d  file_dir_path:%s input_data_file_write_count:%llu svae_input_data_max_size:%lld config_save_simulation:%d config_save_match_error:%d \n",
											channel_id, file_dir_path.c_str(), input_data_file_write_count, svae_input_data_max_size, config_save_simulation, config_save_match_error);
}


DataSave::~DataSave()
{
	input_data_file_write_count = 0;
	file_deinit();
    ZINFO("DataSave release file_deinit!");
}

void DataSave::file_init()
{
	std::string channel_name = "channel_id_" + std::to_string(save_channel_id) + "/";
	std::string channel_save_file_dir_path = save_file_dir_path + channel_name;
	std::string mkdir_str;
	mkdir_str = "mkdir -p ";
	mkdir_str += channel_save_file_dir_path;
	system(mkdir_str.c_str());

    ZINFO("file_init mkdir_str:%s create success!\n", mkdir_str.c_str());
	std::string current_time = trackFusionManger::get_current_time_asstring();
	std::string input_data_file_path = channel_save_file_dir_path + current_time + ".dat";
#ifdef DEBUG_DATA_SAVE
	std::string input_data_file_path_box1 = channel_save_file_dir_path + current_time + "_box1" + ".dat";
	std::string input_data_file_path_box2 = channel_save_file_dir_path + current_time + "_box2" + ".dat";
#endif
	std::string input_data_timems_file_path = channel_save_file_dir_path + current_time + ".txt";
	std::string match_error_file_path = channel_save_file_dir_path + current_time + "_match_id1_id2_ex_ey" + ".txt";
    std::string road_direction_file_path = channel_save_file_dir_path + current_time + "_road_direction" + ".dat";

    ZINFO("file_init input_data_file_path:%s  input_data_timems_file_path:%s match_error_file_path:%s road_direction_file_path:%s\n",
                     input_data_file_path.c_str(), input_data_timems_file_path.c_str(), match_error_file_path.c_str(),
                     road_direction_file_path.c_str());

	input_data_file = fopen(input_data_file_path.c_str(), "wb");
#ifdef DEBUG_DATA_SAVE
	input_data_file_box1 = fopen(input_data_file_path_box1.c_str(), "wb");
	input_data_file_box2 = fopen(input_data_file_path_box2.c_str(), "wb");
#endif
	if (nullptr == input_data_file)
	{
		ZERROR("file_init input_data_file open failure! \n");
	}
	input_data_timems_file = fopen(input_data_timems_file_path.c_str(), "w");
	if (nullptr == input_data_timems_file)
	{
        ZERROR("file_init input_data_timems_file open failure! \n");
	}
    base_device_id_file = nullptr;
	splicing_match_error_file = fopen(match_error_file_path.c_str(), "a+");
	if (nullptr == splicing_match_error_file)
	{
        ZERROR("file_init splicing_match_error_file open failure! \n");
	}

}

void DataSave::file_deinit()
{
	if (nullptr != input_data_file)
	{
		fclose(input_data_file);
		input_data_file = nullptr;
	}

#ifdef DEBUG_DATA_SAVE		
	if (nullptr != input_data_file_box1)
	{
		fclose(input_data_file_box1);
		input_data_file_box1 = nullptr;
	}
	if (nullptr != input_data_file_box2)
	{
		fclose(input_data_file_box2);
		input_data_file_box2 = nullptr;
	}
#endif
	if (nullptr != input_data_timems_file)
	{
		fclose(input_data_timems_file);
		input_data_timems_file = nullptr;
	}
	if (nullptr != splicing_match_error_file)
	{
		fclose(splicing_match_error_file);
		splicing_match_error_file = nullptr;
	}
	if (nullptr != base_device_id_file)
	{
		fclose(base_device_id_file);
		base_device_id_file = nullptr;
	}
	if (nullptr != device_lists_file)
	{
		fclose(device_lists_file);
		device_lists_file = nullptr;
	}
	if (nullptr != device_map_file)
	{
		fclose(device_map_file);
		device_map_file = nullptr;
	}

    if(nullptr != road_direction_file)
    {
        fclose(road_direction_file);
        road_direction_file = nullptr;
    }

    ZINFO("file_deinit close,input_data_file input_data_timems_file splicing_match_error_file base_device_id_file device_lists_file device_map_file!\n");
}

void DataSave::base_device_id_record_fifo(unsigned int base_device_id)
{
	std::string channel_name = "channel_id_" + std::to_string(save_channel_id) + R"(/)";
	std::string channel_save_file_dir_path = save_file_dir_path + channel_name;
	std::string base_device_id_file_path = channel_save_file_dir_path + "base_device_id.txt";
	base_device_id_file = fopen(base_device_id_file_path.c_str(), "w");
	if (nullptr == base_device_id_file)
	{
        ZERROR("file_init base_device_id_file_path open failure! \n");
	}
	if (nullptr != base_device_id_file)
	{
		fprintf(base_device_id_file, "%d\n", base_device_id);
        ZINFO("base_device_id_record_fifo channel_id:%d save base_device_id:%d to base_device_file!\n",
               save_channel_id, base_device_id);
		fclose(base_device_id_file);
		base_device_id_file = nullptr;
	}
}

void DataSave::match_error_record_fifo(unsigned int id1, unsigned int id2, float x_error, float y_error)
{
	if (config_save_match_error == true)
	{
		if (nullptr != splicing_match_error_file)
		{
			fprintf(splicing_match_error_file, "%d %d %.2f %.2f\n",
				id1, id2, x_error, y_error);
            fflush(splicing_match_error_file);
            // fprintf() 是带缓冲的 I/O 操作，但在大多数情况下，当你关闭文件（使用 fclose()）时，缓冲区的内容会被自动刷新到文件中。
            // 然而，如果你需要在关闭文件之前确保数据已经被写入，可以使用 fflush(fp) 来手动刷新输出缓冲区。
		}
	}
}


void DataSave::device_list_record_fifo(DeviceList device_list)
{
	std::string channel_name = "channel_id_" + std::to_string(save_channel_id) + R"(/)";
	std::string channel_save_file_dir_path = save_file_dir_path + channel_name;
	std::string device_lists_file_path = channel_save_file_dir_path + "devicelist.dat";
	device_lists_file = fopen(device_lists_file_path.c_str(), "w");
	if (nullptr == device_lists_file)
	{
		ZERROR("file_init device_lists_file_path open failure! \n");
	}
	if (nullptr != device_lists_file)
	{
		fwrite(&device_list, 1, sizeof(device_list), device_lists_file);
        ZDEBUG("base_device_id_record_fifo channel_id:%d save device_list.cnt:%d to device_lists_file!\n",
			    save_channel_id, device_list.device_cnt);
		fclose(device_lists_file);
		device_lists_file = nullptr;
	}
}

void DataSave::device_map_record_fifo(CalibMatchList calib_match_list)
{
	std::string channel_name = "channel_id_" + std::to_string(save_channel_id) + R"(/)";
	std::string channel_save_file_dir_path = save_file_dir_path + channel_name;
	std::string device_map_file_path = channel_save_file_dir_path + "devicemap.dat";
	device_map_file = fopen(device_map_file_path.c_str(), "w");
	if (nullptr == device_map_file)
	{
        ZERROR("file_init device_map_file open failure! \n");
	}
	if (nullptr != device_map_file)
	{
		fwrite(&calib_match_list, 1, sizeof(calib_match_list), device_map_file);
        ZDEBUG("device_map_record_fifo channel_id:%d save calib_match_list.cnt:%d to device_map_file!\n",
                                       save_channel_id, calib_match_list.device_match_cnt);
		fclose(device_map_file);
		device_map_file = nullptr;
	}
}


void DataSave::input_data_record_fifo(MultiDeviceTargets multi_device_targets, long long timestamp_ms)
{
    if (config_save_simulation == true)
    {
        if (nullptr != input_data_file && nullptr != input_data_timems_file)
        {
            if (input_data_file_write_count < svae_input_data_max_size)
            {
                // 计算实际写入的字节数
                size_t write_size = sizeof(unsigned int); // device_cnt 的大小
                
                // 对每个设备计算实际大小
                for (unsigned int i = 0; i < multi_device_targets.device_cnt; i++) {
                    write_size += sizeof(unsigned int) * 2;  // device_id 和 target_cnt
                    write_size += sizeof(Target) * multi_device_targets.device_input[i].target_cnt;  // 实际目标数据
                }
                
                if (input_data_file_write_count + write_size <= svae_input_data_max_size)
                {
                    fwrite(&multi_device_targets, 1, sizeof(multi_device_targets), input_data_file);
                    fprintf(input_data_timems_file, "%lld\n", timestamp_ms);
                    input_data_file_write_count += write_size;
                }
            }
        }
    }
}

void DataSave::input_data_record_fifo(tecu1000_alg::MultiBoxTargets multi_box_targets, long long timestamp_ms)
{
	if (config_save_simulation == true)
	{
		if (nullptr != input_data_file && nullptr != input_data_timems_file)
		{
			if (input_data_file_write_count < svae_input_data_max_size)
			{
				input_data_file_write_count += sizeof(multi_box_targets);
				fwrite(&multi_box_targets, 1, sizeof(multi_box_targets), input_data_file);
				fprintf(input_data_timems_file, "%lld\n", timestamp_ms);
			}
		}
	}
}

// 建议的数据存储格式
void DataSave::input_data_record_fifo_simple(const MultiDeviceTargets& multi_device_targets, long long timestamp_ms) {
    if (!config_save_simulation || !input_data_file || !input_data_timems_file) {
        return;
    }
    
    if (input_data_file_write_count >= svae_input_data_max_size) {
        return;
    }

    // 计算实际需要写入的总字节数
    size_t total_write_size = sizeof(unsigned int);  // device_cnt的大小
    
    // 对每个设备计算实际大小
    for (unsigned int i = 0; i < multi_device_targets.device_cnt; i++) {
        const auto& device = multi_device_targets.device_input[i];
        total_write_size += sizeof(unsigned int);  // device_id
        total_write_size += sizeof(DeviceType);    // device_type
        total_write_size += sizeof(unsigned int);  // target_cnt
        total_write_size += sizeof(long long);     // timestamp_ms
        total_write_size += sizeof(Target) * device.target_cnt;  // 实际目标数据
    }
	
    // 只写入实际使用的设备数量
    fwrite(&multi_device_targets.device_cnt, sizeof(unsigned int), 1, input_data_file);
    
    // 对每个设备只写入实际的目标数量
    for (unsigned int i = 0; i < multi_device_targets.device_cnt; i++) {
        const auto& device = multi_device_targets.device_input[i];
        fwrite(&device.device_id, sizeof(unsigned int), 1, input_data_file);
        fwrite(&device.device_type, sizeof(DeviceType), 1, input_data_file);
        fwrite(&device.target_cnt, sizeof(unsigned int), 1, input_data_file);
        fwrite(&device.timestamp_ms, sizeof(long long), 1, input_data_file);
        // 只写入实际的目标数量
        fwrite(device.targets.target_devices, 
               sizeof(Target), 
               device.target_cnt, 
               input_data_file);
    }
    
    fprintf(input_data_timems_file, "%lld\n", timestamp_ms);
    
    // 更新写入计数（使用实际写入的字节数）
    input_data_file_write_count += total_write_size;
}


void DataSave::input_boxdata_record_fifo_simple(const tecu1000_alg::MultiBoxTargets &multi_box_targets, int box_id) {
    FILE *input_data_file_tmp = nullptr;
    input_data_file_tmp = input_data_file;
#ifdef DEBUG_DATA_SAVE
    if (box_id == 1) {
        input_data_file_tmp = input_data_file_box1;
    } else if (box_id == 2) {
        input_data_file_tmp = input_data_file_box2;
    }
#endif
    if (!config_save_simulation || !input_data_file_tmp || !input_data_timems_file) {
        return;
    }
        
    if (input_data_file_write_count >= svae_input_data_max_size) {
        return;
    }
    
    const MultiDeviceTargets& multi_device_targets = multi_box_targets.multi_device_targets;
    // 计算实际需要写入的总字节数
    size_t total_write_size = sizeof(unsigned int);  // device_cnt的大小
    
    // 1. 写入 multi_device_targets
    // 对每个设备计算实际大小
    for (unsigned int i = 0; i < multi_device_targets.device_cnt; i++) {
        const auto& device = multi_device_targets.device_input[i];
        total_write_size += sizeof(unsigned int);  // device_id
        total_write_size += sizeof(DeviceType);    // device_type
        total_write_size += sizeof(unsigned int);  // target_cnt
        total_write_size += sizeof(long long);     // timestamp_ms
        total_write_size += sizeof(Target) * device.target_cnt;  // 实际目标数据
    }
    
    // 只写入实际使用的设备数量
    size_t written = fwrite(&multi_device_targets.device_cnt, sizeof(unsigned int), 1, input_data_file_tmp);
    if (written != 1) {
        ZERROR("写入device_cnt失败");
        return;
    }
    
    // 对每个设备只写入实际的目标数量
    for (unsigned int i = 0; i < multi_device_targets.device_cnt; i++) {
        const auto& device = multi_device_targets.device_input[i];
        fwrite(&device.device_id, sizeof(unsigned int), 1, input_data_file_tmp);
        fwrite(&device.device_type, sizeof(DeviceType), 1, input_data_file_tmp);
        fwrite(&device.target_cnt, sizeof(unsigned int), 1, input_data_file_tmp);
        fwrite(&device.timestamp_ms, sizeof(long long), 1, input_data_file_tmp);
        // 只写入实际的目标数量
        if (device.target_cnt > 0) {
            fwrite(device.targets.target_devices, 
                  sizeof(Target), 
                  device.target_cnt, 
                  input_data_file_tmp);
        }
    }
    
    // 2. 写入last_output_target
    // 写入target_cnt
    fwrite(&multi_box_targets.last_output_target.target_cnt, sizeof(unsigned int), 1, input_data_file_tmp);
    
    // 写入target_info
    for (unsigned int i = 0; i < multi_box_targets.last_output_target.target_cnt; i++) {
        const auto& target = multi_box_targets.last_output_target.fusion_targets[i];
        fwrite(&target.splice_id, sizeof(unsigned int), 1, input_data_file_tmp);
        fwrite(&target.device_id, sizeof(unsigned int), 1, input_data_file_tmp);
        fwrite(&target.splice_target.timestamp_ms, sizeof(long long), 1, input_data_file_tmp);
        fwrite(&target.splicing_x, sizeof(float), 1, input_data_file_tmp);
        fwrite(&target.splicing_y, sizeof(float), 1, input_data_file_tmp);
        fwrite(&target.splice_target, sizeof(Target), 1, input_data_file_tmp);
    }
    
    // 计算并添加last_output_target部分的写入大小
    total_write_size += sizeof(unsigned int);  // target_cnt的大小
    total_write_size += multi_box_targets.last_output_target.target_cnt * (
        2 * sizeof(unsigned int) +  // splice_id 和 device_id
        sizeof(long long) +         // timestamp_ms
        2 * sizeof(float) +         // splicing_x 和 splicing_y
        sizeof(Target)              // splice_target
    );
    
    // 更新写入计数（使用实际写入的字节数）
    input_data_file_write_count += total_write_size;
}

void DataSave::input_timems_record_fifo_simple(long long timestamp_ms)
{
	fprintf(input_data_timems_file, "%lld\n", timestamp_ms);
}

/*
 * @Name:
 * @Description:
 *
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/11/19
 * Time: 11:10
 * Author: WangXing
 * Content: Create
*/
void DataSave::road_direction_record_fifo(RoadDirection road_direction_info, std::string file_path)
{
    road_direction_file = fopen(file_path.c_str(), "w");
    if (nullptr == road_direction_file)
    {
        ZERROR("road_direction_file open failure! \n");
    }
    if (nullptr != road_direction_file)
    {
        fwrite(&road_direction_info, 1, sizeof(RoadDirection), road_direction_file);
        ZINFO("road_direction_info save file success!\n");
        fclose(road_direction_file);
        road_direction_file = nullptr;
    }
}