#pragma once
#include <string.h>
#include <iostream>  
#include <iomanip>  
#include <sstream>  
#include <chrono>  
#include <ctime> 
#include <cstdio>
#include "tecu1000_algorithm_header.h"
#include "calib_header.h"
#include "tracks_splicing.h"
#include "tracks_splicing_header.h"

using namespace tecu1000_alg;
namespace tecu_r1000_0_algorithm
{
	class DataSave
	{
	public:
		DataSave(int channel_id, std::string file_dir_path, uint64_t input_data_max_write_size, bool save_simulation, bool save_match_error);
		~DataSave();

		// 输入数据记录
		void input_data_record_fifo(MultiDeviceTargets multi_device_targets, long long timestamp_ms);
		void input_data_record_fifo_simple(const MultiDeviceTargets& multi_device_targets, long long timestamp_ms);
		void input_data_record_fifo(tecu1000_alg::MultiBoxTargets multi_box_targets, long long timestamp_ms);
		void input_boxdata_record_fifo_simple(const tecu1000_alg::MultiBoxTargets &multi_box_targets, int box_id);
		void input_timems_record_fifo_simple(long long timestamp_ms);
		
		//�ص�����Ŀ������
		void match_error_record_fifo(unsigned int id1, unsigned int id2, float x_error,float y_error);
		
		//��׼�豸�ı���
		void base_device_id_record_fifo(unsigned int base_device_id);
		//�豸��Ϣ�б��ı���
		void device_list_record_fifo(DeviceList device_list);
		//�豸ȫϢӳ��ʱ�ı���
		void device_map_record_fifo(CalibMatchList calib_match_list);

        //���ֵ�·ӳ��Ľ��
        void road_direction_record_fifo(RoadDirection road_direction_info, std::string file_path);

	private:

		int save_channel_id;
		std::string save_file_dir_path;

		void file_init();
		void file_deinit();
		//����������ݵı��
		bool config_save_simulation;
		//����ƥ�����ı��
		bool config_save_match_error;
		//�������ݵ��ֽڼ���
		uint64_t input_data_file_write_count;
		//�������ݵ�����ֽ�
		uint64_t svae_input_data_max_size;
		//ͨ�����������ݱ����ļ�
		FILE *input_data_file;
		FILE *input_data_file_box1;
		FILE *input_data_file_box2;
		//ͨ�����������ݵ�ʱ��������ļ�
		FILE *input_data_timems_file;

		//ͨ���ڻ�׼�豸�ı����ļ�
		FILE *base_device_id_file;

		//ͨ�����豸�б���Ϣ������ļ�
		FILE *device_lists_file;

		//ͨ����ӳ��ʱ����Ϣ������ļ�
		FILE *device_map_file;


		//ͨ�����ص�����Ŀ���ص��ںϳɹ�ʱ�����
		FILE *splicing_match_error_file;
		
	    FILE *road_direction_file;

	};
}


