#!/bin/bash

# 获取当前分支名（处理 detached HEAD 状态）
current_branch=$(git symbolic-ref --short HEAD 2>/dev/null || git describe --tags --exact-match HEAD 2>/dev/null || echo "detached")

# 获取当前 HEAD 的 commit hash
original_head=$(git rev-parse HEAD)

# 提示用户输入分支名（超时 10 秒）
read -p "请输入要创建的分支名 (留空则使用当前时间命名): " branch_name

# 判断用户是否输入了内容
if [ -z "$branch_name" ]; then
  branch_name=backup_$(date +"%Y%m%d_%H.%M")
  echo "未输入分支名，使用默认时间命名: $branch_name"
else
  echo "使用用户输入的分支名: $branch_name"
fi

# 检查是否已存在该分支
if git show-ref --verify --quiet refs/heads/$branch_name; then
  echo "分支 '$branch_name' 已存在，正在删除..."
  git branch -D $branch_name
fi

# 暂存所有更改（包括新增、修改、删除）
echo "正在暂存所有更改..."
git add --all

# 判断是否有更改需要提交
if git diff --cached --quiet; then
  echo "⚠️ 没有更改需要提交，跳过提交步骤。"
else
  # 使用 Git 底层命令创建提交，不更新当前分支 HEAD
  echo "正在创建提交对象..."

  # 获取当前索引的树对象
  tree=$(git write-tree)

  # 创建提交对象，父提交为当前 HEAD
  commit_hash=$(git commit-tree $tree -p $original_head -m "Auto-commit: Backup before creating branch $branch_name")

  echo "✅ 已创建提交对象，提交哈希: $commit_hash"

  # 创建新分支指向该提交
  git branch $branch_name $commit_hash

  echo "✅ 新分支 '$branch_name' 已创建，指向提交 $commit_hash"
fi

# 仅重置暂存区，保留工作目录状态
echo "正在恢复原分支状态..."
git reset

# 输出提示信息
echo "✅ 当前分支状态（含未提交更改）已备份至新分支 '$branch_name'"

# 询问是否推送备份分支到远程仓库
read -p "是否将备份分支 '$branch_name' 推送到远程仓库？(y/n): " push_choice

if [ "$push_choice" = "y" ] || [ "$push_choice" = "Y" ]; then
  echo "正在推送分支 '$branch_name' 到远程仓库..."
  git push origin $branch_name
  echo "✅ 分支 '$branch_name' 已成功推送到远程仓库"
else
  echo "已取消推送操作，分支 '$branch_name' 仅保存在本地"
fi

