#include "kalman_smooth.h"
#include "zlogger.h"

ZLOGGER_HEADER_DEFINE("ALG_KALMAN_SMOOTH")

using namespace tecu_r1000_0_algorithm;

KalmanSmooth::KalmanSmooth(int state_size, int meas_size, int control_size, float t, float rs_rate, float qs_rate)
{
    A = Eigen::MatrixXd(state_size, state_size);
    A << 1, 0, t, 0,
        0, 1, 0, t,
        0, 0, 1, 0,
        0, 0, 0, 1;
    H = Eigen::MatrixXd(meas_size, state_size);
    H << 1, 0, 0, 0,
        0, 1, 0, 0;
    P = Eigen::MatrixXd(state_size, state_size);
    P.setIdentity();
    R = Eigen::MatrixXd(meas_size, meas_size);
    R.setIdentity();
    Q = Eigen::MatrixXd(state_size, state_size);
    Q.setIdentity();

    R_smooth = Eigen::MatrixXd::Zero(meas_size, meas_size);
    for (unsigned int i = 0; i < R_smooth.rows(); ++i)
    {
        R_smooth(i, i) = rs_rate;
    }
    Q_smooth = Eigen::MatrixXd::Zero(state_size, state_size);
    for (unsigned int i = 0; i < Q_smooth.rows(); ++i)
    {
        Q_smooth(i, i) = qs_rate;
    }

    kf_x = Eigen::VectorXd(state_size);
    kf_z = Eigen::VectorXd(meas_size);
    kf_z.setZero();
    kf_predict_res = Eigen::VectorXd(state_size);
    kf_predict_res.setZero();
    kf_update_res = Eigen::VectorXd(state_size);
    kf_update_res.setZero();
    kf_smooth = new KalmanFilter(state_size, meas_size, control_size);
    is_predict_state = false;
    is_retreat_state = false;
    is_speed_state = false;
#ifdef DBEUG
    ZDEBUG("create KalmanSmooth state_size:%d meas_size:%d control_size:%d rs_rate:%.2f qs_rate:%.2f\n",
           state_size, meas_size, control_size, rs_rate, qs_rate);
#endif
}

KalmanSmooth::~KalmanSmooth()
{
    if (nullptr != kf_smooth)
    {
        delete kf_smooth;
        kf_smooth = nullptr;
#ifdef DBEUG
        ZDEBUG("delete KalmanSmooth! \n");
#endif
    }
}

/*
 * @Name: kf_smooth_update
 * @Description: 基于卡尔曼滤波器对目标的位置进行更新
 *
 * @Input
 *
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/8/15
 * Time: 9:07
 * Author: WangXing
 * Content: Create
 */
void KalmanSmooth::kf_smooth_predict(int fusion_id,
                                     unsigned int device_id,
                                     unsigned int target_id,
                                     CalibPoint coor_in_radar,
                                     CalibPoint coor_in_mercator,
                                     float meas_speed,
                                     bool is_lose,
                                     double target_direction)
{
    // 连续多帧目标不移动, 则目标疑似丢失
    if ((fabsf(coor_in_radar.y - coor_in_radar_previous.y) < 1e-4 && fabsf(coor_in_radar.x - coor_in_radar_previous.x) < 1e-4) && fabsf(meas_speed) > 1e-4)
    {
        is_suspicious_lose_count++;
        if (is_suspicious_lose_count > is_suspicious_lose_threshold)
        {
            is_suspicious_lose_count = is_suspicious_lose_threshold;
        }
    }
    else
    {
        is_suspicious_lose_count = 0;
    }
    coor_in_radar_previous = coor_in_radar;
    is_suspicious_lose = is_suspicious_lose_count == is_suspicious_lose_threshold;

    //(1) 当疑似丢失时, 使用预测值代替测量值
    //(2) 当目标丢失时, 使用预测值代替测量值
    if (is_suspicious_lose == true)
    {
        kf_predict_res << kf_smooth->predict(A);
        float predict_distance = fabs(meas_speed);
        float xaxis_movement = cos(target_direction / 180.0 * M_PI) * predict_distance;
        float yaxis_movement = sin(target_direction / 180.0 * M_PI) * predict_distance;
        float predict_xaxes_current = kf_update_res[0] + xaxis_movement;
        float predict_yaxes_current = kf_update_res[1] + yaxis_movement;
        kf_predict_res[0] = predict_xaxes_current;
        kf_predict_res[1] = predict_yaxes_current;
        //        kf_z << predict_xaxes_current, predict_yaxes_current;
        //        kf_update_res << kf_smooth->update_output(H, kf_z);
        //        is_predict_state = true;
        //        ZINFO("kf_smooth_predict f_id:%d dv_id:%d t_id:%d is_suspicious_lose target_direction:%.2f xaxis_movement:%.2f yaxis_movement:%.2f kf_predict_before_xaxis:%.2f kf_predict_before_yaxis:%.2f kf_predict_res[0]:%.2f kf_predict_res[1]:%.2f predict_distance:%.2f\n",
        //              fusion_id, device_id, target_id, target_direction, xaxis_movement, yaxis_movement,kf_predict_before_xaxis, kf_predict_before_yaxis, kf_predict_res[0], kf_predict_res[1], predict_distance);
    }
    else if (is_lose == true)
    {
        kf_predict_res << kf_smooth->predict(A);
        float predict_distance = fabs(meas_speed);
        float xaxis_movement = cos(target_direction / 180.0 * M_PI) * predict_distance;
        float yaxis_movement = sin(target_direction / 180.0 * M_PI) * predict_distance;
        float predict_xaxes_current = kf_update_res[0] + xaxis_movement;
        float predict_yaxes_current = kf_update_res[1] + yaxis_movement;
        kf_predict_res[0] = predict_xaxes_current;
        kf_predict_res[1] = predict_yaxes_current;
        //        ZINFO("kf_smooth_predict f_id:%d dv_id:%d t_id:%d is_lose target_direction:%.2f xaxis_movement:%.2f yaxis_movement:%.2f kf_predict_before_xaxis:%.2f kf_predict_before_yaxis:%.2f kf_predict_res[0]:%.2f kf_predict_res[1]:%.2f predict_distance:%.2f\n",
        //              fusion_id, device_id, target_id, target_direction, xaxis_movement, yaxis_movement,kf_predict_before_xaxis, kf_predict_before_yaxis, kf_predict_res[0], kf_predict_res[1], predict_distance);
    }
    else
    {
        // kf_predict_res << kf_smooth->predict(A);
    }
}

/*
 * @Name:kf_smooth_update
 * @Description:目标信息的更新
 *
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/11/4
 * Time: 14:27
 * Author: WangXing
 * Content: Create
 */
void KalmanSmooth::kf_smooth_update(int fusion_id,
                                    unsigned int device_id,
                                    unsigned int target_id,
                                    CalibPoint meas_coor_mercator,
                                    float meas_speed,
                                    float smooth_min_speed,
                                    float smooth_max_speed,
                                    float smooth_speed_interval,
                                    bool is_lose,
                                    double target_direction,
                                    bool use_input_noise_filter)
{
    // 当目标消失时或疑似消失时, 使用预测值代替测量值进行更新
    float predict_move = fabs(meas_speed);
    if (is_lose == true || is_suspicious_lose == true)
    {
        meas_coor_mercator.x = kf_predict_res[0];
        meas_coor_mercator.y = kf_predict_res[1];
        is_predict_state = true;
    }
    else
    {
        is_predict_state = false;
        CalibPoint predict_coor_mercator_previous = {
            kf_predict_res[0],
            kf_predict_res[1]};
        kf_predict_res << kf_smooth->predict(A);
    }
    CalibPoint meas_coor_smooth = {
        meas_coor_mercator.x,
        meas_coor_mercator.y,
    };
    CalibPoint update_coor_previous = {
        kf_update_res[0],
        kf_update_res[1]};
    float threshold = 0.0f;
    // 计算移动路程
    float distance = sqrt(pow(meas_coor_smooth.x - update_coor_previous.x, 2) + pow(meas_coor_smooth.y - update_coor_previous.y, 2));
    if (is_predict_state == false && use_input_noise_filter)
    {
        // 如果当前的位移与平均位移差异过大,则对测量值进行修正
        if (_distance_buffer.size() >= 10 &&  // 至少有10个样本才进行判断
            fabs(_average_distance) > 1e-6 && // 防止除以0
            _distance_variance > 1e-6 && // 防止方差为0
            fabs(distance - _average_distance) > fabs(_distance_variance) * 2.0)  // 与平均值的差值大于方差的2.0倍 认为是噪声
        {   // 异常情况
            // 使用sigmoid函数实现平滑的权重计算
            float diff = fabs(distance - _average_distance);
            float relative_error = diff / fabs(_average_distance);  // 相对误差
            // sigmoid函数参数: k控制曲线陡峭程度, x0控制中心点位置
            float k = 3.0f;  // 陡峭程度参数
            float x0 = 1.2f;  // 中心点参数
            // 预测值的权重
            threshold = 0.1f + 0.8f / (1.0f + exp(-k * (relative_error - x0))); 
            meas_coor_smooth.x = (update_coor_previous.x + cos(target_direction / 180.0 * M_PI) * _average_distance) * threshold + 
                                  meas_coor_smooth.x * (1 - threshold);
            meas_coor_smooth.y = (update_coor_previous.y + sin(target_direction / 180.0 * M_PI) * _average_distance) * threshold + 
                                  meas_coor_smooth.y * (1 - threshold);
            // 重新计算修正后的位移
            double distance_amend = sqrt(pow(meas_coor_smooth.x - update_coor_previous.x, 2) + pow(meas_coor_smooth.y - update_coor_previous.y, 2));
            // distance = distance*threshold + distance_amend*(1-threshold);
            #ifdef DATA_k
            ZINFO("kf_smooth_update f_id:%d target_id:%d 修正后的位移:%.2f threshold:%.2f distance:%.2f _average_distance:%.2f _distance_variance:%.2f target_direction:%.2f\n", 
                  fusion_id, target_id, distance_amend, threshold, distance, _average_distance, _distance_variance, target_direction);
            #endif
            distance = distance_amend;
        }
    }

    _distance_buffer.push_back(distance);
    // 保存10帧的移动路程
    if (_distance_buffer.size() > 10)
    {
        _distance_buffer.pop_front();
    }
    // 计算平均移动路程和方差
    float sum_distance = 0;
    for (size_t i = 0; i < _distance_buffer.size(); i++) {
        sum_distance += _distance_buffer[i];
    }
    _average_distance = sum_distance / _distance_buffer.size();
    
    // 计算标准差
    float variance = 0;
    for (size_t i = 0; i < _distance_buffer.size(); i++) {
        variance += pow(_distance_buffer[i] - _average_distance, 2);
    }
    _distance_variance = sqrt(variance / _distance_buffer.size());

    // /*
    // （1）判断移动方位与目标方位的偏差
    auto endpoints = calculate_endpoints(update_coor_previous, target_direction, 100);
    std::vector<CalibPoint> move_rect = calculate_parallel_with_rectangle(endpoints.first.x, endpoints.first.y,
                                                                          endpoints.second.x, endpoints.second.y,
                                                                          50);
    is_retreat_state = judge_point_inrect(move_rect, meas_coor_smooth);
    if (is_retreat_state == false)
    {
        //        ZINFO("kf_smooth_update retreat_judge f_id:%d dv_id:%d target_id:%d is_retreat_state:%d target_direction:%.2f update_coor_previous x:%.2f y:%.2f  meas_coor_smooth x:%.2f y:%.2f \n",
        //              fusion_id, device_id, target_id, is_retreat_state, target_direction, update_coor_previous.x, update_coor_previous.y, meas_coor_smooth.x, meas_coor_smooth.y);
        //        ZINFO("std::vector<CalibPoint> move_rect size():%d \n",move_rect.size());
        //        for(size_t sr = 0; sr < move_rect.size(); sr++)
        //        {
        //            ZINFO("th:%d/%d x:%.2f y:%.2f \n",
        //                  sr, move_rect.size(), move_rect[sr].x, move_rect[sr].y);
        //        }
    }

    // (2) 判断目标位移是否存在快进或快退
    if (speed_smooth_threshold < smooth_min_speed)
    {
        speed_smooth_threshold = smooth_min_speed;
    }
    if (speed_smooth_threshold > smooth_max_speed)
    {
        speed_smooth_threshold = smooth_max_speed;
    }
    float target_is_move = sqrt(pow(meas_coor_mercator.x - update_coor_previous.x, 2) + pow(meas_coor_mercator.y - update_coor_previous.y, 2));
    is_speed_state = target_is_move > smooth_max_speed;
    //    ZINFO("kf_smooth_update f_id:%d target_id:%d distance:%.2f speed_smooth_threshold:%.2f smooth_min_speed:%.2f smooth_max_speed:%.2f is_retreat_state:%d is_speed_state:%d smooth_speed_interval:%.2f\n",
    //          fusion_id, target_id, distance, speed_smooth_threshold, smooth_min_speed, smooth_max_speed, is_retreat_state, is_speed_state, smooth_speed_interval);

    if (is_retreat_state == false) // 如果是倒退的情况
    {
        meas_coor_smooth = update_coor_previous;
        speed_smooth_threshold -= smooth_speed_interval;
    }
    else if (is_speed_state == true) // 如果是快进的情况
    {
        meas_coor_smooth.x = update_coor_previous.x + cos(target_direction / 180.0 * M_PI) * speed_smooth_threshold;
        meas_coor_smooth.y = update_coor_previous.y + sin(target_direction / 180.0 * M_PI) * speed_smooth_threshold;
        speed_smooth_threshold += smooth_speed_interval;
    }
    else
    {
        speed_smooth_threshold -= smooth_speed_interval;
    }
    // */
    // 直接更新
    kf_z << meas_coor_smooth.x, meas_coor_smooth.y;
    kf_update_res << kf_smooth->update_output(H, kf_z);
}

void KalmanSmooth::kf_smooth_init(unsigned int device_id,
                                  unsigned int target_id,
                                  CalibPoint coor_in_radar,
                                  CalibPoint coor_in_mercator,
                                  float x_error,
                                  float y_error)
{
    speed_smooth_threshold = x_error + y_error;

    is_suspicious_lose_count = 0;
    is_suspicious_lose_threshold = 3;
    is_suspicious_lose = false;
    coor_in_radar_previous = coor_in_radar;

    kf_predict_res[0] = coor_in_mercator.x;
    kf_predict_res[1] = coor_in_mercator.y;

    kf_update_res[0] = coor_in_mercator.x;
    kf_update_res[1] = coor_in_mercator.y;

    _distance_buffer.clear();
    _average_distance = 0;
    _distance_variance = 0;
    // meas_coor_previous = coor_in_mercator;

    kf_x << coor_in_mercator.x, coor_in_mercator.y, 0, 0;
    kf_smooth->init(kf_x, P, R, Q);
}

/*
 * @Name: calculate_endpoints
 * @Description: 根据点生成矩形区域
 *
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/11/22
 * Time: 14:10
 * Author: WangXing
 * Content: Create
 */
std::pair<CalibPoint, CalibPoint> KalmanSmooth::calculate_endpoints(CalibPoint centerPoint, double angle, double length)
{
    double radians = angle / 180.0 * M_PI; // 将角度转换为弧度

    double x2 = centerPoint.x + length * std::cos(radians);
    double y2 = centerPoint.y + length * std::sin(radians);
    double x3 = centerPoint.x;
    double y3 = centerPoint.y;

    // 使用round函数进行四舍五入，注意C++中的round函数返回double类型
    // 但我们可以将其转换为int类型后再赋值给Point结构体的成员，这里为了模拟Python的行为，我们保留小数位
    CalibPoint p2 = {std::round(x2 * 10.0) / 10.0, std::round(y2 * 10.0) / 10.0}; // 保留一位小数
    CalibPoint p3 = {std::round(x3 * 10.0) / 10.0, std::round(y3 * 10.0) / 10.0}; // 保留一位小数

    return std::make_pair(p2, p3);
}

/*
 * @Name:calculate_parallel_with_rectangle
 * @Description:生成矩形区域
 *
 * @Input
 * 无
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/11/22
 * Time: 14:15
 * Author: WangXing
 * Content: Create
 */
std::vector<CalibPoint> KalmanSmooth::calculate_parallel_with_rectangle(double x1, double y1, double x2, double y2, double offset)
{
    // 计算线段A的方向角
    double dx = x2 - x1;
    double dy = y2 - y1;
    double angle = atan2(dy, dx);

    // 计算垂直方向的单位向量
    double perp_dx = -sin(angle);
    double perp_dy = cos(angle);

    // 偏移后线段的端点
    float line1_start_x = x1 + offset * perp_dx;
    float line1_start_y = y1 + offset * perp_dy;
    float line1_end_x = x2 + offset * perp_dx;
    float line1_end_y = y2 + offset * perp_dy;

    float line2_start_x = x1 - offset * perp_dx;
    float line2_start_y = y1 - offset * perp_dy;
    float line2_end_x = x2 - offset * perp_dx;
    float line2_end_y = y2 - offset * perp_dy;

    // 矩形顶点
    std::vector<CalibPoint> rectangle_points = {
        {line1_start_x, line1_start_y},
        {line1_end_x, line1_end_y},
        {line2_end_x, line2_end_y},
        {line2_start_x, line2_start_y}};

    // 返回矩形顶点
    return rectangle_points;
}

bool KalmanSmooth::judge_point_inrect(std::vector<CalibPoint> polygon, CalibPoint point)
{
    std::vector<cv::Point> cv_polygon;
    for (size_t py = 0; py < polygon.size(); py++)
    {
        cv::Point polygon_point =
            {
                polygon[py].x,
                polygon[py].y};
        cv_polygon.push_back(polygon_point);
    }
    cv::Point cv_point = {
        point.x,
        point.y};
    double result = cv::pointPolygonTest(cv_polygon, cv_point, false);
    // 判断结果
    if (result > 0)
    {
        // std::cout << "点在多边形内部" << std::endl;
        return true;
    }
    else if (result == 0)
    {
        // std::cout << "点在多边形边界上" << std::endl;
        return true;
    }
    else
    {
        // std::cout << "点在多边形外部" << std::endl;
        return false;
    }
}

/*
 * @Name: update_speed_from_other_filter
 * @Description: 根据另一个滤波器的状态更新当前滤波器的速度状态
 *
 * @Input
 * other_vx: 其他滤波器的x方向速度
 * other_vy: 其他滤波器的y方向速度
 * weight: 其他滤波器速度的权重 (0.0-1.0)
 * update_covariance: 是否同时更新协方差矩阵
 *
 * @Output
 * 无
 *
 * @Edit History
 * Date: 2024/XX/XX
 * Time: XX:XX
 * Author: XXX
 * Content: Create
 */
void KalmanSmooth::update_speed_from_other_filter(float other_vx, 
                                                 float other_vy, 
                                                 float weight,
                                                 bool update_covariance,
                                                 bool is_predict)
{
    // 添加向量大小验证
    if (kf_smooth == nullptr) {
        ZERROR("滤波器指针为空，无法更新速度\n");
        return;
    }
    
    // 验证向量维度
    if (kf_smooth->x.size() < 4 || kf_update_res.size() < 4 || kf_predict_res.size() < 4) {
        ZERROR("状态向量维度不足，无法更新速度。kf_smooth->x.size()=%ld, kf_update_res.size()=%ld, kf_predict_res.size()=%ld\n",
              kf_smooth->x.size(), kf_update_res.size(), kf_predict_res.size());
        return;
    }
    
    // 验证权重值的合法性
    if (weight < 0.0f) weight = 0.0f;
    if (weight > 1.0f) weight = 1.0f;
    // 获取当前滤波器的速度
    float current_vx = kf_smooth->x[2]; 
    float current_vy = kf_smooth->x[3]; 
    
    // 计算融合后的速度
    float fused_vx = current_vx * (1.0f - weight) + other_vx * weight;
    float fused_vy = current_vy * (1.0f - weight) + other_vy * weight;
    
    // 更新当前滤波器的速度状态
    kf_smooth->x[2] = fused_vx;
    kf_smooth->x[3] = fused_vy;
    
    // 同步更新输出结果向量
    kf_update_res[2] = fused_vx;
    kf_update_res[3] = fused_vy;
    
    // 更新协方差矩阵以反映速度状态的变化
    if (update_covariance) {
        if (kf_smooth->P.rows() >= 4 && kf_smooth->P.cols() >= 4) {
            float reduction_factor = 1.0f - 0.5f * weight;
            kf_smooth->P(2, 2) *= reduction_factor;
            kf_smooth->P(3, 3) *= reduction_factor;
            
            // 确保协方差矩阵保持对称正定
            kf_smooth->P(0, 2) = kf_smooth->P(2, 0);
            kf_smooth->P(1, 3) = kf_smooth->P(3, 1);
            kf_smooth->P(0, 3) = kf_smooth->P(3, 0);
            kf_smooth->P(1, 2) = kf_smooth->P(2, 1);
        } else {
            ZERROR("协方差矩阵维度不足，无法更新. P.rows()=%ld, P.cols()=%ld\n", 
                  kf_smooth->P.rows(), kf_smooth->P.cols());
        }
    }
    
    if (is_predict) {
        // 安全地重新预测状态
        try {
            kf_predict_res = kf_smooth->predict(A);
            
            // 获取时间步长并更新位置
            float dt = 0.0f;
            if (A.rows() >= 3 && A.cols() >= 3) {
                dt = A(0, 2); // A矩阵的(0,2)元素是时间步长
                if (fabs(dt) > 1e-6) { // 防止除以接近0的值
                    // 基于新速度更新位置预测
                    kf_update_res(0) += (fused_vx - current_vx) * dt;
                    kf_update_res(1) += (fused_vy - current_vy) * dt;
                }
            }
        } catch (const std::exception& e) {
            ZERROR("预测状态时发生异常: %s\n", e.what());
        }
    }
    
    #ifdef DEBUG_KALMAN_SMOOTH
    ZDEBUG("update_speed_from_other_filter - Other speed: (%.2f, %.2f), Current speed: (%.2f, %.2f), Fused speed: (%.2f, %.2f), Weight: %.2f\n", 
           other_vx, other_vy, current_vx, current_vy, fused_vx, fused_vy, weight);
    #endif
}

float KalmanSmooth::get_dt()
{
    if (A.rows() >= 3 && A.cols() >= 3)
    {
        return A(0, 2);
    }
    else
    {
        return 0.0f;    
    }
}