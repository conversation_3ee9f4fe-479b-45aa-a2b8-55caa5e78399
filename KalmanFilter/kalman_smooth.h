#pragma once
#include "kalman_filter.h"
#include "tecu1000_algorithm_header.h"
#include <vector>
#include <opencv2/opencv.hpp>            // C++
#include <opencv2/highgui/highgui_c.h>   // C
#include <opencv2/imgproc/imgproc_c.h>   // C
// #define DATA_k
// #define DEBUG_KALMAN_SMOOTH
using namespace std;
using namespace tecu1000_alg;

namespace tecu_r1000_0_algorithm
{
	class KalmanSmooth
	{
	public:
		KalmanSmooth(int state_size,int meas_size,int control_size, float t, float rs_rate, float qs_rate);
		~KalmanSmooth();

		void kf_smooth_init(unsigned int device_id,
                            unsigned int target_id,
                            CalibPoint coor_in_radar,
                            CalibPoint coor_in_mercator,
                            float x_error,
                            float y_error);

		void kf_smooth_predict(int fusion_id,
                               unsigned int device_id,
                               unsigned int target_id,
                               CalibPoint coor_in_radar,
                               CalibPoint coor_in_mercator,
                               float meas_speed,
                               bool is_lose,
                               double target_direction);

        void kf_smooth_update(int fusion_id,
                              unsigned int device_id,
                              unsigned int target_id,
                              CalibPoint coor_in_mercator,
                              float meas_speed,
                              float smooth_min_speed,
                              float smooth_max_speed,
                              float smooth_speed_interval,
                              bool is_lose,
                              double target_direction,
                              bool use_input_noise_filter);

        /*
        * @Name: update_speed_from_other_filter
        * @Description: 根据另一个滤波器的状态更新当前滤波器的速度状态
        *
        * @Input
        * other_vx: 其他滤波器的x方向速度
        * other_vy: 其他滤波器的y方向速度
        * weight: 其他滤波器速度的权重 (0.0-1.0)
        * update_covariance: 是否同时更新协方差矩阵
        *
        * @Output
        * 无
        *
        * @Edit History
        * Date: 2024/XX/XX
        * Time: XX:XX
        * Author: XXX
        * Content: Create
        */
        void update_speed_from_other_filter(float other_vx, 
                                        float other_vy, 
                                        float weight = 0.5f,
                                        bool update_covariance = true,
                                        bool is_predict = true);
        
		Eigen::VectorXd kf_update_res;

		Eigen::VectorXd kf_predict_res;

        bool is_predict_state;
        bool is_retreat_state;
        bool is_speed_state;

        float speed_smooth_threshold;        //快进的平滑阈值
        float get_dt();

    private:

        CalibPoint coor_in_radar_previous;
        int is_suspicious_lose_count;
        int is_suspicious_lose_threshold;
        bool is_suspicious_lose;

        // 轨迹平滑
        // 保存历史5帧的位移
        std::deque<float> _distance_buffer;  // 存储时间戳和移动路程的队列
        float _average_distance;                               // 1s内的平均移动路程  
        float _distance_variance;                               // 1s内的方差
        // CalibPoint meas_coor_previous;     // 上一帧的测量值

		Eigen::MatrixXd A;			 //A矩阵
		Eigen::MatrixXd H;           //H矩阵
		Eigen::MatrixXd P;			 //P矩阵
		Eigen::MatrixXd R;			 //测量噪声矩阵
		Eigen::MatrixXd Q;           //过程噪声矩阵
		Eigen::MatrixXd R_smooth;    //测量噪声平滑矩阵
		Eigen::MatrixXd Q_smooth;    //过程噪声平滑矩阵
		Eigen::VectorXd kf_x;
		Eigen::VectorXd kf_z;


		KalmanFilter *kf_smooth = nullptr;
        std::pair<CalibPoint , CalibPoint> calculate_endpoints(CalibPoint centerPoint, double angle, double length);
        std::vector<CalibPoint> calculate_parallel_with_rectangle(double x1, double y1, double x2, double y2, double offset);
        bool judge_point_inrect(std::vector<CalibPoint> polygon, CalibPoint point);
	};
}



