#!/bin/bash

cp CMakeLists_310B.txt CMakeLists.txt

# ARM交叉编译设置
CROSS_COMPILE=aarch64-linux-gnu-
CMAKE_TOOLCHAIN=aarch-64-linux.cmake

# 参数检查
REBUILD=0
if [ "$1" == "-r" ] || [ "$1" == "--rebuild" ]; then
    REBUILD=1
fi

# 设置Ascend相关环境变量
export DDK_PATH=/usr/local/Ascend/ascend-toolkit/latest
export NPU_HOST_LIB=/usr/local/Ascend/ascend-toolkit/latest/aarch64-linux/runtime/lib64

# 根据参数决定是否重新构建
if [ $REBUILD -eq 1 ]; then
    echo "重新生成build目录..."
    mkdir -p build
    rm -rf build/*
    cd build
    
    # 调用CMake配置
    echo "开始CMake配置..."
    cmake -DCMAKE_TOOLCHAIN_FILE=${CMAKE_TOOLCHAIN} -DARM_CROSS_COMPILE=${CROSS_COMPILE} -DCMAKE_SYSTEM_PROCESSOR=aarch64 ..
else
    # 不重新构建，直接进入build目录
    mkdir -p build
    cd build
fi

echo "开始编译..."
make -j12

if [ $? -eq 0 ]; then
    echo "编译成功！"
else
    echo "编译失败，请检查错误信息"
fi

chmod 777 libtecu_1000_0.so
cp libtecu_1000_0.so ../../tecu1000_0_libs/
cp libtecu_1000_0.so ../../tecu1000_0_testdir/